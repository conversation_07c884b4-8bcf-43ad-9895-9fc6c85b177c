#!/usr/bin/env node
/**
 * -----------------------------------------------------------------------------------
 *  HDSD:
 *    node scripts/MSBTemplate.mjs <ModuleName> <FunctionName> [--noparam] [--noState]
 *
 *  Ví dụ:
 *    yarn gen:msbtemplate ContactManager GetAll --noparam --noState
 *    hoặc:
 *    node scripts/MSBTemplate.mjs MasterData GetBranch --noparam --noState
 *
 * Target:
 * - Tự động tạo/chèn method vào:
 *   1) I[ModuleName]DataSource.ts (interface)
 *   2) [ModuleName]RemoteDataSource.ts (class)
 *   3) [ModuleName]MockDataSource.ts (class)
 *   4) I[ModuleName]Repository.ts (interface)
 *   5) [ModuleName]Repository.ts (class)
 *   6) Tạo UseCase: src/domain/usecases/[module-name-kebab]/[FunctionNamePascal]UseCase.ts
 *   7) Tạo Model: [FunctionNamePascal]Request.ts, [FunctionNamePascal]Response.ts
 *      trong src/data/models/[function-name-kebab]
 *   8) Tạo Entity: [FunctionNamePascal]State.ts (enum), [FunctionNamePascal]DTO.ts
 *
 * - Thư mục => kebab-case (vd "get-branch")
 * - File => PascalCase (vd "GetBranchResponse.ts"), KHÔNG hạ thành "GetbranchResponse".
 * - Method => camelCase (vd "getBranch()")
 * - `--noparam` => không có request param.
 * - `--noState` => sử dụng state mặc định
 *
 *
 * -----------------------------------------------------------------------------------
 */

import {Project} from 'ts-morph';
import fs from 'fs';
import path from 'path';
import url from 'url';
import {execSync} from 'child_process';

// =========================== UTILS ===========================

/**
 * "GetBranch" => "getBranch"
 */
function toCamelCase(str) {
  if (!str) {
    return str;
  }
  const first = str.charAt(0).toLowerCase();
  const rest = str.slice(1);
  return first + rest;
}

/**
 * Mới: Tách theo dấu gạch dưới, gạch ngang, khoảng trắng,
 * rồi chỉ viết hoa chữ cái đầu mỗi token, giữ nguyên phần còn lại.
 *
 * "GetBranch" => ["GetBranch"] => "GetBranch"
 * "get_branch" => ["get", "branch"] => "Get" + "Branch" => "GetBranch"
 * "Get_branch" => ["Get", "branch"] => "Get" + "Branch" => "GetBranch"
 */
function toPascalCase(str) {
  return str
    .split(/[_-\s]+/)
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join('');
}

/**
 * "GetBranch" => "get-branch"
 */
function toKebabCase(str) {
  return str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`).replace(/^-/, '');
}

/** Tạo file skeleton nếu chưa có, trả về sourceFile. */
function loadOrCreateSourceFile(project, filePath, skeletonContent) {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, skeletonContent.trim() + '\n');
    console.log(`[CREATE] Skeleton => ${filePath}`);
  }
  return project.addSourceFileAtPath(filePath);
}

// =========================== AST HELPER ===========================

function addMethodToDataSourceInterface(sourceFile, interfaceName, methodName, hasNoParam, functionNamePascal) {
  let iface = sourceFile.getInterface(interfaceName);
  if (!iface) {
    iface = sourceFile.addInterface({
      name: interfaceName,
      isExported: true,
    });
    console.log(`[ADD] Created interface ${interfaceName} in ${sourceFile.getFilePath()}`);
  }

  const exist = iface.getMethods().find(m => m.getName() === methodName);
  if (exist) {
    console.log(`[SKIP] Interface ${interfaceName} đã có method "${methodName}"`);
    return;
  }

  if (hasNoParam) {
    iface.addMethod({
      name: methodName,
      returnType: `Promise<BaseResponse<${functionNamePascal}Response>>`,
    });
  } else {
    iface.addMethod({
      name: methodName,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType: `Promise<BaseResponse<${functionNamePascal}Response>>`,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into interface ${interfaceName}`);
}

function addMethodToRepositoryInterface(sourceFile, interfaceName, methodName, hasNoParam, functionNamePascal) {
  let iface = sourceFile.getInterface(interfaceName);
  if (!iface) {
    iface = sourceFile.addInterface({
      name: interfaceName,
      isExported: true,
    });
    console.log(`[ADD] Created interface ${interfaceName} in ${sourceFile.getFilePath()}`);
  }

  const exist = iface.getMethods().find(m => m.getName() === methodName);
  if (exist) {
    console.log(`[SKIP] Interface ${interfaceName} đã có method "${methodName}"`);
    return;
  }

  if (hasNoParam) {
    iface.addMethod({
      name: methodName,
      returnType: `Promise<BaseResponse<${functionNamePascal}Model>>`,
    });
  } else {
    iface.addMethod({
      name: methodName,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType: `Promise<BaseResponse<${functionNamePascal}Model>>`,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into interface ${interfaceName}`);
}

function addMethodToMockDataSourceClass(sourceFile, className, methodName, hasNoParam, functionNamePascal) {
  let cls = sourceFile.getClass(className);
  if (!cls) {
    cls = sourceFile.addClass({
      name: className,
      isExported: true,
    });
    console.log(`[ADD] Created class ${className} in ${sourceFile.getFilePath()}`);
  }

  const existMethod = cls.getInstanceMethod(methodName);
  if (existMethod) {
    console.log(`[SKIP] Class ${className} đã có method "${methodName}"`);
    return;
  }

  if (hasNoParam) {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      returnType: `Promise<BaseResponse<${functionNamePascal}Response>>`,
      statements: `
        // TODO: Implement
        return {};
      `,
    });
  } else {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType: `Promise<BaseResponse<${functionNamePascal}Response>>`,
      statements: `
        // TODO: Implement
        return {};
      `,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into class ${className}`);
}

function addMethodToRepositoryClass(sourceFile, className, methodName, hasNoParam, functionNamePascal) {
  let cls = sourceFile.getClass(className);
  if (!cls) {
    cls = sourceFile.addClass({
      name: className,
      isExported: true,
    });
    console.log(`[ADD] Created class ${className} in ${sourceFile.getFilePath()}`);
  }

  const existMethod = cls.getInstanceMethod(methodName);
  if (existMethod) {
    console.log(`[SKIP] Class ${className} đã có method "${methodName}"`);
    return;
  }

  if (hasNoParam) {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      returnType: `Promise<BaseResponse<${functionNamePascal}Model>>`,
      statements: `
        return handleData<${functionNamePascal}Model>(this.remoteDataSource.${methodName}(), map${functionNamePascal}ResponseToModel);
      `,
    });
  } else {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType: `Promise<BaseResponse<${functionNamePascal}Model>>`,
      statements: `
        return handleData<${functionNamePascal}Model>(this.remoteDataSource.${methodName}(request), map${functionNamePascal}ResponseToModel);
      `,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into class ${className}`);
}

// add method to Datasource
function addMethodToDataSourceClass(sourceFile, className, methodName, hasNoParam, functionNamePascal, rawModuleName) {
  let cls = sourceFile.getClass(className);
  if (!cls) {
    cls = sourceFile.addClass({
      name: className,
      isExported: true,
    });
    console.log(`[ADD] Created class ${className} in ${sourceFile.getFilePath()}`);
  }

  const existMethod = cls.getInstanceMethod(methodName);
  if (existMethod) {
    console.log(`[SKIP] Class ${className} đã có method "${methodName}"`);
    return;
  }
  const domainName = toCamelCase(rawModuleName);

  if (hasNoParam) {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      returnType: `Promise<BaseResponse<${functionNamePascal}Response>>`,
      statements: `
        try {
          const url = PathResolver.${domainName}.${methodName}();
          const response = await this.httpClient.post(url, body);
          return handleResponse(response);
        } catch (error: any) {
          if (error instanceof ApiError) throw error;
          throw new NetworkError('503', 'Cannot to connect server', error);
        }
      `,
    });
  } else {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType: `Promise<BaseResponse<${functionNamePascal}Response>>`,
      statements: `
        try {
          const url = PathResolver.${domainName}.${methodName}();
          const response = await this.httpClient.post(url, request);
          return handleResponse(response);
        } catch (error: any) {
          if (error instanceof ApiError) throw error;
          throw new NetworkError('503', 'Cannot to connect server', error);
        }
      `,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into class ${className}`);
}

// add method to Repository

/**
 * Tạo file UseCase nếu chưa, chèn import, skeleton
 */
function createUseCaseFile(
  filePath,
  project,
  moduleNameExact,
  functionNamePascal,
  methodName,
  hasNoParam,
  hasNoState,
  functionNameKebab,
) {
  if (fs.existsSync(filePath)) {
    console.log(`[SKIP] UseCase file đã tồn tại: ${filePath}`);
    return;
  }

  const skeleton = `export class ${functionNamePascal}UseCase {
  private repository: I${moduleNameExact}Repository;

  constructor(repository: I${moduleNameExact}Repository) {
    this.repository = repository;
  }

  public async execute${hasNoParam ? '()' : `(request: ${functionNamePascal}Request)`}: Promise<${
    hasNoState ? `ResultState<${functionNamePascal}Model>` : `${functionNamePascal}State`
  }> {
    // call this.repository.${methodName}(...)
    ${
      hasNoState
        ? `return ExecutionHandler.execute(() => this.repository.${methodName}(${hasNoParam ? '' : 'request'}));`
        : ` //TODO: implement state
        return Promise.resolve({status: 'idle'}); // Placeholder implementation`
    }
  }
}
`;
  fs.writeFileSync(filePath, skeleton.trim() + '\n');
  console.log(`[CREATE] UseCase => ${filePath}`);

  const sf = project.addSourceFileAtPath(filePath);
  // import
  sf.insertStatements(0, [
    `import {I${moduleNameExact}Repository} from '../../repositories/I${moduleNameExact}Repository';`,
    `import {${functionNamePascal}Model} from '../../entities/${functionNameKebab}/${functionNamePascal}Model';`,
    hasNoState
      ? "import {ResultState} from '../../../core/ResultState';"
      : `import { ${functionNamePascal}State } from "../../states/${functionNameKebab}/${functionNamePascal}State";`,
    "import {ExecutionHandler} from '../../../utils/ExcecutionHandler';",
    ...(hasNoParam
      ? []
      : [
          `import {${functionNamePascal}Request} from '../../../data/models/${functionNameKebab}/${functionNamePascal}Request';`,
        ]),
  ]);

  sf.saveSync();
}

/**
 * Tạo Request/Response model
 */
function createModelFiles(dirPath, project, functionNamePascal, functionNameSnake, hasNoParam) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, {recursive: true});
  }

  // Request
  if (!hasNoParam) {
    const reqPath = path.join(dirPath, `${functionNamePascal}Request.ts`);
    if (!fs.existsSync(reqPath)) {
      fs.writeFileSync(reqPath, `export interface ${functionNamePascal}Request {\n  // TODO: define fields\n}\n`);
      console.log(`[CREATE] => ${reqPath}`);
      project.addSourceFileAtPath(reqPath);
    }
  }

  // Response
  const resPath = path.join(dirPath, `${functionNamePascal}Response.ts`);
  if (!fs.existsSync(resPath)) {
    fs.writeFileSync(resPath, `export interface ${functionNamePascal}Response {\n  // TODO: define fields\n}\n`);
    console.log(`[CREATE] => ${resPath}`);
    project.addSourceFileAtPath(resPath);
  }

  // generateFromOpenAPI(methodName);
}


// Merge schemas into a single schema
function mergeSchemas(target, source, api) {
  if (source.$ref) {
    // Resolve schema if it contains a $ref
    const refKey = source.$ref.replace('#/components/schemas/', '');
    const resolved = api.components?.schemas?.[refKey]; // Safe check for components.schemas
    if (resolved) {
      source = resolved; // Replace source with resolved schema
    } else {
      console.warn(`⚠️ Unable to resolve $ref: ${refKey}`);
    }
  }

  // Merge properties from source into target schema
  for (const [key, value] of Object.entries(source.properties || {})) {
    if (target.properties[key]) {
      target.properties[key] = {...target.properties[key], ...value};
    } else {
      target.properties[key] = value;
    }
  }
  return target;
}

// =========================== MAIN ===========================

async function main() {
  const args = process.argv.slice(2);
  if (args.length < 2) {
    console.log('Cách dùng: node scripts/MSBTemplate.mjs <ModuleName> <FunctionName> [--noparam]');
    process.exit(1);
  }

  const rawModuleName = args[0]; // "MasterData"
  const rawFunctionName = args[1]; // "GetBranch"
  const hasNoParam = args.includes('--noparam');

  const hasNoState = args.includes('--noState');

  // Tên method => "getBranch"
  const methodName = toCamelCase(rawFunctionName);
  // Tên function => "GetBranch" (PascalCase) (KHÔNG hạ "Branch" thành "branch")
  const functionNamePascal = toPascalCase(rawFunctionName);
  // Tên thư mục => kebab-case => "get-branch"
  const functionNameKebab = toKebabCase(rawFunctionName);

  // Giữ nguyên tên module => moduleNameExact
  const moduleNameExact = rawModuleName; // "MasterData"

  // Tạo AST project
  const project = new Project();

  // Lấy đường dẫn gốc
  const scriptDir = path.dirname(url.fileURLToPath(import.meta.url));
  const rootDir = path.join(scriptDir, '..');

  // 1) I[ModuleName]DataSource
  const iDataSourceName = `I${moduleNameExact}DataSource`;
  const dataSourceDir = path.join(rootDir, 'src', 'data', 'datasources');
  fs.mkdirSync(dataSourceDir, {recursive: true});
  const iDataSourceFilePath = path.join(dataSourceDir, `${iDataSourceName}.ts`);

  // [ModuleName]RemoteDataSource
  const remoteName = `${moduleNameExact}RemoteDataSource`;
  const remoteDir = path.join(dataSourceDir, 'remote');
  fs.mkdirSync(remoteDir, {recursive: true});
  const remoteFilePath = path.join(remoteDir, `${remoteName}.ts`);

  // [ModuleName]MockDataSource
  const mockName = `${moduleNameExact}MockDataSource`;
  const mockDir = path.join(dataSourceDir, 'mock');
  fs.mkdirSync(mockDir, {recursive: true});
  const mockFilePath = path.join(mockDir, `${mockName}.ts`);

  // 2) I[ModuleName]Repository
  const iRepoName = `I${moduleNameExact}Repository`;
  const domainRepoDir = path.join(rootDir, 'src', 'domain', 'repositories');
  fs.mkdirSync(domainRepoDir, {recursive: true});
  const iRepoFilePath = path.join(domainRepoDir, `${iRepoName}.ts`);

  // [ModuleName]Repository
  const repoName = `${moduleNameExact}Repository`;
  const dataRepoDir = path.join(rootDir, 'src', 'data', 'repositories');
  fs.mkdirSync(dataRepoDir, {recursive: true});
  const repoFilePath = path.join(dataRepoDir, `${repoName}.ts`);

  // 3) UseCase => domain/usecases/[module_name_snake]
  const usecaseDir = path.join(rootDir, 'src', 'domain', 'usecases', toKebabCase(moduleNameExact));
  fs.mkdirSync(usecaseDir, {recursive: true});

  // 4) Model => data/models/[function_name_snake]

  // 5) Entity => domain/entity/[function_name_snake]

  // ===== MODEL =====
  const modelDir = path.join(rootDir, 'src', 'domain', 'entities', functionNameKebab);
  fs.mkdirSync(modelDir, {recursive: true});
  const modelPath = path.join(modelDir, `${functionNamePascal}Model.ts`);
  if (!fs.existsSync(modelPath)) {
    fs.writeFileSync(modelPath, `export class ${functionNamePascal}Model {\n  // TODO: define fields\n}\n`);
    console.log(`[CREATE] => ${modelPath}`);
    project.addSourceFileAtPath(modelPath);
  }

  // ===== MAPPER =====
  const mapperDir = path.join(rootDir, 'src', 'data', 'mappers', functionNameKebab);
  fs.mkdirSync(mapperDir, {recursive: true});
  const mapperPath = path.join(mapperDir, `${functionNamePascal}Mapper.ts`);
  if (!fs.existsSync(mapperPath)) {
    const mapperContent = `
    import { ${functionNamePascal}Response } from '../../models/${functionNameKebab}/${functionNamePascal}Response';
    import { ${functionNamePascal}Model } from '../../../domain/entities/${functionNameKebab}/${functionNamePascal}Model';

    export function map${functionNamePascal}ResponseToModel(response: ${functionNamePascal}Response): ${functionNamePascal}Model {
      return new ${functionNamePascal}Model();
    }
    `;
    fs.writeFileSync(mapperPath, mapperContent.trim() + '\n');
    console.log(`[CREATE] => ${mapperPath}`);
    project.addSourceFileAtPath(mapperPath);
  }

  // ============ 1) I[ModuleName]DataSource =============
  {
    const skeleton = `export interface ${iDataSourceName} {\n}\n`;
    const sf = loadOrCreateSourceFile(project, iDataSourceFilePath, skeleton);

    // import
    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Request`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../models/${functionNameKebab}/${functionNamePascal}Request`,
          namedImports: [`${functionNamePascal}Request`],
        });
      }
    }

    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Response`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../models/${functionNameKebab}/${functionNamePascal}Response`,
          namedImports: [`${functionNamePascal}Response`],
        });
      }
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('BaseResponse'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../core/BaseResponse',
        namedImports: ['BaseResponse'],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Response`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../models/${functionNameKebab}/${functionNamePascal}Response`,
        namedImports: [`${functionNamePascal}Response`],
      });
    }

    addMethodToDataSourceInterface(sf, iDataSourceName, methodName, hasNoParam, functionNamePascal);
    sf.saveSync();
  }

  // ============ 2) [ModuleName]RemoteDataSource ============
  {
    const skeleton = `export class ${remoteName} implements ${iDataSourceName} {\n  constructor(private httpClient: IHttpClient) {}\n}\n`;
    const sf = loadOrCreateSourceFile(project, remoteFilePath, skeleton);

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(iDataSourceName))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../${iDataSourceName}`,
        namedImports: [iDataSourceName],
      });
    }
    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Request`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../../models/${functionNameKebab}/${functionNamePascal}Request`,
          namedImports: [`${functionNamePascal}Request`],
        });
      }
    }

    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Response`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../../models/${functionNameKebab}/${functionNamePascal}Response`,
          namedImports: [`${functionNamePascal}Response`],
        });
      }
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('ResponseHandler'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../../utils/ResponseHandler',
        namedImports: ['handleResponse'],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('msb-host-shared-module'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: 'msb-host-shared-module',
        namedImports: ['IHttpClient'],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('MSBCustomError'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../../core/MSBCustomError',
        namedImports: ['ApiError', 'NetworkError'],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('PathResolver'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../../utils/PathResolver',
        namedImports: ['PathResolver'],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('BaseResponse'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../../core/BaseResponse',
        namedImports: ['BaseResponse'],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Response`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../../models/${functionNameKebab}/${functionNamePascal}Response`,
        namedImports: [`${functionNamePascal}Response`],
      });
    }

    addMethodToDataSourceClass(sf, remoteName, methodName, hasNoParam, functionNamePascal, rawModuleName);
    sf.saveSync();
  }

  // ============ 3) [ModuleName]MockDataSource ============
  {
    const skeleton = `export class ${mockName} implements ${iDataSourceName} {\n  constructor() {}\n}\n`;
    const sf = loadOrCreateSourceFile(project, mockFilePath, skeleton);

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(iDataSourceName))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../${iDataSourceName}`,
        namedImports: [iDataSourceName],
      });
    }
    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Request`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../../models/${functionNameKebab}/${functionNamePascal}Request`,
          namedImports: [`${functionNamePascal}Request`],
        });
      }
    }

    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Response`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../../models/${functionNameKebab}/${functionNamePascal}Response`,
          namedImports: [`${functionNamePascal}Response`],
        });
      }
    }

    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('BaseResponse'))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: '../../../core/BaseResponse',
          namedImports: ['BaseResponse'],
        });
      }
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Response`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../../models/${functionNameKebab}/${functionNamePascal}Response`,
        namedImports: [`${functionNamePascal}Response`],
      });
    }

    addMethodToMockDataSourceClass(sf, mockName, methodName, hasNoParam, functionNamePascal);
    sf.saveSync();
  }

  // ============ 4) I[ModuleName]Repository ============
  {
    const iRepoSkeleton = `export interface ${iRepoName} {\n}\n`;
    const sf = loadOrCreateSourceFile(project, iRepoFilePath, iRepoSkeleton);

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('BaseResponse'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../core/BaseResponse',
        namedImports: ['BaseResponse'],
      });
    }
    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Request`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../../data/models/${functionNameKebab}/${functionNamePascal}Request`,
          namedImports: [`${functionNamePascal}Request`],
        });
      }
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Model`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../entities/${functionNameKebab}/${functionNamePascal}Model`,
        namedImports: [`${functionNamePascal}Model`],
      });
    }

    addMethodToRepositoryInterface(sf, iRepoName, methodName, hasNoParam, functionNamePascal);

    // Sửa returnType => Promise<BaseResponse<xxxResponse>>
    // const iface = sf.getInterface(iRepoName);
    // const methodDec = iface.getMethod(methodName);
    // if (methodDec) {
    //   methodDec.setReturnType(`Promise<BaseResponse<${functionNamePascal}Response>>`);
    // }

    sf.saveSync();
  }

  // ============ 5) [ModuleName]Repository ============
  {
    const repoSkeleton = `export class ${repoName} implements ${iRepoName} {
  private remoteDataSource: ${iDataSourceName};

  constructor(remoteDataSource: ${iDataSourceName}) {
    this.remoteDataSource = remoteDataSource;
  }
}
`;
    const sf = loadOrCreateSourceFile(project, repoFilePath, repoSkeleton);

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(iRepoName))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../../domain/repositories/${iRepoName}`,
        namedImports: [iRepoName],
      });
    }
    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('BaseResponse'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../core/BaseResponse',
        namedImports: ['BaseResponse'],
      });
    }
    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(iDataSourceName))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../datasources/${iDataSourceName}`,
        namedImports: [iDataSourceName],
      });
    }
    if (!hasNoParam) {
      if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Request`))) {
        sf.insertImportDeclaration(0, {
          moduleSpecifier: `../models/${functionNameKebab}/${functionNamePascal}Request`,
          namedImports: [`${functionNamePascal}Request`],
        });
      }
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Model`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../../domain/entities/${functionNameKebab}/${functionNamePascal}Model`,
        namedImports: [`${functionNamePascal}Model`],
      });
    }

    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('HandleData'))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: '../../utils/HandleData',
        namedImports: ['handleData'],
      });
    }

    if (
      !sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`map${functionNamePascal}ResponseToModel`))
    ) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `../mappers/${functionNameKebab}/${functionNamePascal}Mapper`,
        namedImports: [`map${functionNamePascal}ResponseToModel`],
      });
    }

    addMethodToRepositoryClass(sf, repoName, methodName, hasNoParam, functionNamePascal);
    sf.saveSync();
  }

  // ============ 6) UseCase ============
  {
    const usecaseDir = path.join(rootDir, 'src', 'domain', 'usecases', toKebabCase(moduleNameExact));
    fs.mkdirSync(usecaseDir, {recursive: true});
    const useCaseFilePath = path.join(usecaseDir, `${functionNamePascal}UseCase.ts`);

    createUseCaseFile(
      useCaseFilePath,
      project,
      moduleNameExact,
      functionNamePascal,
      methodName,
      hasNoParam,
      hasNoState,
      functionNameKebab,
    );
  }

  // ============ 6.1) MockUseCase ============
  {
    const usecaseDir = path.join(rootDir, 'src', 'domain', 'usecases', toKebabCase(moduleNameExact));
    const mockUseCaseFilePath = path.join(usecaseDir, `${functionNamePascal}MockUseCase.ts`);

    if (!fs.existsSync(mockUseCaseFilePath)) {
      const mockUseCaseContent = `
        export class ${functionNamePascal}MockUseCase {
          public async execute${hasNoParam ? '()' : `(request: ${functionNamePascal}Request)`}: Promise<${
            hasNoState ? `ResultState<${functionNamePascal}Model>` : `${functionNamePascal}State`
          }> {
            // TODO: return mock data
            return {
                  status: 'INIT',
                };
          }
        }
    `;

      fs.writeFileSync(mockUseCaseFilePath, mockUseCaseContent.trim() + '\n');
      console.log(`[CREATE] MockUseCase => ${mockUseCaseFilePath}`);

      const sf = project.addSourceFileAtPath(mockUseCaseFilePath);

      // Import response, request, DTO
      sf.insertStatements(0, [
        ...(hasNoParam
          ? []
          : [
              `import { ${functionNamePascal}Request } from '../../../data/models/${functionNameKebab}/${functionNamePascal}Request';`,
            ]),
        hasNoState
          ? "import {ResultState} from '../../../core/ResultState';"
          : `import { ${functionNamePascal}State } from '../../states/${functionNameKebab}/${functionNamePascal}State';`,
        `import { ${functionNamePascal}Model } from '../../entities/${functionNameKebab}/${functionNamePascal}Model';`,
      ]);

      sf.saveSync();
    } else {
      console.log(`[SKIP] MockUseCase đã tồn tại: ${mockUseCaseFilePath}`);
    }
  }

  // ============ 7) Model Request/Response ============
  {
    const modelDir = path.join(rootDir, 'src', 'data', 'models', functionNameKebab);
    createModelFiles(modelDir, project, functionNamePascal, functionNameKebab, hasNoParam);

    // Ghi đè dữ liệu từ API sờ pếc
    // generateFromOpenAPI(rootDir, methodName);
  }

  // ============ 8) Entity => State ============
  if (!hasNoState) {
    const entityDir = path.join(rootDir, 'src', 'domain', 'states', functionNameKebab);
    fs.mkdirSync(entityDir, {recursive: true});

    // [FunctionNamePascal]State.ts
    const statePath = path.join(entityDir, `${functionNamePascal}State.ts`);
    if (!fs.existsSync(statePath)) {
      const dtoContent = `
        import {MSBError} from '../../../data/models/BaseResponse';
        import {${functionNamePascal}Model} from '../../entities/${functionNameKebab}/${functionNamePascal}Model';

        export type ${functionNamePascal}State =
          | { status: 'INIT' }
          | { status: 'LOADING' }
          | { status: 'SUCCESS'; data: ${functionNamePascal}Model | undefined | null }
          | { status: 'ERROR'; error?: MSBError | undefined | null };
        `;
      fs.writeFileSync(statePath, dtoContent.trim() + '\n');
      console.log(`[CREATE] => ${statePath}`);
      project.addSourceFileAtPath(statePath);
    }
  }

  // ============ 9) Update PathResolver thủ công (có auto thêm domain nếu thiếu) ============
  {
    const pathResolverPath = path.join(rootDir, 'src', 'utils', 'PathResolver.ts');
    const domainName = toCamelCase(rawModuleName); // fundTranfer
    const method = methodName; // splitOrder
    const apiPath = `/${toKebabCase(rawModuleName)}/${toKebabCase(rawFunctionName)}`;

    if (fs.existsSync(pathResolverPath)) {
      let content = fs.readFileSync(pathResolverPath, 'utf-8');

      const pathResolverMatch = content.match(/export\s+const\s+PathResolver\s*=\s*{([\s\S]*?)^};/m);
      const domainPattern = new RegExp(`(${domainName}\\s*:\\s*{)([\\s\\S]*?)(^\\s*})`, 'm');
      const domainMatch = content.match(domainPattern);

      if (domainMatch) {
        if (domainMatch[2].includes(`${method}:`)) {
          console.log(`[SKIP] PathResolver.${domainName} đã có "${method}"`);
        } else {
          const insertion = `    ${method}: () => \`\${baseUrl}${apiPath}\`,\n`;
          const updatedBlock = domainMatch[1] + '\n' + domainMatch[2] + insertion + domainMatch[3];
          content = content.replace(domainPattern, updatedBlock);
          fs.writeFileSync(pathResolverPath, content, 'utf-8');
          console.log(`[UPDATE] Đã thêm path "${method}" vào PathResolver.${domainName}`);
        }
      } else if (pathResolverMatch) {
        // Nếu chưa có domain, chèn domain mới với method vào
        const domainInsert = `  ${domainName}: {\n    ${method}: () => \`\${baseUrl}${apiPath}\`,\n  },\n`;
        const updated = content.replace(/(export\s+const\s+PathResolver\s*=\s*{)/, `$1\n${domainInsert}`);
        fs.writeFileSync(pathResolverPath, updated, 'utf-8');
        console.log(`[ADD] Đã tạo mới PathResolver.${domainName} với path "${method}"`);
      } else {
        console.warn('[WARN] Không tìm thấy object PathResolver trong file.');
      }
    } else {
      console.warn('[SKIP] Không tìm thấy file PathResolver.ts');
    }
  }

  // ============ 10) Update DIContainer.ts ============
  {
    const diPath = path.join(rootDir, 'src', 'di', 'DIContainer.ts');
    const moduleName = toPascalCase(rawModuleName); // FundsTransfer
    const camelModule = toCamelCase(rawModuleName); // fundsTransfer
    const functionName = toPascalCase(rawFunctionName); // SplitOrder

    const importLines = [
      `import {I${moduleName}DataSource} from '../data/datasources/I${moduleName}DataSource';`,
      `import {${moduleName}RemoteDataSource} from '../data/datasources/remote/${moduleName}RemoteDataSource';`,
      `import {I${moduleName}Repository} from '../domain/repositories/I${moduleName}Repository';`,
      `import {${moduleName}Repository} from '../data/repositories/${moduleName}Repository';`,
      `import {${functionName}UseCase} from '../domain/usecases/${toKebabCase(moduleName)}/${functionName}UseCase';`,
    ];

    if (!fs.existsSync(diPath)) {
      console.warn('[SKIP] Không tìm thấy DIContainer.ts');
    } else {
      let content = fs.readFileSync(diPath, 'utf-8');

      // IMPORT
      importLines.forEach(line => {
        if (!content.includes(line)) {
          content = line + '\n' + content;
        }
      });

      // ADD PRIVATE FIELDS
      const fieldMarker = /private\s+isUseMock\s*=\s*false;/;
      const newFields = `
        private ${camelModule}DataSource!: I${moduleName}DataSource;
        private ${camelModule}Repository!: I${moduleName}Repository;
      `;
      if (!content.includes(`${camelModule}DataSource`)) {
        content = content.replace(fieldMarker, match => match + newFields);
      }

      // ADD GET DATA SOURCE METHOD
      const getDataSource = `
        private get${moduleName}DataSource(): I${moduleName}DataSource {
          if (!this.${camelModule}DataSource) {
            this.${camelModule}DataSource = new ${moduleName}RemoteDataSource(this.httpClient);
          }
          return this.${camelModule}DataSource;
        }
      `;
      if (!content.includes(`get${moduleName}DataSource()`)) {
        content = content.replace('//DATA SOURCES', `//DATA SOURCES\n${getDataSource}`);
      }

      // ADD GET REPOSITORY METHOD
      const getRepo = `
        public get${moduleName}Repository(): I${moduleName}Repository {
          if (!this.${camelModule}Repository) {
            this.${camelModule}Repository = new ${moduleName}Repository(this.get${moduleName}DataSource());
          }
          return this.${camelModule}Repository;
        }
      `;
      if (!content.includes(`get${moduleName}Repository()`)) {
        content = content.replace('// REPORITORIES', `// REPORITORIES\n${getRepo}`);
      }

      // ADD GET USECASE METHOD
      const getUseCase = `
        public get${functionName}UseCase(): ${functionName}UseCase {
          return new ${functionName}UseCase(this.get${moduleName}Repository());
        }
      `;
      if (!content.includes(`get${functionName}UseCase()`)) {
        if (content.includes('// USE CASES')) {
          content = content.replace('// USE CASES', `// USE CASES\n${getUseCase}`);
        } else {
          content += `\n${getUseCase}`;
        }
      }

      fs.writeFileSync(diPath, content, 'utf-8');
      console.log(`[UPDATE] Đã thêm ${functionName}UseCase, Repository, DataSource vào DIContainer.`);
    }
  }

  // Lưu AST
  await project.save();

  // Format code

  try {
    // execSync('npx eslint --fix src --ext .ts,.tsx', {stdio: 'inherit'});
    execSync('npx prettier --write "src/**/*.{ts,tsx}"', {stdio: 'inherit'});
    console.log('\n✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨ Code formatted');
  } catch (err) {
    console.warn('⚠️ Méo chạy được Prettier. Cài vào ae ơi.');
  }

  console.log(`\n[DONE] Tạo/chèn method thành công! Module=${rawModuleName}, Function=${rawFunctionName}\n`);
}

main().catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
