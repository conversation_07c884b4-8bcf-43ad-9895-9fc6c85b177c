#!/usr/bin/env node

/**
 * This is a <PERSON>sky pre-push hook that ensures package.json version has been incremented
 * before pushing to the remote repository.
 *
 * It compares the current version with the version in the remote repository's package.json
 * to ensure proper versioning.
 */

import {execSync} from 'child_process';
import fs from 'fs';
import path from 'path';

const RED = '\x1b[31m%s\x1b[0m';
const GREEN = '\x1b[32m%s\x1b[0m';
const YELLOW = '\x1b[33m%s\x1b[0m';

// Read current package.json
const packageJsonPath = path.resolve(process.cwd(), 'package.json');
let currentPackageJson;

try {
  currentPackageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
} catch (err) {
  console.error(RED, 'Error reading package.json:', err.message);
  process.exit(1);
}

const currentVersion = currentPackageJson.version;

if (!currentVersion) {
  console.error(RED, 'No version specified in package.json');
  process.exit(1);
}

// Get the remote version from the target branch
// const getCurrentBranch = () => {
//   try {
//     return execSync('git rev-parse --abbrev-ref HEAD', {encoding: 'utf8'}).trim();
//   } catch (err) {
//     console.error(RED, 'Error getting current branch:', err.message);
//     process.exit(1);
//   }
// };

const getRemoteVersion = branch => {
  try {
    // Get the remote name (usually 'origin')
    const remotes = execSync('git remote', {encoding: 'utf8'}).trim().split('\n');
    if (!remotes.length) {
      console.error(RED, 'No git remotes found');
      process.exit(1);
    }
    const remote = remotes[0]; // Use the first remote (usually 'origin')

    // Try to fetch the package.json from the remote
    console.log(YELLOW, `Fetching ${remote}/${branch} to check version...`);

    try {
      // Make sure we have the latest from remote
      execSync(`git fetch ${remote} ${branch}`, {stdio: 'inherit'});
    } catch (err) {
      // If this is a new branch, there's no remote version to compare with
      console.log(
        YELLOW,
        `Branch ${branch} doesn't exist on remote ${remote} or couldn't be fetched. Assuming this is a new branch.`,
      );
      return null;
    }

    // Get the package.json content from the remote
    try {
      const remotePackageJson = execSync(`git show ${remote}/${branch}:package.json`, {encoding: 'utf8'});
      return JSON.parse(remotePackageJson).version;
    } catch (err) {
      console.log(YELLOW, 'No package.json found on remote or parsing error. Allowing push without version check.');
      return null;
    }
  } catch (err) {
    console.error(RED, 'Error getting remote version:', err.message);
    process.exit(1);
  }
};

const compareVersions = (v1, v2) => {
  if (!v1 || !v2) {
    return false;
  }

  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const part1 = v1Parts[i] || 0;
    const part2 = v2Parts[i] || 0;

    if (part1 > part2) {
      return 1;
    }
    if (part1 < part2) {
      return -1;
    }
  }

  return 0; // Versions are equal
};

// Main execution
// const currentBranch = getCurrentBranch();
const remoteVersion = getRemoteVersion('sit');

// If we couldn't get a remote version, allow the push
if (remoteVersion === null) {
  console.log(
    GREEN,
    `Allowing push as no valid remote version was found to compare with. Current version: ${currentVersion}`,
  );
  process.exit(0);
}

// Compare versions
const comparison = compareVersions(currentVersion, remoteVersion);

if (comparison <= 0) {
  console.error(RED, 'Version in package.json must be incremented before pushing.');
  console.error(RED, `Current version: ${currentVersion}, Remote version: ${remoteVersion}`);
  console.error(RED, 'Please update the version in package.json and try again.');
  process.exit(1);
}

console.log(GREEN, `Version check passed. Current: ${currentVersion}, Remote: ${remoteVersion}`);
process.exit(0);
