type Props = any;

declare module 'PaymentModule/PaymentModuleMain' {
  import React from 'react';
  const Default: React.ComponentType<Props>;
  export default Default;
}

declare module 'TransferModule/SourceAccount' {
  import React from 'react';
  import {StyleProp, ViewStyle} from 'react-native';
  type SourceAccountProps = {
    containerStyle?: StyleProp<ViewStyle>;
    title: string; // example: 'Tài khoản nguồn'
    onSelectAccount: (account: Props) => void; // get account has selected from source account list
    handleFilterAccounts?: (accounts: Props[]) => Props[]; // filter with conditions accounts has selected from source account list
    errorTitle?: string; // if error, show error
    hasCreditCard?: boolean;
    sourceAccounts?: Props[]; // custom account list
    disabled?: boolean; // if true, disable select account
    accNo?: string; // custom first account has selected from source account list
    request?: Props;
  };
  const Default: React.ComponentType<SourceAccountProps>;
  export default Default;
}
