import * as React from 'react';
import Svg, {<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Defs, LinearGradient, Stop} from 'react-native-svg';

const IconError = ({width = 144, height = 144}) => (
  <Svg width={width} height={height} viewBox="0 0 144 144" fill="none">
    <Path
      d="M35.0648 64.5185C34.4441 58.7853 39.0017 53.1175 43.0652 49.0178C47.1288 44.9182 52.3534 42.1886 57.597 39.7689C62.8406 37.3512 68.2382 35.1549 72.99 31.8773C77.3955 28.84 81.134 24.949 85.3095 21.604C89.4828 18.2568 94.3169 15.3818 99.6512 14.9371C104.899 14.4986 110.303 16.6043 113.866 20.4763C117.432 24.3483 119.078 29.9002 118.202 35.0854C117.345 40.1504 114.295 44.562 111.006 48.5141C105.8 54.7637 100.726 59.8055 94.0952 64.5227H35.0648V64.5185Z"
      fill="#FEEFE7"
    />
    <Path
      d="M75.4579 67.5791C83.642 62.2337 90.8847 55.453 96.7467 47.6373C98.1801 45.7255 99.5691 43.6704 99.9828 41.3181C100.399 38.9659 99.5923 36.2426 97.5046 35.0749C95.134 33.7491 92.1935 34.8135 89.7004 35.8906C81.4044 39.4738 73.1063 43.0571 64.8103 46.6403C59.4189 48.9694 53.9685 51.3323 49.3096 54.905C44.6507 58.4755 40.7941 63.4394 39.7639 69.2126C50.9604 69.2864 64.2614 67.5053 75.4579 67.5791Z"
      fill="#FEEFE7"
    />
    <Mask
      id="mask0_6662_8863"
      style={{
        maskType: 'alpha',
      }}
      maskUnits="userSpaceOnUse"
      x={39}
      y={34}
      width={62}
      height={36}>
      <Path
        d="M75.4579 67.5793C83.642 62.234 90.8847 55.4532 96.7467 47.6376C98.1801 45.7258 99.5691 43.6707 99.9828 41.3184C100.399 38.9661 99.5923 36.2428 97.5046 35.0751C95.134 33.7493 92.1935 34.8138 89.7004 35.8908C81.4044 39.4741 73.1063 43.0573 64.8103 46.6406C59.4189 48.9697 53.9685 51.3325 49.3096 54.9052C44.6507 58.4758 40.7941 63.4396 39.7639 69.2129C50.9604 69.2866 64.2614 67.5056 75.4579 67.5793Z"
        fill="url(#paint0_linear_6662_8863)"
      />
    </Mask>
    <G mask="url(#mask0_6662_8863)">
      <Path
        d="M75.4579 67.5793C83.642 62.234 90.8847 55.4532 96.7467 47.6376C98.1801 45.7258 99.5691 43.6707 99.9828 41.3184C100.399 38.9661 99.5923 36.2428 97.5046 35.0751C95.134 33.7493 92.1935 34.8138 89.7004 35.8908C81.4044 39.4741 73.1063 43.0573 64.8103 46.6406C59.4189 48.9697 53.9685 51.3325 49.3096 54.9052C44.6507 58.4758 40.7941 63.4396 39.7639 69.2129C50.9604 69.2866 64.2614 67.5056 75.4579 67.5793Z"
        fill="#F4600C"
      />
    </G>
    <Path
      d="M48.6151 24.8656C51.1636 24.8656 53.2297 22.8027 53.2297 20.258C53.2297 17.7133 51.1636 15.6504 48.6151 15.6504C46.0666 15.6504 44.0006 17.7133 44.0006 20.258C44.0006 22.8027 46.0666 24.8656 48.6151 24.8656Z"
      fill="#F4600C"
    />
    <Path
      d="M83.4479 74.6423H98.6129C102.047 74.6423 104.697 77.6649 104.239 81.0648L103.04 89.9786C102.174 96.4242 96.6645 101.234 90.1523 101.234H76.7203"
      stroke="#F8A06D"
      strokeWidth={9.23539}
      strokeMiterlimit={10}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M121.543 41.7123C126.324 34.968 126.443 25.2077 121.824 18.3497L124.277 16.7031C129.58 24.5785 129.445 35.6727 123.955 43.4173L121.543 41.7123Z"
      fill="#F8A06D"
    />
    <Path
      d="M76.8094 111.36H52.3519C47.1168 111.36 42.6395 107.606 41.7339 102.457L35.0654 64.5186H94.0959L87.4274 102.457C86.5218 107.604 82.0445 111.36 76.8094 111.36Z"
      fill="#FEEFE7"
    />
    <Mask
      id="mask1_6662_8863"
      style={{
        maskType: 'alpha',
      }}
      maskUnits="userSpaceOnUse"
      x={35}
      y={64}
      width={60}
      height={48}>
      <Path
        d="M76.8099 111.36H52.3524C47.1173 111.36 42.64 107.606 41.7344 102.457L35.0659 64.5186H94.0964L87.4279 102.457C86.5223 107.604 82.045 111.36 76.8099 111.36Z"
        fill="url(#paint1_linear_6662_8863)"
      />
    </Mask>
    <G mask="url(#mask1_6662_8863)">
      <Path
        d="M76.8099 111.36H52.3524C47.1173 111.36 42.64 107.606 41.7344 102.457L35.0659 64.5186H94.0964L87.4279 102.457C86.5223 107.604 82.045 111.36 76.8099 111.36Z"
        fill="#F8A06D"
      />
    </G>
    <G opacity={0.3} filter="url(#filter0_f_6662_8863)">
      <Ellipse cx={68.9123} cy={133.92} rx={31.68} ry={3.36} fill="url(#paint2_linear_6662_8863)" />
    </G>
    <Defs>
      <LinearGradient
        id="paint0_linear_6662_8863"
        x1={-25.5764}
        y1={79.2732}
        x2={72.687}
        y2={73.248}
        gradientUnits="userSpaceOnUse">
        <Stop />
        <Stop offset={1} stopColor="#111111" stopOpacity={0} />
      </LinearGradient>
      <LinearGradient
        id="paint1_linear_6662_8863"
        x1={47.0406}
        y1={81.2755}
        x2={76.8511}
        y2={123.263}
        gradientUnits="userSpaceOnUse">
        <Stop />
        <Stop offset={1} stopOpacity={0} />
      </LinearGradient>
      <LinearGradient
        id="paint2_linear_6662_8863"
        x1={58.4418}
        y1={130.56}
        x2={107.364}
        y2={135.53}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F4600C" />
        <Stop offset={1} stopColor="#F4600C" stopOpacity={0.03} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default IconError;
