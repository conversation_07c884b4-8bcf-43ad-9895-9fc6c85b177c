import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

const IconSchedule = ({width = 25, height = 24}) => (
  <Svg width={width} height={height} viewBox="0 0 25 24" fill="none">
    <Path
      d="M7.99901 11.9999C7.44875 11.9999 6.99746 12.4464 7.00001 13.0026C7.00148 13.5536 7.4487 13.9999 8.00001 13.9999C8.55222 13.9999 9.00001 13.5521 9.00001 12.9999C9.00001 12.4457 8.55027 11.9999 7.99901 11.9999Z"
      fill="#F4600C"
    />
    <Path
      d="M7.00001 17.0026C6.99746 16.4464 7.44875 15.9999 7.99901 15.9999C8.55027 15.9999 9.00001 16.4457 9.00001 16.9999C9.00001 17.5521 8.55222 17.9999 8.00001 17.9999C7.4487 17.9999 7.00148 17.5536 7.00001 17.0026Z"
      fill="#F4600C"
    />
    <Path
      d="M12.499 15.9999C11.9487 15.9999 11.4975 16.4464 11.5 17.0026C11.5015 17.5536 11.9487 17.9999 12.5 17.9999C13.0522 17.9999 13.5 17.5521 13.5 16.9999C13.5 16.4457 13.0503 15.9999 12.499 15.9999Z"
      fill="#F4600C"
    />
    <Path
      d="M11.5 13.0026C11.4975 12.4464 11.9487 11.9999 12.499 11.9999C13.0503 11.9999 13.5 12.4457 13.5 12.9999C13.5 13.5521 13.0522 13.9999 12.5 13.9999C11.9487 13.9999 11.5015 13.5536 11.5 13.0026Z"
      fill="#F4600C"
    />
    <Path
      d="M16.999 11.9999C16.4487 11.9999 15.9975 12.4464 16 13.0026C16.0015 13.5536 16.4487 13.9999 17 13.9999C17.5522 13.9999 18 13.5521 18 12.9999C18 12.4457 17.5503 11.9999 16.999 11.9999Z"
      fill="#F4600C"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.5 2.12988C8.91421 2.12988 9.25 2.46567 9.25 2.87988V3.24988H15.75V2.87988C15.75 2.46567 16.0858 2.12988 16.5 2.12988C16.9142 2.12988 17.25 2.46567 17.25 2.87988V3.24988H18.5C20.5712 3.24988 22.25 4.92866 22.25 6.99988V17.9999C22.25 20.0711 20.5712 21.7499 18.5 21.7499H6.5C4.42879 21.7499 2.75 20.0711 2.75 17.9999V6.99988C2.75 4.92866 4.42879 3.24988 6.5 3.24988H7.75V2.87988C7.75 2.46567 8.08579 2.12988 8.5 2.12988ZM7.75 5.11988V4.74988H6.5C5.25721 4.74988 4.25 5.75709 4.25 6.99988V8.24988H20.75V6.99988C20.75 5.75709 19.7428 4.74988 18.5 4.74988H17.25V5.11988C17.25 5.5341 16.9142 5.86988 16.5 5.86988C16.0858 5.86988 15.75 5.5341 15.75 5.11988V4.74988H9.25V5.11988C9.25 5.5341 8.91421 5.86988 8.5 5.86988C8.08579 5.86988 7.75 5.5341 7.75 5.11988ZM4.25 17.9999V9.74988H20.75V17.9999C20.75 19.2427 19.7428 20.2499 18.5 20.2499H6.5C5.25721 20.2499 4.25 19.2427 4.25 17.9999Z"
      fill="#091E42"
    />
  </Svg>
);
export default IconSchedule;
