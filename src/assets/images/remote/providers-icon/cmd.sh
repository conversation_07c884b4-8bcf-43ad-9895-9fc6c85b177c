#!/bin/bash

# Define the directory containing the SVG files
SVG_DIR="src/assets/images/banks-logo"

# Define the output file
OUTPUT_FILE="index.ts"

# Start the index.ts file with a comment
echo "// Auto-generated index file for SVG imports" > $OUTPUT_FILE

# Iterate over each SVG file in the directory
for svg_file in *.png; do
  # Extract the base name of the file (without extension)
  base_name=$(basename "$svg_file" .png | sed 's/[^a-zA-Z0-9]//g')
  base_name=$(echo "$base_name" | tr '[:upper:]' '[:lower:]')

  svg_name=$(basename "$svg_file" .png)
  # Create an import statement for each SVG file
  echo "import $base_name from './$svg_name.png';" >> $OUTPUT_FILE
done

# Add export statement at the end of the file
echo -e "\nexport {" >> $OUTPUT_FILE
for svg_file in *.png; do
  base_name=$(basename "$svg_file" .png | sed 's/[^a-zA-Z0-9]//g')
  base_name=$(echo "$base_name" | tr '[:upper:]' '[:lower:]')
  echo "  $base_name," >> $OUTPUT_FILE
done
echo "};" >> $OUTPUT_FILE

echo "SVG imports have been successfully added to $OUTPUT_FILE"