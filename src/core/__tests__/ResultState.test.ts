/**
 * Unit Tests for ResultState
 * Testing the Result pattern implementation
 */

import { ResultState } from '../ResultState';
import { CustomError, ErrorCategory } from '../MSBCustomError';

describe('ResultState', () => {
  describe('Success State', () => {
    it('should create success state with data', () => {
      const testData = { id: 1, name: 'Test User' };
      const result: ResultState<typeof testData> = {
        status: 'SUCCESS',
        data: testData,
      };

      expect(result.status).toBe('SUCCESS');
      expect(result.data).toEqual(testData);
      expect('error' in result).toBe(false);
    });

    it('should create success state with undefined data', () => {
      const result: ResultState<string> = {
        status: 'SUCCESS',
        data: undefined,
      };

      expect(result.status).toBe('SUCCESS');
      expect(result.data).toBeUndefined();
    });

    it('should handle complex data types', () => {
      interface ComplexData {
        users: Array<{ id: number; name: string }>;
        metadata: {
          total: number;
          page: number;
        };
      }

      const complexData: ComplexData = {
        users: [
          { id: 1, name: 'User 1' },
          { id: 2, name: 'User 2' },
        ],
        metadata: {
          total: 2,
          page: 1,
        },
      };

      const result: ResultState<ComplexData> = {
        status: 'SUCCESS',
        data: complexData,
      };

      expect(result.status).toBe('SUCCESS');
      expect(result.data?.users).toHaveLength(2);
      expect(result.data?.metadata.total).toBe(2);
    });
  });

  describe('Error State', () => {
    it('should create error state with CustomError', () => {
      const customError = new CustomError(
        'TEST_ERROR',
        ErrorCategory.BUSINESS,
        'Test Error',
        'This is a test error',
        true,
        [{ type: 'RETRY', label: 'Retry', primary: true }]
      );

      const result: ResultState<any> = {
        status: 'ERROR',
        error: customError,
      };

      expect(result.status).toBe('ERROR');
      expect(result.error).toBe(customError);
      expect(result.error.code).toBe('TEST_ERROR');
      expect(result.error.category).toBe(ErrorCategory.BUSINESS);
      expect('data' in result).toBe(false);
    });

    it('should handle different error categories', () => {
      const networkError = new CustomError(
        'NETWORK_001',
        ErrorCategory.NETWORK,
        'Network Error',
        'Connection failed',
        true,
        []
      );

      const validationError = new CustomError(
        'VALIDATION_001',
        ErrorCategory.VALIDATION,
        'Validation Error',
        'Invalid input',
        false,
        []
      );

      const networkResult: ResultState<any> = {
        status: 'ERROR',
        error: networkError,
      };

      const validationResult: ResultState<any> = {
        status: 'ERROR',
        error: validationError,
      };

      expect(networkResult.error.category).toBe(ErrorCategory.NETWORK);
      expect(networkResult.error.retryable).toBe(true);

      expect(validationResult.error.category).toBe(ErrorCategory.VALIDATION);
      expect(validationResult.error.retryable).toBe(false);
    });
  });

  describe('Type Safety', () => {
    it('should enforce type safety for success data', () => {
      interface User {
        id: number;
        email: string;
      }

      const result: ResultState<User> = {
        status: 'SUCCESS',
        data: {
          id: 1,
          email: '<EMAIL>',
        },
      };

      // TypeScript should enforce that data matches User interface
      expect(result.data?.id).toBe(1);
      expect(result.data?.email).toBe('<EMAIL>');
    });

    it('should work with generic types', () => {
      function createSuccessResult<T>(data: T): ResultState<T> {
        return {
          status: 'SUCCESS',
          data,
        };
      }

      function createErrorResult<T>(error: CustomError): ResultState<T> {
        return {
          status: 'ERROR',
          error,
        };
      }

      const stringResult = createSuccessResult('test string');
      const numberResult = createSuccessResult(42);
      const arrayResult = createSuccessResult([1, 2, 3]);

      expect(stringResult.data).toBe('test string');
      expect(numberResult.data).toBe(42);
      expect(arrayResult.data).toEqual([1, 2, 3]);

      const error = new CustomError(
        'GENERIC_ERROR',
        ErrorCategory.UNKNOWN,
        'Error',
        'Error message',
        false,
        []
      );

      const errorResult = createErrorResult<string>(error);
      expect(errorResult.status).toBe('ERROR');
      expect(errorResult.error.code).toBe('GENERIC_ERROR');
    });
  });

  describe('Discriminated Union Behavior', () => {
    it('should properly discriminate between success and error states', () => {
      const successResult: ResultState<string> = {
        status: 'SUCCESS',
        data: 'success data',
      };

      const errorResult: ResultState<string> = {
        status: 'ERROR',
        error: new CustomError(
          'ERROR_001',
          ErrorCategory.API,
          'API Error',
          'API call failed',
          true,
          []
        ),
      };

      // Type guards should work properly
      if (successResult.status === 'SUCCESS') {
        expect(successResult.data).toBe('success data');
        // TypeScript knows this is success state, so 'error' property doesn't exist
      }

      if (errorResult.status === 'ERROR') {
        expect(errorResult.error.code).toBe('ERROR_001');
        // TypeScript knows this is error state, so 'data' property doesn't exist
      }
    });

    it('should work with type guards in functions', () => {
      function handleResult<T>(result: ResultState<T>): string {
        if (result.status === 'SUCCESS') {
          return `Success: ${JSON.stringify(result.data)}`;
        } else {
          return `Error: ${result.error.userMessage}`;
        }
      }

      const successResult: ResultState<{ name: string }> = {
        status: 'SUCCESS',
        data: { name: 'John' },
      };

      const errorResult: ResultState<{ name: string }> = {
        status: 'ERROR',
        error: new CustomError(
          'HANDLE_ERROR',
          ErrorCategory.BUSINESS,
          'Business Error',
          'Business logic failed',
          false,
          []
        ),
      };

      expect(handleResult(successResult)).toBe('Success: {"name":"John"}');
      expect(handleResult(errorResult)).toBe('Error: Business logic failed');
    });
  });

  describe('Real-world Usage Patterns', () => {
    it('should work with async functions', async () => {
      async function fetchUser(id: number): Promise<ResultState<{ id: number; name: string }>> {
        try {
          // Simulate API call
          if (id === 1) {
            return {
              status: 'SUCCESS',
              data: { id: 1, name: 'John Doe' },
            };
          } else {
            return {
              status: 'ERROR',
              error: new CustomError(
                'USER_NOT_FOUND',
                ErrorCategory.API,
                'User Not Found',
                'User with specified ID not found',
                false,
                [{ type: 'GO_BACK', label: 'Go Back', primary: true }]
              ),
            };
          }
        } catch (error) {
          return {
            status: 'ERROR',
            error: new CustomError(
              'FETCH_ERROR',
              ErrorCategory.NETWORK,
              'Fetch Error',
              'Failed to fetch user',
              true,
              [{ type: 'RETRY', label: 'Retry', primary: true }]
            ),
          };
        }
      }

      const successResult = await fetchUser(1);
      const errorResult = await fetchUser(999);

      expect(successResult.status).toBe('SUCCESS');
      if (successResult.status === 'SUCCESS') {
        expect(successResult.data?.name).toBe('John Doe');
      }

      expect(errorResult.status).toBe('ERROR');
      if (errorResult.status === 'ERROR') {
        expect(errorResult.error.code).toBe('USER_NOT_FOUND');
      }
    });

    it('should work with chaining operations', () => {
      function processResult<T, U>(
        result: ResultState<T>,
        processor: (data: T) => U
      ): ResultState<U> {
        if (result.status === 'SUCCESS') {
          try {
            const processedData = processor(result.data!);
            return {
              status: 'SUCCESS',
              data: processedData,
            };
          } catch (error) {
            return {
              status: 'ERROR',
              error: new CustomError(
                'PROCESSING_ERROR',
                ErrorCategory.SYSTEM,
                'Processing Error',
                'Failed to process data',
                false,
                []
              ),
            };
          }
        } else {
          return result; // Pass through error
        }
      }

      const initialResult: ResultState<number> = {
        status: 'SUCCESS',
        data: 5,
      };

      const processedResult = processResult(initialResult, (num) => num * 2);

      expect(processedResult.status).toBe('SUCCESS');
      if (processedResult.status === 'SUCCESS') {
        expect(processedResult.data).toBe(10);
      }
    });
  });
});
