/**
 * Unit Tests for MSBCustomError
 * Testing Error System - CustomError, ErrorMapper, and related utilities
 */

import {
  CustomError,
  ErrorCategory,
  ErrorAction,
  ErrorMapper,
  createError,
  extractErrorFromResponse,
  isRetryable,
  hasError,
} from '../MSBCustomError';
import { MSBErrorCode } from '../MSBErrorCode';
import { BaseResponse } from '../BaseResponse';

describe('CustomError', () => {
  describe('Constructor', () => {
    it('should create CustomError with all properties', () => {
      const actions: ErrorAction[] = [
        { type: 'RETRY', label: 'Retry', primary: true },
        { type: 'CANCEL', label: 'Cancel', primary: false },
      ];

      const error = new CustomError(
        'TEST_001',
        ErrorCategory.BUSINESS,
        'Test Title',
        'Test message',
        true,
        actions
      );

      expect(error.code).toBe('TEST_001');
      expect(error.category).toBe(ErrorCategory.BUSINESS);
      expect(error.title).toBe('Test Title');
      expect(error.userMessage).toBe('Test message');
      expect(error.retryable).toBe(true);
      expect(error.actions).toEqual(actions);
      expect(error.name).toBe('CustomError');
      expect(error.message).toBe('Test message');
    });

    it('should handle null actions', () => {
      const error = new CustomError(
        'TEST_002',
        ErrorCategory.NETWORK,
        'Test Title',
        'Test message',
        false,
        null
      );

      expect(error.actions).toEqual([]);
    });

    it('should handle undefined actions', () => {
      const error = new CustomError(
        'TEST_003',
        ErrorCategory.API,
        'Test Title',
        'Test message',
        false
      );

      expect(error.actions).toEqual([]);
    });
  });

  describe('Action Methods', () => {
    const actions: ErrorAction[] = [
      { type: 'RETRY', label: 'Retry', primary: true },
      { type: 'CANCEL', label: 'Cancel', primary: false },
      { type: 'HELP', label: 'Help' }, // No primary specified
    ];

    const error = new CustomError(
      'TEST_004',
      ErrorCategory.VALIDATION,
      'Test Title',
      'Test message',
      true,
      actions
    );

    it('should get primary action', () => {
      const primaryAction = error.getPrimaryAction();
      expect(primaryAction).toEqual({ type: 'RETRY', label: 'Retry', primary: true });
    });

    it('should get secondary action', () => {
      const secondaryAction = error.getSecondaryAction();
      expect(secondaryAction).toEqual({ type: 'CANCEL', label: 'Cancel', primary: false });
    });

    it('should return first action as primary if no primary specified', () => {
      const errorWithoutPrimary = new CustomError(
        'TEST_005',
        ErrorCategory.SYSTEM,
        'Test',
        'Test',
        false,
        [{ type: 'ACTION1', label: 'Action 1' }, { type: 'ACTION2', label: 'Action 2' }]
      );

      expect(errorWithoutPrimary.getPrimaryAction()).toEqual({ type: 'ACTION1', label: 'Action 1' });
    });

    it('should return null for actions when no actions available', () => {
      const errorWithoutActions = new CustomError(
        'TEST_006',
        ErrorCategory.UNKNOWN,
        'Test',
        'Test',
        false,
        []
      );

      expect(errorWithoutActions.getPrimaryAction()).toBeNull();
      expect(errorWithoutActions.getSecondaryAction()).toBeNull();
    });
  });
});

describe('ErrorMapper', () => {
  describe('createError', () => {
    it('should create error from known error code', () => {
      const error = ErrorMapper.createError(MSBErrorCode.PIS0101);
      
      expect(error.code).toBe(MSBErrorCode.PIS0101);
      expect(error.category).toBe(ErrorCategory.BUSINESS);
      expect(error.retryable).toBe(false);
      expect(error.actions).toHaveLength(2);
    });

    it('should create unknown error for undefined code', () => {
      const error = ErrorMapper.createError();
      
      expect(error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
      expect(error.category).toBe(ErrorCategory.UNKNOWN);
      expect(error.retryable).toBe(true);
    });

    it('should create unknown error for unrecognized code', () => {
      const error = ErrorMapper.createError('INVALID_CODE');
      
      expect(error.code).toBe('INVALID_CODE');
      expect(error.category).toBe(ErrorCategory.UNKNOWN);
      expect(error.retryable).toBe(true);
    });
  });

  describe('extractErrorFromResponse', () => {
    it('should extract error from BaseResponse with errors', () => {
      const response: BaseResponse<any> = {
        data: null,
        errors: [
          {
            key: MSBErrorCode.PIS0106,
            message: 'Network error',
            context: {
              vi: 'Lỗi mạng',
              en: 'Network error',
            },
          },
        ],
      };

      const error = ErrorMapper.extractErrorFromResponse(response);
      
      expect(error).not.toBeNull();
      expect(error!.code).toBe(MSBErrorCode.PIS0106);
      expect(error!.userMessage).toBe('Lỗi mạng');
      expect(error!.category).toBe(ErrorCategory.NETWORK);
    });

    it('should return null for response without errors', () => {
      const response: BaseResponse<any> = {
        data: { success: true },
      };

      const error = ErrorMapper.extractErrorFromResponse(response);
      expect(error).toBeNull();
    });

    it('should return null for response with empty errors array', () => {
      const response: BaseResponse<any> = {
        data: null,
        errors: [],
      };

      const error = ErrorMapper.extractErrorFromResponse(response);
      expect(error).toBeNull();
    });

    it('should use fallback message when context not available', () => {
      const response: BaseResponse<any> = {
        data: null,
        errors: [
          {
            key: MSBErrorCode.A05,
            message: 'System error',
          },
        ],
      };

      const error = ErrorMapper.extractErrorFromResponse(response);
      
      expect(error).not.toBeNull();
      expect(error!.userMessage).toBe('error.errorOccurred'); // Fallback message
    });
  });

  describe('Utility Methods', () => {
    it('should check if error is retryable', () => {
      expect(ErrorMapper.isRetryable(MSBErrorCode.PIS0106)).toBe(true);
      expect(ErrorMapper.isRetryable(MSBErrorCode.PIS0101)).toBe(false);
      expect(ErrorMapper.isRetryable()).toBe(true); // Unknown error is retryable
    });

    it('should get error category', () => {
      expect(ErrorMapper.getCategory(MSBErrorCode.PIS0106)).toBe(ErrorCategory.NETWORK);
      expect(ErrorMapper.getCategory(MSBErrorCode.PIS0103)).toBe(ErrorCategory.VALIDATION);
      expect(ErrorMapper.getCategory()).toBe(ErrorCategory.UNKNOWN);
    });

    it('should check if response has error', () => {
      const responseWithError: BaseResponse<any> = {
        errors: [{ key: 'ERROR_001' }],
      };
      
      const responseWithoutError: BaseResponse<any> = {
        data: { success: true },
      };

      expect(ErrorMapper.hasError(responseWithError)).toBe(true);
      expect(ErrorMapper.hasError(responseWithoutError)).toBe(false);
    });

    it('should get first error from response', () => {
      const response: BaseResponse<any> = {
        errors: [
          { key: 'ERROR_001', message: 'First error' },
          { key: 'ERROR_002', message: 'Second error' },
        ],
      };

      const firstError = ErrorMapper.getFirstError(response);
      expect(firstError).toEqual({ key: 'ERROR_001', message: 'First error' });
    });

    it('should return null when getting first error from response without errors', () => {
      const response: BaseResponse<any> = {
        data: { success: true },
      };

      const firstError = ErrorMapper.getFirstError(response);
      expect(firstError).toBeNull();
    });
  });

  describe('Dynamic Error Management', () => {
    it('should add new predefined error', () => {
      const customError = new CustomError(
        'CUSTOM_001',
        ErrorCategory.BUSINESS,
        'Custom Title',
        'Custom message',
        true,
        [{ type: 'CUSTOM_ACTION', label: 'Custom Action', primary: true }]
      );

      ErrorMapper.addPredefinedError('CUSTOM_001', customError);
      
      const retrievedError = ErrorMapper.createError('CUSTOM_001');
      expect(retrievedError.code).toBe('CUSTOM_001');
      expect(retrievedError.title).toBe('Custom Title');
    });

    it('should get available error codes', () => {
      const codes = ErrorMapper.getAvailableErrorCodes();
      
      expect(codes).toContain(MSBErrorCode.UNKNOWN_ERROR);
      expect(codes).toContain(MSBErrorCode.PIS0101);
      expect(codes).toContain(MSBErrorCode.PIS0106);
      expect(Array.isArray(codes)).toBe(true);
    });
  });
});

describe('Convenience Functions', () => {
  it('should export createError function', () => {
    const error = createError(MSBErrorCode.A05);
    expect(error.code).toBe(MSBErrorCode.A05);
  });

  it('should export extractErrorFromResponse function', () => {
    const response: BaseResponse<any> = {
      errors: [{ key: MSBErrorCode.FTES0008 }],
    };
    
    const error = extractErrorFromResponse(response);
    expect(error?.code).toBe(MSBErrorCode.FTES0008);
  });

  it('should export isRetryable function', () => {
    expect(isRetryable(MSBErrorCode.PIS0106)).toBe(true);
  });

  it('should export hasError function', () => {
    const response: BaseResponse<any> = {
      errors: [{ key: 'ERROR' }],
    };
    
    expect(hasError(response)).toBe(true);
  });
});
