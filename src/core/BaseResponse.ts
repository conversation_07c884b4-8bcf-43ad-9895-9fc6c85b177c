export interface MSBStatus {
  code?: string;
  message?: string;
  title?: string;
}

export interface MSBError {
  message?: string;
  key?: string;
  context?: MSBLocaliztion;
}

export interface MSBLocaliztion {
  vi?: string;
  en?: string;
}

// export interface BaseResponse<T> {
//   status?: MSBStatus;
//   data?: T;
//   errors?: [MSBError];
// }

export type BaseResponse<T> = {
  errors?: [MSBError] | null | undefined;
} & T;

// export const isValidResponse = <T>(response: BaseResponse<T> | null | undefined): boolean => {
//   console.log('isValidResponse', !(!response || response.errors));
//   if (!response) {
//     return false;
//   } // Kiểm tra response có tồn tại không
//   // if (!response.status || response.status?.message != 'Success') return false; // Kiểm tra status có tồn tại không
//   if (response.errors) {
//     return false;
//   } // Nế<PERSON> có lỗi thì response không hợp lệ
//   // if (response.data === undefined || response.data === null) return false; // Kiểm tra data có tồn tại không
//   return true;
// };
