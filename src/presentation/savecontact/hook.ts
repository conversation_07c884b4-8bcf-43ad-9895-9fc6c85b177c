import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback, useEffect, useRef, useState} from 'react';
import {PopupType} from 'msb-host-shared-module/dist/types/PopupType';

import {ACCOUNT_TYPE, ContactType} from '../../commons/Constants.ts';
import Utils from '../../utils/Utils.ts';
import {DIContainer} from '../../di/DIContainer.ts';
import {showErrorPopup, createActionHandlers} from '../../utils/PopupUtils.ts';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel.ts';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel.ts';
import {ProviderSelectionRef, ProviderSelectionState} from '../../components/provider-selection/types.ts';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {MyBillContactModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel.ts';
import {EditBillContactRequest} from '../../data/models/edit-bill-contact/EditBillContactRequest.ts';
import {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest.ts';
import {SaveBillContactRequest} from '../../data/models/save-bill-contact/SaveBillContactRequest.ts';
import useCombineLatest from '../payment-bill/hooks/useCombineLatest.ts';
import {TextInput} from 'react-native';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel.ts';
import {CustomError} from '../../core/MSBCustomError.ts';

const useSaveContact = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'SaveBillContactScreen'>>();
  const {category} = route.params || {};
  const [providerSelected, setProviderSelected] = useState<ProviderModel | null>(null);
  const [accNumber, setAccNumber] = useState<string>('');
  const [billContact, setBillContact] = useState<GetBillDetailModel | undefined | null>();
  const [inputName, setInputName] = useState<string>('');
  const [aliasName, setAliasName] = useState<string>('');
  const [isShowName, setIsShowName] = useState<boolean>(false);
  const [isEditName, setIsEditName] = useState<boolean>(false);
  const [isEnableAutomatic, setEnableAutomatic] = useState<boolean>(false);
  const [typing, setTyping] = useState<boolean>(false);
  const [continueEnable, setContinueEnable] = useState<boolean>(false);
  const [isChoosingBank, setStopHandleBeneficiary] = useState<boolean>(false);
  const [contacts, setContacts] = useState<MyBillContactModel[]>([]);
  const [disableProviderSelection, setDisableProviderSelection] = useState<boolean>(false);
  const providerSelectionRef = useRef<ProviderSelectionRef>(null);
  const {values, updaters, isComplete} = useCombineLatest<ProviderModel | null | string>([null, '']);
  const [updateProviderSelect, updateAccountNumber] = updaters;
  const [defaultSelectedProvider, setDefaultSelectedProvider] = useState<ProviderSelectionState>();
  const inputBillNumberRef = useRef<TextInput>(null);
  const navigation = useNavigation();

  useEffect(() => {
    fetchContacts();
  }, []);

  // #region effects
  useEffect(() => {
    if (providerSelectionRef.current?.isBottomSheetOpen) {
      setStopHandleBeneficiary(true);
    }
  }, [providerSelectionRef.current?.isBottomSheetOpen]);

  useEffect(() => {
    handleBillContract();
  }, [providerSelected]);

  useEffect(() => {
    if (isShowName && isEditName) {
      if (inputName.trim() === '') {
        setContinueEnable(false);
      } else {
        setContinueEnable(true);
      }
    }
    if (providerSelected && accNumber.length > 0 && inputName.length > 0) {
      setContinueEnable(true);
    }
  }, [accNumber.length, providerSelected, inputName, isEditName, isShowName]);

  useEffect(() => {
    if (accNumber.trim() === '') {
      setContinueEnable(false);
    }
  }, [accNumber]);

  // #endregion

  //#region get data functions

  const fetchContacts = async () => {
    const result = await DIContainer.getInstance().getMyBillContactListUseCase().execute();
    if (result.status === 'ERROR') {
      return;
    }
    setContacts(result.data ?? []);
  };

  // #endregion

  // Navigation

  const goHome = () => {
    navigation?.reset({
      index: 0,
      routes: [
        {
          name: 'SegmentStack' as never,
        },
      ],
    });
  };

  // khi out focus input: Nhập số tài khoản/số thẻ
  const onBlurAccountNumber = () => {
    updateAccountNumber(accNumber);
    updateProviderSelect(providerSelected);
    inputBillNumberRef.current?.blur();
    setTyping(false);
  };

  const onSelectProviderItem = useCallback(
    (item: ProviderModel) => {
      console.log('----------------------- select provider item', item);
      setProviderSelected(item);
      updateProviderSelect(item);
    },
    [updateProviderSelect],
  );

  const handleBillContract = async () => {
    if (isChoosingBank || accNumber.trim().length === 0) {
      //Đang chọn ngân hàng thì không xử lý
      return;
    }
    const request: GetBillDetailRequest = {
      billCode: accNumber,
      serviceCode: providerSelected?.serviceCode ?? '',
      accountingType: ACCOUNT_TYPE.ACCT,
    };
    const result = await DIContainer.getInstance().getGetBillDetailUseCase().execute(request);

    if (result.status === 'ERROR') {
      // ✅ Use new error system với proper action handlers
      const handlers = createActionHandlers({
        RETRY: async () => {
          console.log('🔄 Retrying bill detail...');
          await handleBillContract();
        },
        EDIT_INPUT: async () => {
          console.log('✏️ Edit account number...');
          resetInfo();
        },
      });
      showErrorPopup(result.error, handlers);
      resetInfo();
    } else {
      setBillContact(result.data);
      showInfoReadOnly();
      setInfoReadOnly(result.data?.billList?.[0].custName ?? '');
    }
  };

  const verifyDuplicateContact = (
    _contacts: MyBillContactModel[],
    _providerSelected: ProviderModel,
    _accNumber: string,
  ): string | null => {
    const bankCode = _providerSelected?.serviceCode;
    console.log('CHECK DUPLICATE', bankCode, _accNumber, _contacts, category);
    return (
      _contacts.find(contact =>
        contact?.accounts?.some(acc => acc.accountNumber === _accNumber.trim() && acc.externalId === bankCode),
      )?.id ?? null
    );
  };

  const onSubmit = async () => {
    const idTemp = verifyDuplicateContact(contacts, providerSelected!, accNumber);
    if (idTemp == null) {
      await createContact();
      return;
    }
    editContact(idTemp);
  };

  const editContact = async (id: string) => {
    const request: EditBillContactRequest = {
      id: id,
      name: billContact?.getCustomerName() ?? '',
      alias: aliasName.trim(),
      category: ContactType.BILLPAY,
      accounts: [
        {
          bankName: (category as CategoryModel)?.categoryName ?? '',
          accountNumber: billContact?.billCode ?? '',
          accountType: (category as CategoryModel)?.id ?? '',
          bankCode: providerSelected?.subGroupId?.toString() ?? '',
          externalId: providerSelected?.serviceCode ?? '',
        },
      ],
      additions: {
        favoriteStatus: billContact?.getFavoriteStatus() ?? 'INACTIVE',
        reminderStatus: billContact?.getReminderStatus() ?? 'INACTIVE',
        payableAmount: billContact?.getPayableAmount() ?? '0',
      },
    };
    console.log('====================================');
    console.log('request update', request);
    console.log('====================================');
    const result = await DIContainer.getInstance().getEditBillContactUseCase().execute(request);
    if (result.status === 'ERROR') {
      // ✅ Use new error system với proper action handlers
      const handlers = createActionHandlers({
        RETRY: async () => {
          console.log('🔄 Retrying edit contact...');
          await onSubmit();
        },
        NAVIGATE_BACK: async () => {
          console.log('⬅️ Navigate back...');
          navigation.goBack();
        },
      });
      showErrorPopup(result.error, handlers);
      return;
    }
    Utils.showToastSuccess(`Cập nhật hoá đơn thành công`);

    navigation.goBack();
  };

  const createContact = async () => {
    const request: SaveBillContactRequest = {
      name: billContact?.getCustomerName() ?? '',
      alias: aliasName.trim(),
      category: ContactType.BILLPAY,
      accounts: [
        {
          bankName: (category as CategoryModel)?.categoryName ?? '',
          accountNumber: billContact?.billCode ?? '',
          accountType: (category as CategoryModel)?.id ?? '',
          bankCode: providerSelected?.subGroupId ?? '',
          externalId: providerSelected?.serviceCode ?? '',
        },
      ],
      additions: {
        favoriteStatus: 'INACTIVE',
        reminderStatus: 'INACTIVE',
        payableAmount: '0',
      },
    };
    console.log('====================================', providerSelected);
    console.log('request create', request);
    console.log('====================================');
    const result = await DIContainer.getInstance().getSaveBillContactUseCase().execute(request);
    if (result.status === 'ERROR') {
      Utils.showToastError('Thêm hoá đơn không thành công');
      return;
    }
    Utils.showToastSuccess(`Thêm hoá đơn thành công`);
    navigation.goBack();
  };

  const showTransferDialog = (error?: CustomError | null | undefined, onConfirm?: (() => void) | undefined) => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: PopupType.WARNING,
      title: error?.title,
      content: error?.userMessage, // ✅ Fixed: use userMessage instead of message
      confirmBtnText: error?.getPrimaryAction()?.label, // ✅ Fixed: primary = confirm
      cancelBtnText: error?.getSecondaryAction()?.label, // ✅ Fixed: secondary = cancel
      onConfirm: onConfirm,
    });
  };

  //SHOW DATA
  const showDefault = () => {
    setProviderSelected(null);
    setAccNumber('');
    setIsShowName(false);
    setContinueEnable(false);
  };

  const resetInfo = () => {
    setBillContact(null);
    setInputName('');
    setAliasName('');
    setIsShowName(false);
    setIsEditName(false);
    setContinueEnable(false);
  };

  const showInfoReadOnly = () => {
    setIsShowName(true);
    setIsEditName(false);
    setContinueEnable(true);
  };

  const showInfoEditable = () => {
    setIsShowName(true);
    setIsEditName(true);
  };

  //SET DATA
  const setInfoReadOnly = (fullName: string) => {
    setIsShowName(true);
    setInputName(fullName ?? '');
  };

  return {
    category,
    providerSelected,
    setProviderSelected,
    accNumber,
    setAccNumber,
    billContact,
    setBillContact,
    inputName,
    setInputName,
    aliasName,
    setAliasName,
    isShowName,
    showInfoEditable,
    isEditName,
    typing,
    resetInfo,
    setTyping,
    isEnableAutomatic,
    setEnableAutomatic,
    setStopHandleBeneficiary,
    continueEnable,
    setContinueEnable,
    handleBillContract,
    onSubmit,
    goHome,
    providerSelectionRef,
    disableProviderSelection,
    onSelectProviderItem,
    defaultSelectedProvider,
    onBlurAccountNumber,
    inputBillNumberRef,
  };
};

export default useSaveContact;
