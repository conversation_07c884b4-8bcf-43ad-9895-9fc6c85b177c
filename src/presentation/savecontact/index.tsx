import {<PERSON>tonT<PERSON>, ColorField, MSBButton, MSBIcons, MSBInputBase, MSBPage, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Keyboard, Pressable, View} from 'react-native';

import MSBProviderSelection from '../../components/provider-selection/index.tsx';
import TransferAccountNumberInput from '../../components/transfer-account-number-input/index.tsx';
import {translate} from '../../locales/i18n.ts';
import Utils from '../../utils/Utils.ts';
import useSaveContact from './hook.ts';

// import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel.ts';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel.ts';
import {makeStyle} from './style.ts';

const SaveBillContactScreen = () => {
  const hook = useSaveContact();
  const {styles} = useMSBStyles(makeStyle);

  // const handleSelectBankItem = useCallback((bankItem: ProviderModel) => {
  //   hook.setStopHandleBeneficiary(false);
  //   hook.setProviderSelected(bankItem);
  //   hook.resetInfo();
  // }, []);

  return (
    <View style={styles.container}>
      <MSBPage
        headerProps={{
          title: translate('addContact.title'),
          hasBack: true,
          rightButtons: [
            {
              icon: MSBIcons.IconHome,
              onPress: hook.goHome,
            },
          ],
        }}
        style={styles.container}>
        <View style={styles.container}>
          <Pressable style={styles.body} onPress={Keyboard.dismiss}>
            <View style={styles.card}>
              <MSBProviderSelection
                code={(hook.category as CategoryModel)?.id}
                ref={hook.providerSelectionRef}
                defaultValue={hook.defaultSelectedProvider}
                disabled={hook.disableProviderSelection}
                onSelected={hook.onSelectProviderItem}
              />
              <View style={styles.space} />
              <TransferAccountNumberInput
                testID={'transfer.saveContactScreen.enterAccountNumber'}
                label={translate('paymentBill.labelInputAccNumber')}
                value={hook.accNumber}
                placeholder={translate('paymentBill.hintInputAccNumber')}
                maxLength={34}
                onChangeText={(text: string) => {
                  const normalizeText = Utils.regexAccountNumberInput(text);
                  hook.setAccNumber(normalizeText);
                  hook.setTyping(true);
                  hook.setBillContact(null);
                }}
                onBlur={async () => {
                  await hook.handleBillContract();
                }}
              />
              {hook.isShowName && (
                <View>
                  <View style={styles.space} />
                  <MSBInputBase
                    // containerStyle={styles.space}
                    testID={'transfer.beneficiaryScreen.enterBeneficiaryName'}
                    label={translate('addContact.customerName')}
                    value={(hook.billContact?.getCustomerName() ?? hook.inputName).toLocaleUpperCase()}
                    disabled={true}
                    isDisableRemoveIcon={true}
                    backgroundColorInput={ColorField.SurfaceDisable}
                  />
                  <View style={styles.space} />
                  <MSBInputBase
                    testID={'transfer.saveContactScreen.enterAliasName'}
                    label={translate('addContact.labelAlias')}
                    value={hook.aliasName}
                    onChangeText={(text: string) => {
                      hook.setAliasName(Utils.removeEmoji(text));
                    }}
                    onBlur={() => {
                      hook.setAliasName(Utils.regexNickName(hook.aliasName));
                    }}
                    placeholder={translate('addContact.contentAlias')}
                    maxLength={80}
                    textInputProps={{
                      keyboardType: 'ascii-capable',
                      returnKeyLabel: 'Done',
                      returnKeyType: 'done',
                      autoCapitalize: 'sentences',
                    }}
                  />
                </View>
              )}
              {/* <View style={styles.space} />
              {hook.isShowName && (
                <View style={styles.favorite}>
                  <View style={styles.leftFavorite}>
                    <MSBTextBase style={Tpg.base_medium} content={translate('addContact.enableAutomatic')} />
                  </View>
                  <View style={{paddingTop: SizeGlobal.Size100}}>
                    <ToggleSwitch isFavorite={hook.isEnableAutomatic} setFavorite={hook.setEnableAutomatic} />
                  </View>
                </View>
              )} */}
            </View>
            <View style={styles.container} />
            <MSBButton
              testID={'transfer.saveContactScreen.pressToContinue'}
              buttonType={ButtonType.Primary}
              label={translate('addContact.btnContinue')}
              disabled={!hook.continueEnable}
              onPress={hook.onSubmit}
              style={styles.bottomSpace}
            />
          </Pressable>
        </View>
      </MSBPage>
    </View>
  );
};

export default SaveBillContactScreen;
