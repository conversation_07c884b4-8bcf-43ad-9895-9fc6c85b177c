import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({SizeA<PERSON>s, Shadow, ColorField, SizeField, SizeGlobal}) => {
  return {
    body: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    bottomSpace: {
      marginBottom: SizeAlias.SpacingLarge,
    },
    card: {
      backgroundColor: 'white',
      borderRadius: SizeAlias.Radius3,
      flexDirection: 'column',
      justifyContent: 'space-between',
      paddingVertical: SizeAlias.SpacingSmall,
      padding: SizeAlias.SpacingSmall,
      ...Shadow.center,
    },
    container: {
      flex: 1,
    },
    favorite: {
      borderColor: ColorField.BorderDefault,
      borderRadius: SizeField.BorderRadius,
      borderWidth: SizeField.BorderStroke,
      flexDirection: 'row',
      justifyContent: 'space-between',
      minHeight: 40,
      paddingHorizontal: SizeGlobal.Size400,
    },
    leftFavorite: {
      flexWrap: 'wrap',
      justifyContent: 'center',
      width: '85%',
    },
    logo: {
      height: 32,
      marginRight: SizeAlias.SpacingSmall,
      resizeMode: 'contain',
      width: 32,
    },
    space: {
      height: SizeAlias.SpacingXSmall,
    },
  };
});
