import {createMSBStyleSheet, getSize} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({SizeAlias, Shadow, ColorField, SizeField, SizeGlobal}) => {
  return {
    body: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    bottomSpace: {
      marginBottom: SizeAlias.SpacingLarge,
    },
    card: {
      backgroundColor: 'white',
      borderRadius: SizeAlias.Radius3,
      flexDirection: 'column',
      justifyContent: 'space-between',
      paddingVertical: SizeAlias.SpacingSmall,
      padding: SizeAlias.SpacingSmall,
      ...Shadow.center,
    },
    container: {
      flex: 1,
    },
    favorite: {
      borderColor: ColorField.BorderDefault,
      borderRadius: SizeField.BorderRadius,
      borderWidth: SizeField.BorderStroke,
      flexDirection: 'row',
      justifyContent: 'space-between',
      minHeight: getSize(40),
      paddingHorizontal: SizeGlobal.Size400,
    },
    leftFavorite: {
      flexWrap: 'wrap',
      justifyContent: 'center',
      width: '85%',
    },
    logo: {
      height: SizeGlobal.Size800,
      marginRight: SizeAlias.SpacingSmall,
      resizeMode: 'contain',
      width: SizeGlobal.Size800,
    },
    space: {
      height: SizeAlias.SpacingXSmall,
    },
  };
});
