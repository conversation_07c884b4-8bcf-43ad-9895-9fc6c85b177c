import {createMSBStyleSheet, getSize} from 'msb-shared-component';
import DimensionUtils from '../../utils/DimensionUtils';

export const makeStyle = createMSBStyleSheet(
  ({ColorGlobal, SizeGlobal, ColorField, ColorAlias, ColorDataView, SizeAlias, Shadow, Typography}) => {
    return {
      beneficiaryInfo: {
        alignItems: 'center',
        flexDirection: 'row',
        // height: 32,
      },
      bottomSpace: {
        // marginBottom: DimensionUtils.getPaddingBottomByDevice(),
        marginHorizontal: SizeAlias.SpacingSmall,
      },
      container: {
        flex: 1,
      },
      bottomSheetContentContainer: {flexDirection: 'column', flex: 1, height: 400},
      loadingContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        backgroundColor: 'black',
        opacity: 0.5,
        width: '100%',
        height: '100%',
      },
      contentContainer: {
        backgroundColor: ColorGlobal.NeutralWhite,
        borderRadius: SizeAlias.Radius3,
        marginTop: SizeAlias.SpacingLarge,
        padding: SizeAlias.SpacingSmall,
        ...Shadow.center,
      },
      iconContact: {
        height: SizeGlobal.Size600,
        width: SizeGlobal.Size600,
      },

      logo: {
        height: SizeGlobal.Size800,
        marginRight: SizeAlias.SpacingSmall,
        resizeMode: 'stretch',
        width: SizeGlobal.Size800,
      },
      marginTop20: {
        marginTop: SizeAlias.SpacingMedium,
      },
      scrollViewContentContainer: {
        paddingBottom: SizeAlias.Spacing3xSmall,
        paddingHorizontal: SizeAlias.SpacingSmall,
      },
      space: {
        height: SizeAlias.SpacingXSmall,
      },
      suggestionList: {
        backgroundColor: ColorGlobal.NeutralWhite,
        borderRadius: SizeGlobal.Size300,
        left: 0,
        overflow: 'visible',
        position: 'absolute',
        right: 0,
        top: getSize(94),
        zIndex: 999,
        ...Shadow.center,
      },

      zheight: {
        zIndex: 999,
      },
    };
  },
);
