import {createMSBStyleSheet, getSize} from 'msb-shared-component';
import DimensionUtils from '../../utils/DimensionUtils';

export const makeStyle = createMSBStyleSheet(
  ({ColorGlobal, SizeGlobal, ColorField, ColorAlias, ColorDataView, SizeAlias, Shadow, Typography}) => {
    return {
      beneficiaryInfo: {
        alignItems: 'center',
        flexDirection: 'row',
        // height: 32,
      },
      bottomSpace: {
        // marginBottom: DimensionUtils.getPaddingBottomByDevice(),
        marginHorizontal: SizeAlias.SpacingSmall,
      },
      container: {
        flex: 1,
      },
      bottomSheetContentContainer: {flexDirection: 'column', flex: 1, height: 400},
      loadingContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        backgroundColor: 'black',
        opacity: 0.5,
        width: '100%',
        height: '100%',
      },
      contentContainer: {
        backgroundColor: ColorGlobal.NeutralWhite,
        borderRadius: 12,
        marginTop: 24,
        padding: 16,
        ...Shadow.center,
      },
      iconContact: {
        height: 24,
        width: 24,
      },

      logo: {
        height: 30,
        marginRight: 16,
        resizeMode: 'stretch',
        width: 30,
      },
      marginTop20: {
        marginTop: 20,
      },
      scrollViewContentContainer: {
        paddingBottom: 50,
        paddingHorizontal: 16,
      },
      space: {
        height: 12,
      },
      suggestionList: {
        backgroundColor: ColorGlobal.NeutralWhite,
        borderRadius: SizeGlobal.Size300,
        left: 0,
        overflow: 'visible',
        position: 'absolute',
        right: 0,
        top: getSize(94),
        zIndex: 999,
        ...Shadow.center,
      },

      zheight: {
        zIndex: 999,
      },
    };
  },
);
