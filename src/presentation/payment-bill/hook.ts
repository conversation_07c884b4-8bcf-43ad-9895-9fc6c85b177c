import {NavigationProp, RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {useCallback, useEffect, useRef, useState} from 'react';
import {Keyboard, TextInput} from 'react-native';
import ScreenNames from '../../commons/ScreenNames.ts';
import Utils from '../../utils/Utils.ts';
import {DIContainer} from '../../di/DIContainer.ts';
import {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {showCommonPopup, showErrorPopup} from '../../utils/PopupUtils.ts';
import {hostSharedModule} from 'msb-host-shared-module';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {ACCOUNT_TYPE, SafeAny} from '../../commons/Constants.ts';
import {IBillContact} from '../../domain/entities/IBillContact.ts';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel.ts';
import {ProviderSelectionRef, ProviderSelectionState} from '../../components/provider-selection/types.ts';
import useCombineLatest from './hooks/useCombineLatest.ts';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel.ts';
import {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest.ts';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel.ts';
import {PaymentInfoModel} from '../../navigation/types.ts';
import {translate} from '../../locales/i18n.ts';

const usePaymentBill = (
  renderContactTab: (
    contactList: IBillContact[] | undefined | null,
    recentContact: IBillContact[] | undefined | null,
    onSelectContact: (contactList?: IBillContact) => void,
  ) => React.ReactNode,
) => {
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentBillScreen'>>();
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentBillScreen'>>();
  const {category} = route.params || {};

  const [providerList, setProviderList] = useState<ProviderModel[]>([]); // dánh sách ngân hàng
  const [providerSelected, setProviderSelected] = useState<ProviderModel | null>(null); // ngân hàng được chọn
  const [defaultSelectedProvider, setDefaultSelectedProvider] = useState<ProviderSelectionState>();
  const [accNumber, setAccNumber] = useState<string>(''); // số tài khoản số thẻ
  const [paymentBill, setPaymentBill] = useState<GetBillDetailModel | undefined>(undefined); // thông tin hoá đơn
  const [inputName, setInputName] = useState<string>(''); // nhập tên người nhận: citab
  const [isShowInputName, setShowInputName] = useState<boolean>(false); // show textinput tên người nhận
  const {values, updaters, isComplete} = useCombineLatest<ProviderModel | null | string>([null, '']);
  const [updateProviderSelect, updateAccountNumber] = updaters;
  const [filteredSuggestions, setFilteredSuggestions] = useState<IBillContact[]>([]);
  const [typing, setTyping] = useState<boolean>(false);
  const [disableProviderSelection, setDisableProviderSelection] = useState<boolean>(false);
  const [itemHeight, setItemHeight] = useState<number>(60);
  const [errorContent, setErrorContent] = useState<string | undefined>(); // nhập tên người nhận: citab
  const inputBillNumberRef = useRef<TextInput>(null);
  const providerSelectionRef = useRef<ProviderSelectionRef>(null);

  const suggestionListHeight = useSharedValue(0);
  const paddingBottomSuggestionList = useSharedValue(0);
  const animatedStyle = useAnimatedStyle(() => ({
    height: suggestionListHeight.value,
    opacity: suggestionListHeight.value > 0 ? 1 : 0,
  }));

  const handleMeasureItemHeight = (height: number) => {
    if (height > 0 && height !== itemHeight) {
      setItemHeight(height);
    }
  };

  useEffect(() => {
    if (!accNumber && providerSelected) {
      handleNumberBeneficiaryRefFocus(); // khi Chọn nhà cung cấp thụ hưởng -> focus vào nhập số tài khoản
    }
  }, [accNumber, providerSelected]);

  useEffect(() => {
    if (accNumber && false) {
      //TODO: implement suggesstion logic here
      if (typing) {
        const filtered = [].filter(suggestionSelect => {});
        const height = filtered.length > 0 ? Math.min(filtered.length * (itemHeight + 10), 300) : 0;
        setFilteredSuggestions(filtered);
        suggestionListHeight.value = withTiming(height, {duration: 300});
        paddingBottomSuggestionList.value = withTiming(height, {duration: 300});
      } else {
        setFilteredSuggestions([]);
        suggestionListHeight.value = withTiming(0, {duration: 300});
        paddingBottomSuggestionList.value = withTiming(0, {duration: 300});
      }
    } else {
      setFilteredSuggestions([]);
      suggestionListHeight.value = withTiming(0, {duration: 300});
      paddingBottomSuggestionList.value = withTiming(0, {duration: 300});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accNumber, typing, itemHeight]);

  const onContinue = () => {
    const paymentInfo: PaymentInfoModel = {
      title: `${translate('paymentBill.title')} ${(category as CategoryModel)?.categoryName ?? ''}`,
      categoryName: (category as CategoryModel)?.categoryName ?? '',
      billInfo: paymentBill,
      contractName: paymentBill?.billList?.[0]?.custName,
      provider: providerSelected,
    };

    navigation.navigate(ScreenNames.PaymentInfoScreen, {paymentInfo});
  };

  // gọi api lấy thông tin hoá đơn
  const getPaymentBill = useCallback(async () => {
    const request: GetBillDetailRequest = {
      billCode: accNumber,
      serviceCode: providerSelected?.serviceCode ?? '',
      accountingType: ACCOUNT_TYPE.ACCT,
    };
    const result = await DIContainer.getInstance().getGetBillDetailUseCase().execute(request);
    if (result.status === 'ERROR') {
      setInputName('');
      setShowInputName(false);
      setPaymentBill(undefined);
      // setErrorContent('Không tìm thấy thông tin hoá đơn');
      showErrorPopup(result.error);
      return;
    }
    setErrorContent(undefined);
    setPaymentBill(result.data);
    setInputName(result.data?.billList?.[0].custName ?? '');
    setShowInputName(true);
    const paymentInfo: PaymentInfoModel = {
      title: `${translate('paymentBill.title')} ${(category as CategoryModel)?.categoryName.toLocaleLowerCase() ?? ''}`,
      categoryName: (category as CategoryModel)?.categoryName ?? '',
      billInfo: result.data,
      contractName: result.data?.billList?.[0]?.custName,
      provider: providerSelected,
    };

    navigation.navigate(ScreenNames.PaymentInfoScreen, {paymentInfo});
  }, [accNumber, category, navigation, providerSelected]);

  // khi out focus input: Nhập số tài khoản/số thẻ
  const onBlurAccountNumber = () => {
    updateAccountNumber(accNumber);
    updateProviderSelect(providerSelected);
    inputBillNumberRef.current?.blur();
    setTyping(false);
  };

  // trở về màn hình home
  const goHome = () => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: 'WARNING',
      title: translate('paymentConfirm.endOfTransaction'),
      content: translate('paymentConfirm.endOfTransactionDescription'),
      cancelBtnText: translate('paymentConfirm.endOfTransaction'),
      confirmBtnText: translate('paymentConfirm.close'),
      onCancel: () =>
        navigation?.reset({
          index: 0,
          routes: [
            {
              name: 'SegmentStack',
            },
          ],
        }),
    });
  };

  // mở bottomsheet: danh bạ đã lưu
  const selectContactTab = () => {
    Keyboard.dismiss();
    hostSharedModule.d.domainService?.showBottomSheet({
      header: 'Người thụ hưởng',
      children: renderContactTab(null, null, onSelectContact), //TODO: handle bill list
      snapToIndex: 80,
    });
  };

  const onSelectContact = (contactInfo?: IBillContact) => {
    hostSharedModule.d.domainService?.hideBottomSheet();
    console.log('data, ', contactInfo);
    const provider = providerList.find(b => b.partnerCode === contactInfo?.getId());
    if (!provider) {
      return;
    }

    setTyping(false);
    setAccNumber(contactInfo?.getId() ?? '');
    setProviderSelected(provider);
    updateProviderSelect(provider);
    updateAccountNumber(contactInfo?.getId() ?? '');

    setDefaultSelectedProvider({
      providerSelected: provider,
    });
  };

  // Chọn nhà cung cấp thụ hưởng
  const selectProviderDetail = (item: ProviderModel) => {
    hostSharedModule.d.domainService?.hideBottomSheet();
    if (!Utils.isEmpty(accNumber)) {
      console.log('Chon lai provider');
      setProviderSelected(item);
      updateProviderSelect(item);
    } else {
      setProviderSelected(item);
      updateProviderSelect(item);
      if (Utils.isEmpty(accNumber)) {
        handleNumberBeneficiaryRefFocus(); // khi Chọn nhà cung cấp thụ hưởng -> focus vào nhập số tài khoản
      }
    }
  };

  // Chọn số tài khoản/số thẻ: Trong danh sách gợi ý
  const onSelectSuggestedAccount = (suggestionSelect?: IBillContact) => {
    const provider = providerList.find(b => b.partnerCode === suggestionSelect?.getPartnerCode());
    if (!provider) {
      return;
    }

    setTyping(false);
    setAccNumber(suggestionSelect?.getId() ?? '');
    setProviderSelected(provider);

    setDefaultSelectedProvider({
      providerSelected: provider,
    });
    setTimeout(() => inputBillNumberRef.current?.blur(), 500);
  };

  // khi Chọn nhà cung cấp thụ hưởng -> focus vào nhập số tài khoản
  const handleNumberBeneficiaryRefFocus = () => {
    if (inputBillNumberRef.current) {
      inputBillNumberRef.current.focus();
    }
  };

  // onChangeText: Nhập số tài khoản/số thẻ
  const onChangeAccountNo = (text: string) => {
    setAccNumber(text);
  };

  // get list tài khoản nguồn
  // const getSourceAccountList = useCallback(async () => {
  //   const result = await DIContainer.getInstance().getSourceAccountListUseCase().execute();
  //   if (result.status === 'ERROR') {
  //     showCommonPopup(result.error);
  //     return;
  //   }
  //   const sourceAccount: SourceAccountModel[] = (result?.data?.data ?? []).filter(
  //     item => item?.userPreferences?.visible !== false,
  //   );
  //   setSourceAcc(sourceAccount);
  // }, []);

  // get danh sách nhà cung cấp
  const getProviderList = useCallback(async () => {
    const request = {code: (category as CategoryModel)?.id};
    console.log('request params category', category);
    const result = await DIContainer.getInstance().getProviderListUseCase().execute(request);
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
      return;
    }
    if (result.data?.length === 1) {
      setProviderSelected(result.data[0]);
      setDefaultSelectedProvider({
        providerSelected: result.data[0],
      });
      setDisableProviderSelection(true);
    } else {
      setProviderList(result.data ?? []);
      setDisableProviderSelection(false);
    }
  }, [category]);

  // get first data
  useEffect(() => {
    getProviderList();
    // getSourceAccountList();
  }, []);

  useEffect(() => {
    console.log('values----------->>>', values);
    if (!Utils.isEmpty(values)) {
      const [newProviderSelected, newAccNumber] = values as SafeAny;
      console.log('isComplete ==', isComplete);
      console.log('newProviderSelected ==', newProviderSelected);
      console.log('newAccNumber ==', newAccNumber);
      console.log(
        '!providerSelectionRef.current?.isBottomSheetOpen ==',
        !providerSelectionRef.current?.isBottomSheetOpen,
      );
      if (isComplete && newProviderSelected && newAccNumber && !providerSelectionRef.current?.isBottomSheetOpen) {
        getPaymentBill();
      }
    }
  }, [values, isComplete, providerSelectionRef.current?.isBottomSheetOpen]);

  const onSelectProviderItem = useCallback(
    (item: ProviderModel) => {
      setProviderSelected(item);
      updateProviderSelect(item);
    },
    [updateProviderSelect],
  );

  return {
    selectContactTab,
    providerSelectionRef,
    onSelectProviderItem,
    defaultSelectedProvider,
    onBlurAccountNumber,
    handleMeasureItemHeight,
    onSelectSuggestedAccount,
    goHome,
    onChangeAccountNo,
    setInputName,
    onContinue,
    setTyping,
    //ref
    inputBillNumberRef,
    selectProviderDetail,

    // state
    disableProviderSelection,
    errorContent,
    inputName,
    isShowInputName,
    animatedStyle,
    filteredSuggestions,
    paddingBottomSuggestionList,
    paymentBill,
    providerSelected,
    accNumber,
  };
};

export default usePaymentBill;
