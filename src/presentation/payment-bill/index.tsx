import {<PERSON><PERSON><PERSON><PERSON>, <PERSON>Field, MSBButton, MSBInputBase, MSBPage, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import Animated from 'react-native-reanimated';

import {translate} from '../../locales/i18n.ts';
import Utils from '../../utils/Utils.ts';
import MSBProviderSelection from '../../components/provider-selection/index.tsx';
import TransferAccountNumberInput from '../../components/transfer-account-number-input/index.tsx';
import BillContainerTab from '../payment-home/components/BillContainerTab.tsx';
import usePaymentBill from './hook.ts';
import {useRoute, RouteProp} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel.ts';
import {makeStyle} from './style.ts';
import {BottomSheetView} from '@gorhom/bottom-sheet';

const PaymentBillScreen = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentBillScreen'>>();
  const {category} = route.params || {};
  const {styles} = useMSBStyles(makeStyle);

  const {
    providerSelectionRef,
    defaultSelectedProvider,
    onSelectProviderItem,
    onBlurAccountNumber,
    onChangeAccountNo,
    onContinue,
    setTyping,
    //ref
    inputBillNumberRef,
    selectContactTab,
    goHome,
    // state
    disableProviderSelection,
    errorContent,
    inputName,
    isShowInputName,
    paddingBottomSuggestionList,
    paymentBill,
    providerSelected,
    accNumber,
  } = usePaymentBill(() => (
    <BottomSheetView>
      <View style={styles.bottomSheetContentContainer}>
        <BillContainerTab isBlocked={false} />
      </View>
    </BottomSheetView>
  ));

  return (
    <View style={styles.container}>
      <MSBPage
        isScrollable={false}
        headerProps={{
          hasBack: true,
          rightButtons: [{icon: 'tone-home', onPress: goHome}],
          title: `${translate('paymentBill.title')} ${(category as CategoryModel).categoryName.toLocaleLowerCase()}`,
        }}
        style={styles.container}>
        {/* <ImageBackground source={Images.bgMain} style={styles.container}> */}
        <View style={styles.container}>
          <View style={styles.container}>
            <KeyboardAwareScrollView
              style={styles.container}
              contentContainerStyle={styles.scrollViewContentContainer}
              enableOnAndroid={true}
              enableAutomaticScroll={true}
              keyboardShouldPersistTaps="handled"
              extraScrollHeight={20}
              enableResetScrollToCoords={false}>
              <Animated.View style={{paddingBottom: paddingBottomSuggestionList}}>
                <View style={styles.contentContainer}>
                  <MSBProviderSelection
                    code={(category as CategoryModel)?.id}
                    ref={providerSelectionRef}
                    defaultValue={defaultSelectedProvider}
                    disabled={disableProviderSelection}
                    onSelected={onSelectProviderItem}
                  />
                  <Animated.View pointerEvents="box-none" style={styles.zheight}>
                    <TransferAccountNumberInput
                      inputRef={inputBillNumberRef}
                      testID={'transfer.beneficiaryScreen.enterNumberBeneficiary'}
                      label={translate('paymentBill.labelInputAccNumber')}
                      value={accNumber}
                      onChangeText={(text: string) => {
                        console.log('text--------------------------------------->', text);
                        onChangeAccountNo(text);
                        setTyping(true);
                      }}
                      onFocus={() => {
                        setTyping(true);
                      }}
                      maxLength={255}
                      onBlur={onBlurAccountNumber}
                      containerStyle={styles.marginTop20}
                      errorContent={errorContent}
                      placeholder={translate('paymentBill.hintInputAccNumber')}
                      childrenIconRight={
                        null
                        // <MSBIcon
                        //   folderIcon={MSBFolderImage.ICON_SVG}
                        //   icon={'tone-bill'}
                        //   // iconSize={MSBIconSize.SIZE_40}
                        //   onIconClick={hostSharedModule.d.domainService.undevelopedFeature}
                        //   styleContainer={styles.iconContact}
                        // />
                      }
                    />
                  </Animated.View>
                  {(inputName || isShowInputName) && (
                    <MSBInputBase
                      containerStyle={styles.marginTop20}
                      testID={'transfer.beneficiaryScreen.enterBeneficiaryName'}
                      value={inputName.toUpperCase()}
                      disabled={true}
                      isDisableRemoveIcon={true}
                      backgroundColorInput={ColorField.SurfaceDisable}
                    />
                  )}
                </View>
              </Animated.View>
            </KeyboardAwareScrollView>
            <MSBButton
              testID={'transfer.beneficiaryScreen.pressToContinue'}
              buttonType={ButtonType.Primary}
              label={translate('paymentBill.btnContinue')}
              disabled={Utils.isEmpty(providerSelected) || Utils.isEmpty(accNumber) || Utils.isEmpty(paymentBill)}
              onPress={onContinue}
              style={styles.bottomSpace}
            />
          </View>
        </View>
      </MSBPage>
    </View>
  );
};

export default PaymentBillScreen;
