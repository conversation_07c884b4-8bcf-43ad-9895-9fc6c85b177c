/* eslint-disable react-hooks/rules-of-hooks */
import {useState, useRef, useCallback} from 'react';

type Updater<T> = (value: T) => void;

function useCombineLatest<T>(initialValues: T[]): {
  values: T[] | null;
  updaters: Updater<T>[];
  isComplete: boolean;
} {
  const [combinedValues, setCombinedValues] = useState<T[]>(initialValues);
  const emittedRef = useRef<boolean[]>(Array(initialValues.length).fill(false));
  const [isComplete, setIsComplete] = useState(false);

  const updaters: Updater<T>[] = initialValues.map((_, index) =>
    useCallback(
      (newValue: T) => {
        setCombinedValues(prev => {
          const newValues = [...prev];
          newValues[index] = newValue;
          return newValues;
        });

        emittedRef.current[index] = true;

        const allEmitted = emittedRef.current.every(Boolean);
        if (allEmitted && !isComplete) {
          setIsComplete(true);
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [index, isComplete],
    ),
  );

  return {
    values: isComplete ? combinedValues : null,
    updaters,
    isComplete,
  };
}

export default useCombineLatest;
