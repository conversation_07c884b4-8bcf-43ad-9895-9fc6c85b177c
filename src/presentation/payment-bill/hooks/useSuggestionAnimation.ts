import {useSharedValue, withTiming, useAnimatedStyle} from 'react-native-reanimated';

export const useSuggestionAnimation = (itemHeight: number) => {
  const suggestionListHeight = useSharedValue(0);
  const paddingBottomSuggestionList = useSharedValue(0);

  const updateSuggestionListHeight = (count: number) => {
    const height = count > 0 ? Math.min(count * (itemHeight + 10), 300) : 0;
    suggestionListHeight.value = withTiming(height, {duration: 300});
    paddingBottomSuggestionList.value = withTiming(height, {duration: 300});
  };

  const animatedStyle = useAnimatedStyle(() => ({
    height: suggestionListHeight.value,
    opacity: suggestionListHeight.value > 0 ? 1 : 0,
  }));

  return {
    animatedStyle, // <<< Giữ tên này
    updateSuggestionListHeight,
    paddingBottomSuggestionList,
  };
};
