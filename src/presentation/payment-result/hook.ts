import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';
import {useMemo, useRef, useState} from 'react';
import Share from 'react-native-share';
import {captureRef} from 'react-native-view-shot';
import {LayoutChangeEvent} from 'react-native';

import ScreenNames from '../../commons/ScreenNames';
import {ActionProps} from './components/transfer-result-action/types';
import {PAYMENT_TYPE, TRANSFER_RESULT_ACTION} from '../../commons/Constants';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {hostSharedModule} from 'msb-host-shared-module';
import DimensionUtils from '../../utils/DimensionUtils';
import {AccountModel, MyBillContactModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';

const usePaymentResult = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentResultScreen'>>();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentResultScreen'>>();

  const {paymentInfo} = route.params;
  console.log('PaymentResultScreen', paymentInfo);
  const [heightViewshot, setHeightViewshot] = useState<number>(DimensionUtils.getWindowHeight());

  const totalAmount = useMemo(() => {
    if (
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT
    ) {
      return paymentInfo.paymentValidate.transferTransactionInformation?.instructedAmount?.amount;
    }
    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);
    return _totalAmount;
  }, [paymentInfo]);

  const viewRef = useRef(null);

  const isTopup = paymentInfo.provider?.isTopup();

  const goTransferResultDetail = () => {};

  const transferAction = (item: ActionProps) => {
    console.log('transferAction', item);
    switch (item.type) {
      case TRANSFER_RESULT_ACTION.TRANSFER_SHARE:
        transferShare();
        break;
      case TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE:
        transferSaveTemplate();
        break;
      case TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY:
        transferSaveBeneficiary();
        break;
      case TRANSFER_RESULT_ACTION.TRANSFER_MANAGER:
        transferManager();
        break;
      case TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT:
        transferSupport();
        break;
      default:
        break;
    }
  };

  const transferShare = async () => {
    try {
      const uri = await captureRef(viewRef, {
        format: 'jpg',
        quality: 0.8,
      });

      const options = {
        title: 'Chia sẻ giao dịch',
        message: '',
        url: uri,
        type: 'image/png',
      };
      await Share.open(options);
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const transferSaveTemplate = () => {
    hostSharedModule.d.domainService.undevelopedFeature();
  };
  const transferSaveBeneficiary = () => {
    const contact = new MyBillContactModel('', paymentInfo?.contractName, '', paymentInfo?.categoryName, '', '', [
      new AccountModel(
        paymentInfo?.provider?.subgroupNameVn,
        paymentInfo?.billInfo?.billCode,
        paymentInfo?.provider?.subGroupId?.toString(),
        paymentInfo.provider?.categoryCode,
        paymentInfo.provider?.serviceCode,
      ),
    ]);
    console.log('contact=====-----===', contact);
    navigation.navigate(ScreenNames.EditBillContactScreen, {contact: contact});
  };
  const transferManager = () => {
    hostSharedModule.d.domainService.undevelopedFeature();
  };
  const transferSupport = () => {
    hostSharedModule.d.domainService.undevelopedFeature();
  };

  const otherTransactions = () => {
    navigation.reset({
      index: 2,
      routes: [
        {name: 'BottomTabs'},
        {
          name: 'PaymentStack',
        },
      ],
    });
  };

  const handleLayoutViewshot = (event: LayoutChangeEvent) => {
    const {height} = event.nativeEvent.layout;
    setHeightViewshot(height);
  };

  const goHome = () => {
    navigation?.reset({
      index: 0,
      routes: [
        {
          name: 'SegmentStack',
        },
      ],
    });
  };

  const goPaymentResultDetail = () => {
    navigation.navigate(ScreenNames.PaymentResultDetailScreen, {paymentInfo});
  };

  return {
    goTransferResultDetail,
    otherTransactions,
    transferAction,
    viewRef,
    goHome,
    paymentInfo,
    goPaymentResultDetail,
    totalAmount,
    handleLayoutViewshot,
    heightViewshot,
    isTopup,
  };
};

export type Props = ReturnType<typeof usePaymentResult>;

export default usePaymentResult;
