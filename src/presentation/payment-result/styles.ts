import {ColorGlobal, createMSBStyleSheet, getSize, SizeGlobal} from 'msb-shared-component';

import DimensionUtils from '../../utils/DimensionUtils';

export const makeStyle = createMSBStyleSheet(({<PERSON><PERSON><PERSON><PERSON><PERSON>, ColorField, ColorDataView, Shadow, Typography}) => {
  return {
    alignItemsCenter: {
      alignItems: 'center',
    },
    btnConfirm: {
      marginHorizontal: SizeAlias.SpacingXMSmall,
    },

    container: {
      flex: 1,
      // paddingHorizontal: SizeAlias.SpacingSmall,
      // paddingVertical: SizeAlias.Spacing2xSmall,
    },
    scrollViewContainer: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.Spacing2xSmall,
    },
    containerViewshot: {
      flex: 1,
      height: DimensionUtils.getWindowHeight(),
      paddingBottom: SizeAlias.SpacingSmall,
      paddingHorizontal: SizeAlias.SpacingSmall,
      width: DimensionUtils.getWindowWidth(),
      justifyContent: 'center',
    },
    contentContainer: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXMSmall,
      padding: SizeAlias.SpacingSmall,
      overflow: 'hidden',
      ...Shadow.center,
    },
    contentContainerView: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXMSmall,
      ...Shadow.center,
    },
    // render image shot
    contentContainerViewShot: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.SpacingXMSmall,
      marginBottom: SizeAlias.SpacingSmall,
      padding: SizeAlias.SpacingSmall,
      opacity: 0.9,
    },
    contentViewShot: {
      borderRadius: SizeAlias.SpacingXMSmall,
    },
    flex1: {
      flex: 1,
    },
    icLogo: {
      marginTop: SizeAlias.SpacingLarge,
      marginBottom: SizeAlias.SpacingSmall,
      resizeMode: 'stretch',
      height: getSize(56),
      width: getSize(156),
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch',
    },
    logoShot: {height: getSize(35), marginBottom: SizeAlias.SpacingLarge, resizeMode: 'contain', width: getSize(150)},
    marginTop16: {
      marginTop: SizeAlias.SpacingSmall,
      backgroundColor: 'transparent',
    },

    sender: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    senderName: {
      ...Typography?.base_semiBold,
      color: ColorDataView.TextMain,
      marginLeft: 12,
    },
    shotView: {
      left: -10000,
      position: 'absolute',
      height: DimensionUtils.getWindowHeight(),
      width: DimensionUtils.getWindowWidth(),
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    },
    transferResultAction: {
      borderTopColor: ColorField.BorderDefault,
      borderTopWidth: 1,
      marginTop: SizeAlias.SpacingXSmall,
    },
    txtName: {
      ...Typography?.base_semiBold,
      color: ColorDataView.TextMain,
    },
    txtTitle: {
      ...Typography?.small_regular,
      // ...Tpg.base_semiBold,
      color: ColorDataView.TextSub,
      marginBottom: SizeAlias.Spacing4xSmall,
    },
    txtValue: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },
    top16: {
      marginTop: SizeAlias.SpacingSmall,
    },
    accountInfo: {
      marginTop: SizeGlobal.Size500,
    },
  };
});
