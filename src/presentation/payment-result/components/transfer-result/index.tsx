// for test

import React from 'react';
import {View} from 'react-native';

// import Images from '../../../../assets/images/Images';
import {translate} from '../../../../locales/i18n';
import FormatUtils from '../../../../utils/FormatUtils';
import {TransferResultProps} from './types';
import {
  createMSBStyleSheet,
  getSize,
  MSBFastImage,
  MSBFolderImage,
  MSBTextBase,
  SizeGlobal,
  useMSBStyles,
} from 'msb-shared-component';
import {PAYMENT_ORDER_STATUS, PAYMENT_TYPE} from '../../../../commons/Constants';

const TransferResultView = ({resultType, style, amount, isDetail, date, paymentType}: TransferResultProps) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  const getTitleStyle = () => {
    let titleStyle = {};
    switch (resultType) {
      case PAYMENT_ORDER_STATUS.PROCESSED:
        titleStyle = {
          // color: '#12B76A',
          color: theme.ColorAlias.TextSuccess,
        };
        break;
      case PAYMENT_ORDER_STATUS.ACCEPTED:
        titleStyle = {
          // color: '#2E90FA',
          color: theme.ColorAlias.TextInformation,
        };
        break;
      case PAYMENT_ORDER_STATUS.REJECTED:
        titleStyle = {
          // color: '#F04438',
          color: theme.ColorAlias.TextError,
        };
        break;
      default:
        titleStyle = {
          // color: '#F04438',
          color: theme.ColorAlias.TextError,
        };
        break;
    }
    return [styles.txtTitleResultTransfer, titleStyle];
  };

  const getIconResult = () => {
    let image: string | {uri: string} = 'result-success';
    switch (resultType) {
      case PAYMENT_ORDER_STATUS.PROCESSED:
        image = 'result-success';
        break;
      case PAYMENT_ORDER_STATUS.ACCEPTED:
        image = 'result-pending';
        break;
      case PAYMENT_ORDER_STATUS.REJECTED:
        image = 'result-error';
        break;
      default:
        image = 'result-error';
        break;
    }
    return image;
  };

  const getTransferTitle = () => {
    let title: string = translate('paymentResult.transaction_success');
    switch (resultType) {
      case PAYMENT_ORDER_STATUS.PROCESSED:
        if (paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT) {
          title = translate('paymentResult.topup_success');
        } else {
          title = translate('paymentResult.transaction_success');
        }
        break;
      case PAYMENT_ORDER_STATUS.ACCEPTED:
        if (paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT) {
          title = translate('paymentResult.topup_in_progress');
        } else {
          title = translate('paymentResult.transaction_in_progress');
        }
        break;
      case PAYMENT_ORDER_STATUS.REJECTED:
        if (paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT) {
          title = translate('paymentResult.topup_error');
        } else {
          title = translate('paymentResult.transaction_failed');
        }
        break;
      default:
        title = translate('paymentResult.transaction_failed');
        break;
    }
    return title;
  };

  const getTransferAmount = () => {
    let _amount: string = FormatUtils.formatPrice(amount);
    switch (resultType) {
      case PAYMENT_ORDER_STATUS.PROCESSED:
        _amount = FormatUtils.formatPrice(amount);
        break;
      case PAYMENT_ORDER_STATUS.ACCEPTED:
        _amount = FormatUtils.formatPrice(amount);
        break;
      case PAYMENT_ORDER_STATUS.REJECTED:
        _amount = '';
        break;
      default:
        _amount = '';
        break;
    }
    return _amount;
  };

  const getTransferDescription = () => {
    let description: string = '';
    switch (resultType) {
      case PAYMENT_ORDER_STATUS.PROCESSED:
        description = '';
        break;
      case PAYMENT_ORDER_STATUS.ACCEPTED:
        description = translate('paymentResult.transaction_in_progress_explain');
        break;
      case PAYMENT_ORDER_STATUS.REJECTED:
        description = translate('paymentResult.transaction_in_progress_error');
        break;
      default:
        description = translate('error.paidErrorBilling');
        break;
    }
    return description;
  };

  return (
    <View>
      <View style={[styles.resultTransfer, style]}>
        {/* <Image source={getIconResult()} style={styles.icTransferResult} /> */}
        <MSBFastImage nameImage={getIconResult()} style={styles.icTransferResult} folder={MSBFolderImage.IMAGES} />
        {/* <MSBIcon icon={MSBIcons.Icon5GData} iconSize={MSBIconSize.SIZE_48} /> */}
        <View style={styles.marginLeft8}>
          <MSBTextBase style={getTitleStyle()} content={getTransferTitle()} />
          {getTransferAmount() && (
            <MSBTextBase style={styles.txtAmount}>
              {`${getTransferAmount()}`}
              <MSBTextBase style={styles.txtCurrency} content={translate('components.transferResult.currencyVnd')} />
            </MSBTextBase>
          )}
          <MSBTextBase style={styles.txtTimeResultTransfer} content={FormatUtils.formatDateDDMMYYYY(date || '')} />
        </View>
      </View>
      {!isDetail && getTransferDescription().length > 0 && (
        <MSBTextBase style={styles.description} content={getTransferDescription()} />
      )}
    </View>
  );
};

export default TransferResultView;

export const makeStyle = createMSBStyleSheet(({ColorGlobal, Typography}) => {
  return {
    description: {
      ...Typography?.small_regular,
      color: ColorGlobal.Neutral800,
      marginTop: SizeGlobal.Size400,
    },
    icTransferResult: {
      height: getSize(56),
      width: getSize(56),
    },
    marginLeft8: {
      marginLeft: SizeGlobal.Size200,
    },
    resultTransfer: {
      alignItems: 'center',
      borderBottomColor: ColorGlobal.Neutral100,
      borderBottomWidth: 1,
      flexDirection: 'row',
      paddingBottom: SizeGlobal.Size400,
    },

    txtAmount: {
      ...Typography?.h4_bold,
      color: ColorGlobal.Neutral800,
      marginTop: SizeGlobal.Size100,
    },
    txtCurrency: {
      ...Typography?.h4_medium,
      color: ColorGlobal.Neutral600,
    },
    txtTimeResultTransfer: {
      ...Typography?.small_regular,
      color: ColorGlobal.Neutral600,
      marginTop: SizeGlobal.Size100,
    },
    txtTitleResultTransfer: {
      ...Typography?.base_semiBold,
    },
  };
});
