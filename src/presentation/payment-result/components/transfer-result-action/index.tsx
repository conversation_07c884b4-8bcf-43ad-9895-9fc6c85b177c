import React from 'react';
import {View} from 'react-native';
import {createMSBStyleSheet, MSBFastImage, MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';

import {ActionProps, TransferResultActionProps} from './types';

const TransferResultAction = ({actions, style, onPress}: TransferResultActionProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={[styles.resultTransfer, style]}>
      {actions.map((item, index) => (
        <Item key={index} item={item} length={actions.length} onPress={onPress} />
      ))}
    </View>
  );
};

const Item: React.FC<{item: ActionProps; length: number; onPress: (item: ActionProps) => void}> = ({item, onPress}) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <MSBTouchable
      onPress={() => {
        onPress(item);
      }}
      style={[styles.itemContainer]}>
      <MSBFastImage source={item.icon} style={styles.icAction} />
      {/* <MSBIcon icon={item.icon} iconSize={MSBIconSize.SIZE_32} /> */}
      <MSBTextBase style={styles.txtTitleItem} numberOfLines={2}>
        {item.title}
      </MSBTextBase>
    </MSBTouchable>
  );
};

export default TransferResultAction;

export const makeStyle = createMSBStyleSheet(({SizeGlobal, Typography}) => {
  return {
    resultTransfer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      paddingVertical: SizeGlobal.Size400,
      justifyContent: 'space-between',
      flex: 1,
    },
    itemContainer: {
      alignItems: 'center',
      flex: 1,
    },
    txtTitleItem: {
      ...Typography?.caption_regular,
      color: '#091E42',
      marginTop: 6,
      textAlign: 'center',
    },
    flatList: {
      flex: 1,
    },
    icAction: {
      width: 32,
      height: 32,
    },
  };
});
