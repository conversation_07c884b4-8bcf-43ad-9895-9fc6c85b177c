import Images from '../../assets/images/Images';
import {TRANSFER_RESULT_ACTION} from '../../commons/Constants';
import {ActionProps} from './components/transfer-result-action/types';

export const actionsSuccess: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
    title: 'Chia sẻ',
    icon: Images.icTransferShare,
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
    title: 'Thanh toán tự động',
    icon: Images.icTransferSave,
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY,
    title: '<PERSON><PERSON><PERSON> ho<PERSON> đơn',
    icon: Images.icTransferSaveBenefit,
  },
];

export const actionsSuccessUserInternal: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
    title: 'Chia sẻ',
    icon: Images.icTransferShare,
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
    title: '<PERSON><PERSON>u mẫu',
    icon: Images.icTransferSave,
  },
];

export const actionsPending: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
    title: 'Share',
    icon: Images.icTransferShare,
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
    title: 'Lưu mẫu',
    icon: Images.icTransferSave,
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_MANAGER,
    title: 'Quản lý lệnh chuyển tiền',
    icon: Images.icTransferManager,
  },
];

export const actionsFail: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT,
    title: 'Trung tâm hỗ trợ',
    icon: Images.icTransferSupport,
  },
];
