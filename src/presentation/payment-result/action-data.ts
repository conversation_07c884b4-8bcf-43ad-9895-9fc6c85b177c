// import Images from '../../assets/images/Images';
import {TRANSFER_RESULT_ACTION} from '../../commons/Constants';
import {ActionProps} from './components/transfer-result-action/types';

export const actionsSuccess: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
    title: 'Chia sẻ',
    icon: 'tone-share',
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
    title: 'Thanh toán tự động',
    icon: 'tone-save',
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_BENEFICIARY,
    title: '<PERSON><PERSON><PERSON> hoá đơn',
    icon: 'tone-user-plus-add',
  },
];

export const actionsSuccessUserInternal: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
    title: 'Chia sẻ',
    icon: 'tone-share',
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
    title: '<PERSON><PERSON><PERSON> mẫu',
    icon: 'tone-save',
  },
];

export const actionsPending: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SHARE,
    title: 'Share',
    icon: 'tone-share',
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSER_SAVE_TEMPLATE,
    title: 'Lưu mẫu',
    icon: 'tone-save',
  },
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_MANAGER,
    title: 'Quản lý lệnh chuyển tiền',
    icon: 'tone-folder',
  },
];

export const actionsFail: ActionProps[] = [
  {
    type: TRANSFER_RESULT_ACTION.TRANSFER_SUPPORT,
    title: 'Trung tâm hỗ trợ',
    icon: 'tone-support-headphone',
  },
];
