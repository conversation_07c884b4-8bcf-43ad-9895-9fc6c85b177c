import React from 'react';
import {View, ImageBackground, ScrollView, Text} from 'react-native';
import {
  ButtonType,
  MSBButton,
  MSBFastImage,
  MSBFolderImage,
  MSBIcon,
  MSBIconSize,
  MSBPage,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';
import ViewShot from 'react-native-view-shot';
import Images from '../../assets/images/Images';
import TransferResultView from './components/transfer-result';
import usePaymentResult from './hook';
import BillItemInfo from '../../components/bill-item-info';
import BillInfo from '../../components/bill-info';
import {translate} from '../../locales/i18n';
import TransferResultAction from './components/transfer-result-action';
import {actionsFail, actionsPending, actionsSuccess} from './action-data';
import {PAYMENT_ORDER_STATUS} from '../../commons/Constants';
import {makeStyle} from './styles';
import FormatUtils from '../../utils/FormatUtils';

const PaymentResultScreen = () => {
  const {
    otherTransactions,
    totalAmount,
    goHome,
    paymentInfo,
    goPaymentResultDetail,
    transferAction,
    viewRef,
    heightViewshot,
    handleLayoutViewshot,
    isTopup,
  } = usePaymentResult();

  const {styles} = useMSBStyles(makeStyle);

  const renderImageShot = () => {
    return (
      <ViewShot ref={viewRef} style={[styles.shotView]} options={{format: 'jpg', quality: 1}}>
        <MSBFastImage folder={MSBFolderImage.BACKGROUND} nameImage="bg_home" style={styles.containerViewshot}>
          <View style={styles.contentViewShot} onLayout={handleLayoutViewshot}>
            <View style={styles.alignItemsCenter}>
              <MSBFastImage source={Images.icLogo} style={styles.icLogo} />
            </View>
            <View style={styles.contentContainerViewShot}>
              <TransferResultView
                resultType={paymentInfo.paymentResultType}
                amount={totalAmount}
                date={new Date().toString()}
                paymentType={paymentInfo.paymentValidate?.paymentType}
              />
              <View>
                <BillInfo
                  title={translate('paymentBill.title')}
                  style={styles.marginTop16}
                  styleName={styles.txtName}
                  name={paymentInfo?.billInfo?.billList?.[0].custName}
                  bankName={paymentInfo?.categoryName}
                  bankLogo={paymentInfo?.provider?.subGroupId?.toString()}
                  bankAlias={paymentInfo?.billInfo?.billCode}
                />
                <View style={styles.top16} />
                <Text style={styles.txtTitle}>{translate('paymentInfor.sourceAccount')}</Text>
                <View style={styles.sender}>
                  <MSBIcon folderIcon={MSBFolderImage.LOGO_BANK} icon={'MSB'} iconSize={MSBIconSize.SIZE_32} />
                  <MSBTextBase
                    style={[styles.senderName]}
                    numberOfLines={1}
                    content={FormatUtils.removeVietnameseTones(
                      paymentInfo?.originatorAccount?.name?.toLocaleUpperCase() ?? '',
                    )}
                  />
                </View>
                <BillItemInfo
                  style={styles.marginTop16}
                  title={translate('paymentConfirm.transactionFee')}
                  value={translate('paymentResult.free')}
                />

                <View>
                  <View style={styles.marginTop16} />
                  <MSBTextBase style={styles.txtTitle}>{'Kỳ thanh toán'}</MSBTextBase>
                  {paymentInfo?.billInfo?.billList?.map(item => (
                    <MSBTextBase style={styles.txtValue} content={item.period} />
                  ))}
                </View>

                <View>
                  <View style={styles.marginTop16} />
                  <MSBTextBase style={styles.txtTitle}>{translate('paymentConfirm.paymentCode')}</MSBTextBase>
                  {paymentInfo.additionalInfo?.split(',')?.map(item => (
                    <MSBTextBase style={styles.txtValue} content={item} />
                  ))}
                </View>
              </View>
            </View>
          </View>
        </MSBFastImage>
      </ViewShot>
    );
  };

  return (
    <View style={styles.flex1}>
      <MSBPage
        headerProps={{
          title: paymentInfo.title,
          isLogo: true,
          rightButtons: [
            {
              icon: 'tone-home',
              onPress: goHome,
            },
          ],
        }}
        style={styles.container}>
        <ScrollView style={styles.scrollViewContainer}>
          <View style={styles.contentContainerView}>
            <ImageBackground source={Images.detailTransactionBackground} style={styles.contentContainer}>
              <TransferResultView
                resultType={paymentInfo.paymentResultType}
                amount={totalAmount}
                date={new Date().toString()}
                paymentType={paymentInfo.paymentValidate?.paymentType}
              />
              {paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.ACCEPTED ||
                (paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.PROCESSED && (
                  <View>
                    <BillInfo
                      title={translate('paymentBill.title')}
                      style={styles.marginTop16}
                      styleName={styles.txtName}
                      name={paymentInfo?.billInfo?.billList?.[0].custName}
                      bankName={paymentInfo?.categoryName}
                      bankLogo={paymentInfo?.provider?.subGroupId?.toString()}
                      bankAlias={paymentInfo?.billInfo?.billCode}
                    />
                    <View style={styles.top16} />
                    <Text style={styles.txtTitle}>{translate('paymentInfor.sourceAccount')}</Text>
                    <View style={styles.sender}>
                      <MSBIcon folderIcon={MSBFolderImage.LOGO_BANK} icon={'MSB'} iconSize={MSBIconSize.SIZE_32} />
                      <MSBTextBase
                        style={[styles.senderName]}
                        numberOfLines={1}
                        content={FormatUtils.removeVietnameseTones(
                          paymentInfo?.originatorAccount?.name?.toLocaleUpperCase() ?? '',
                        )}
                      />
                    </View>
                    <BillItemInfo
                      style={styles.marginTop16}
                      title={translate('paymentConfirm.transactionFee')}
                      value={translate('paymentResult.free')}
                    />
                    <MSBButton
                      testID={'transfer.transferResultScreen.goTransferResultDetail'}
                      label={translate('paymentResult.detail_transaction')}
                      buttonType={ButtonType.Tertiary}
                      style={styles.marginTop16}
                      onPress={goPaymentResultDetail}
                    />
                  </View>
                ))}
              <TransferResultAction
                actions={
                  paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.PROCESSED
                    ? isTopup
                      ? actionsSuccess.slice(0, 1)
                      : actionsSuccess
                    : paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.ACCEPTED
                    ? isTopup
                      ? actionsPending.slice(0, 1)
                      : actionsPending
                    : actionsFail
                }
                style={styles.transferResultAction}
                onPress={transferAction}
              />
            </ImageBackground>
          </View>
        </ScrollView>
        <MSBButton
          label={translate('paymentResult.other_transaction')}
          style={styles.btnConfirm}
          onPress={otherTransactions}
        />
      </MSBPage>
      {renderImageShot()}
    </View>
  );
};

export default PaymentResultScreen;
