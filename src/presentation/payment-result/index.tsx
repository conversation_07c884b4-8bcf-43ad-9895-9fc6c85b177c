import React from 'react';
import {View, ScrollView} from 'react-native';
import {
  ButtonType,
  MSBBackgroundFastImage,
  MSBButton,
  MSBFastImage,
  MSBFolderImage,
  MSBIcon,
  MSBIconSize,
  MSBPage,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';
import ViewShot from 'react-native-view-shot';
import TransferResultView from './components/transfer-result';
import usePaymentResult from './hook';
import BillItemInfo from '../../components/bill-item-info';
import BillInfo from '../../components/bill-info';
import {translate} from '../../locales/i18n';
import TransferResultAction from './components/transfer-result-action';
import {actionsFail, actionsPending, actionsSuccess} from './action-data';
import {PAYMENT_ORDER_STATUS} from '../../commons/Constants';
import {makeStyle} from './styles';
import FormatUtils from '../../utils/FormatUtils';
import Utils from '../../utils/Utils';
import PaymentBillInfo from '../../components/payment-bill-info';

const PaymentResultScreen = () => {
  const {
    otherTransactions,
    totalAmount,
    goHome,
    paymentInfo,
    goPaymentResultDetail,
    transferAction,
    viewRef,
    handleLayoutViewshot,
    isTopup,
  } = usePaymentResult();

  const {styles} = useMSBStyles(makeStyle);

  const contentInfo = () => (
    <View>
      {Utils.isEmpty(paymentInfo.qrPaymentInfo) ? (
        <BillInfo
          title={translate('paymentBill.title')}
          style={styles.marginTop16}
          styleName={styles.txtName}
          name={paymentInfo?.billInfo?.billList?.[0].custName}
          bankName={paymentInfo?.categoryName}
          bankLogo={paymentInfo?.provider?.subGroupId?.toString()}
          bankAlias={paymentInfo?.billInfo?.billCode}
        />
      ) : (
        <PaymentBillInfo
          style={styles.accountInfo}
          merchantName={paymentInfo?.qrPaymentInfo?.merchantName ?? ''}
          storeId={paymentInfo?.qrPaymentInfo?.storeId ?? ''}
        />
      )}
      <View style={styles.top16} />
      <MSBTextBase style={styles.txtTitle} content={translate('paymentInfor.sourceAccount')} />
      <View style={styles.sender}>
        <MSBIcon folderIcon={MSBFolderImage.LOGO_BANK} icon={'MSB'} iconSize={MSBIconSize.SIZE_32} />
        <MSBTextBase
          style={[styles.senderName]}
          numberOfLines={1}
          content={FormatUtils.removeVietnameseTones(paymentInfo?.originatorAccount?.name?.toLocaleUpperCase() ?? '')}
        />
      </View>

      {!Utils.isEmpty(paymentInfo.qrPaymentInfo) && (
        <BillItemInfo
          style={styles.marginTop16}
          title={translate('paymentConfirm.content')}
          value={paymentInfo?.qrPaymentInfo?.remark}
        />
      )}
      <BillItemInfo
        style={styles.marginTop16}
        title={translate('paymentConfirm.transactionFee')}
        value={translate('paymentResult.free')}
      />
    </View>
  );

  const renderImageShot = () => {
    return (
      <ViewShot ref={viewRef} style={[styles.shotView]} options={{format: 'jpg', quality: 1}}>
        <MSBFastImage folder={MSBFolderImage.BACKGROUND} nameImage="bg_home" style={styles.containerViewshot}>
          <View style={styles.contentViewShot} onLayout={handleLayoutViewshot}>
            <View style={styles.alignItemsCenter}>
              <MSBFastImage nameImage={'logo_app'} style={styles.icLogo} folder={MSBFolderImage.LOGO} />
            </View>
            <View style={styles.contentContainerViewShot}>
              <TransferResultView
                resultType={paymentInfo.paymentResultType}
                amount={totalAmount}
                date={new Date().toString()}
                paymentType={paymentInfo.paymentValidate?.paymentType}
              />
              {contentInfo()}
              {Utils.isEmpty(paymentInfo.qrPaymentInfo) && (
                <View>
                  <View>
                    <View style={styles.marginTop16} />
                    <MSBTextBase style={styles.txtTitle} content={translate('screens.paymentResult.paymentPeriod')} />
                    {paymentInfo?.billInfo?.billList?.map(item => (
                      <MSBTextBase style={styles.txtValue} content={item.period} />
                    ))}
                  </View>

                  <View>
                    <View style={styles.marginTop16} />
                    <MSBTextBase style={styles.txtTitle}>{translate('paymentConfirm.paymentCode')}</MSBTextBase>
                    {paymentInfo.additionalInfo?.split(',')?.map(item => (
                      <MSBTextBase style={styles.txtValue} content={item} />
                    ))}
                  </View>
                </View>
              )}
            </View>
          </View>
        </MSBFastImage>
      </ViewShot>
    );
  };

  return (
    <View style={styles.flex1}>
      <MSBPage
        headerProps={{
          title: paymentInfo.title,
          isLogo: true,
          rightButtons: [
            {
              icon: 'tone-home',
              onPress: goHome,
            },
          ],
        }}
        style={styles.container}>
        <ScrollView style={styles.scrollViewContainer}>
          <View style={styles.contentContainerView}>
            {/* <ImageBackground source={Images.detailTransactionBackground} style={styles.contentContainer}> */}
            <MSBBackgroundFastImage style={styles.contentContainer} nameImage="bg_decorative_motifs">
              <TransferResultView
                resultType={paymentInfo.paymentResultType}
                amount={totalAmount}
                date={new Date().toString()}
                paymentType={paymentInfo.paymentValidate?.paymentType}
              />
              {paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.ACCEPTED ||
              paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.PROCESSED ? (
                <View>
                  {contentInfo()}
                  <MSBButton
                    testID={'transfer.transferResultScreen.goTransferResultDetail'}
                    label={translate('paymentResult.detail_transaction')}
                    buttonType={ButtonType.Tertiary}
                    style={styles.marginTop16}
                    onPress={goPaymentResultDetail}
                  />
                </View>
              ) : null}
              <TransferResultAction
                actions={
                  paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.PROCESSED
                    ? isTopup
                      ? actionsSuccess.slice(0, 1)
                      : actionsSuccess
                    : paymentInfo.paymentResultType === PAYMENT_ORDER_STATUS.ACCEPTED
                    ? isTopup
                      ? actionsPending.slice(0, 1)
                      : actionsPending
                    : actionsFail
                }
                style={styles.transferResultAction}
                onPress={transferAction}
              />
            </MSBBackgroundFastImage>
          </View>
        </ScrollView>
        <MSBButton
          label={translate('paymentResult.other_transaction')}
          style={styles.btnConfirm}
          onPress={otherTransactions}
        />
      </MSBPage>
      {renderImageShot()}
    </View>
  );
};

export default PaymentResultScreen;
