import {MSBFastImage, MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import {View} from 'react-native';
import {hostSharedModule} from 'msb-host-shared-module';
import React from 'react';
// import Images from '../../../../assets/images/Images.js';
import {translate} from '../../../../locales/i18n.ts';
import {CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel.ts';
import CategorySection from '../category-selection/index.tsx';
import {makeStyle} from './style.ts';

interface FeatureProps {
  categories: CategoryModel[];
  onSelect: (category: CategoryModel) => void;
  onOpenAutomaticRewards?: () => void;
  onOpenAllCategory?: () => void;
  loading?: boolean;
}

const FeatureSection: React.FC<FeatureProps> = ({
  categories,
  onSelect,
  onOpenAutomaticRewards,
  onOpenAllCategory,
  loading,
}) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  const buttonGroupData = [
    {
      id: 1,
      label: translate('paymentHome.automaticRewards'),
      icon: <MSBFastImage nameImage={'tone-clock-speed'} style={styles.functionIconSize} />,
      callback: onOpenAutomaticRewards,
      testID: 'automatic-rewards',
    },
    {
      id: 2,
      label: translate('paymentHome.allCategory'),
      icon: <MSBFastImage nameImage={'tone-grid-layout-more'} style={styles.functionIconSize} />,
      callback: onOpenAllCategory,
      testID: 'all-features',
    },
  ];

  const renderGroupApp = () => {
    return (
      <View style={styles.containerGroupApp}>
        {buttonGroupData.map((item, index) => (
          <MSBTouchable
            testID={item.testID}
            key={index}
            style={styles.buttonGroupData}
            onPress={() => {
              item?.callback ? item?.callback() : hostSharedModule.d.domainService.undevelopedFeature();
            }}>
            {item.icon ?? <></>}
            <MSBTextBase style={{}} content={item.label.toString()} type={theme.Typography?.caption_medium} />
          </MSBTouchable>
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, {minHeight: categories.length > 3 ? 232 : 128}]}>
      {CategorySection({categories, onSelect: onSelect, loading})}
      {renderGroupApp()}
    </View>
  );
};

export default FeatureSection;
