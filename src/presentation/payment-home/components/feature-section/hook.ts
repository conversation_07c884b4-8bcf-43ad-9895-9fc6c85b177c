import {useEffect, useState} from 'react';
import {CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel';
import {DIContainer} from '../../../../di/DIContainer';
import {showErrorPopup} from '../../../../utils/PopupUtils';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {PaymentStackParamList} from '../../../../navigation/PaymentStack';

export const useCategory = () => {
  const navigation = useNavigation<NavigationProp<PaymentStackParamList>>();
  const [categories, setCategories] = useState<CategoryModel[] | undefined>();
  const [state, setState] = useState<'INIT' | 'LOADING' | 'SUCCESS' | 'ERROR'>('INIT');
  useEffect(() => {
    getCategories();
  }, []);

  const getCategories = async () => {
    console.log('start call useCategory');
    const result = await DIContainer.getInstance().getCategoryListUseCase().execute();
    setState(result.status);
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
    } else if (result.status === 'SUCCESS') {
      setCategories(result?.data);
    }
  };

  const gotoPaymentBill = async (category: CategoryModel) => {
    console.log('checkCustomerDetail');
    const result = await DIContainer.getInstance().getValidateCustomerUseCase().execute();
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
    } //TODO : navigate to next screen
    if (category.id === 'MB-MR') {
      navigation.navigate('PaymentPhoneScreen', {category});
    } else {
      navigation.navigate('PaymentBillScreen', {category});
    }
  };

  return {
    categories,
    state,
    gotoPaymentBill,
  };
};
