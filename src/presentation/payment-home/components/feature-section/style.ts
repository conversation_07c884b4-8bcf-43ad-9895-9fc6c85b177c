// import {<PERSON>zeButton, SizeGlobal, ColorGlobal, SizeAlias, ColorAlias, IconButtonSize} from 'msb-shared-component';
// import {StyleSheet} from 'react-native';
// import StyleCommon from '../../../../commons/Styles.ts';

import {createMSBStyleSheet, SizeButton, ColorGlobal} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias, Shadow}) => {
  return {
    button: {
      // backgroundColor: ColorGlobal.Red500,
      width: '30%',
      margin: '1.5%',
      // height: 68, // TODO: Designer
      paddingVertical: 12,
      justifyContent: 'center',
      alignItems: 'center',
      gap: SizeButton.SmallSpacingVertical,
    },
    buttonGroupData: {
      alignItems: 'center',
      flex: 1,
      flexDirection: 'row',
      gap: SizeGlobal.Size200,
      justifyContent: 'center',
    },
    container: {
      alignContent: 'center',
      justifyContent: 'center',
      alignItems: 'stretch',
      backgroundColor: ColorGlobal.White,
      marginHorizontal: SizeAlias.SpacingSmall,
      borderRadius: SizeAlias.Radius4,
      borderWidth: SizeGlobal.Size25,
      borderColor: ColorAlias.BorderDefault,
      // gap: SizeAlias.SpacingSmall,
      minHeight: 232, // TODO: Designer
      marginBottom: 24,
      ...Shadow.center,
    },
    containerFeatureButton: {
      flexDirection: 'row',
      flexWrap: 'wrap', // Tự động xuống dòng
      justifyContent: 'flex-start', // Căn trái
    },
    containerGroupApp: {
      // backgroundColor: ColorGlobal.Red500,
      height: 52, // TODO: Designer
      borderTopWidth: SizeGlobal.Size25,
      borderColor: ColorAlias.BorderDefault,
      flexDirection: 'row',
    },
    containerGroupAppItem: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    gridIconSize: {
      height: SizeAlias.IconLarge,
      width: SizeAlias.IconLarge,
    },

    functionIconSize: {
      height: SizeAlias.IconMedium,
      width: SizeAlias.IconMedium,
    },
  };
});
