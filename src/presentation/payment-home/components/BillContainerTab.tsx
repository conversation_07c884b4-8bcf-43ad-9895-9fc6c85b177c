import {getSize, MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React, {useState} from 'react';
import {useWindowDimensions, View} from 'react-native';
import {TabBar, TabView} from 'react-native-tab-view';

import {translate} from '../../../locales/i18n.ts';
import BillTab from './bill-list/BillTab.tsx';

import {createMSBStyleSheet, ColorGlobal} from 'msb-shared-component';
import {IBillContact} from '../../../domain/entities/IBillContact.ts';

interface BillContainerTabProps {
  isBlocked: boolean;
  savedContacts?: IBillContact[];
  recentContacts?: IBillContact[];
  onAddNewContact?: () => void;
}

const BillContainerTab = ({isBlocked, savedContacts, recentContacts, onAddNewContact}: BillContainerTabProps) => {
  const layout = useWindowDimensions();
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    {key: 'saved', title: translate('billingTab.titleSaved')},
    {key: 'recent', title: translate('billingTab.titleRecent')},
  ]);
  const {styles} = useMSBStyles(makeStyle);

  return (
    <TabView
      testID={'payment.ContactTab.changeTab'}
      onIndexChange={setIndex}
      navigationState={{index, routes}}
      renderScene={({route}) => {
        switch (route.key) {
          case 'saved':
            return (
              <BillTab
                isBlocked={isBlocked}
                isShowAddContact={true}
                contacts={savedContacts}
                onAddNewContact={onAddNewContact}
              />
            );
          case 'recent':
            return <BillTab isBlocked={isBlocked} contacts={recentContacts} />;
          default:
            return null;
        }
      }}
      initialLayout={{width: layout.width}}
      swipeEnabled={false}
      renderTabBar={props => (
        <TabBar
          {...props}
          style={styles.tabBar}
          indicatorStyle={styles.indicator}
          contentContainerStyle={styles.tabContainer}
          renderTabBarItem={itemProps => {
            const {route, onPress} = itemProps;
            const focused = index === itemProps.navigationState.routes.findIndex(r => r.key === route.key);
            return (
              <MSBTouchable onPress={onPress} style={styles.tabItem} testID={`payment.moneyHubScreen.${route.title}`}>
                <MSBTextBase style={[styles.tabText, focused && styles.tabTextActive]} content={route.title} />
                {focused && <View style={styles.tabIndicator} />}
              </MSBTouchable>
            );
          }}
        />
      )}
    />
  );
};

export const makeStyle = createMSBStyleSheet(({SizeGlobal, SizeAlias, Typography}) => {
  return {
    indicator: {
      backgroundColor: ColorGlobal.Brand500,
    },
    tabBar: {
      backgroundColor: ColorGlobal.White,
      borderTopEndRadius: SizeAlias.Radius3,
      borderTopStartRadius: SizeAlias.Radius3,
    },
    tabContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    tabIndicator: {
      backgroundColor: ColorGlobal.Brand500,
      bottom: 0,
      height: SizeGlobal.Size50,
      position: 'absolute',
      width: getSize(86),
    },
    tabItem: {
      alignItems: 'center',
      flex: 1,
      paddingVertical: SizeGlobal.Size300,
    },
    tabText: {
      // ...Tpg.base_regular,
      ...Typography?.base_regular,
      color: ColorGlobal.Black,
    },
    tabTextActive: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Brand500,

      // ...Tpg.base_semiBold,
      // color: ColorGlobal.Brand500,
    },
  };
});

export default BillContainerTab;
