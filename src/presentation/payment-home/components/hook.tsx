import React, {useEffect, useState} from 'react';
import {createContext, PropsWithChildren, useContext} from 'react';

import {SafeAny} from '../../../commons/Constants';
import ScreenNames from '../../../commons/ScreenNames';

type BeneficiaryContextType = {
  navigation: SafeAny;
  isBeneficiary: boolean;
  setIsBeneficiary: (isBeneficiary: boolean) => void;
};
export const BeneficiaryContext = createContext<BeneficiaryContextType | null>(null);

export const BeneficiaryProvider = ({children, navigation}: PropsWithChildren & {navigation: any}) => {
  const currentRouteName = navigation.getState().routes[navigation.getState().routes.length - 1].name;

  const [isBeneficiary, setIsBeneficiary] = useState(false);

  useEffect(() => {
    setIsBeneficiary(currentRouteName === ScreenNames.PaymentBillScreen);
  }, [currentRouteName]);

  return (
    <BeneficiaryContext.Provider value={{navigation, isBeneficiary, setIsBeneficiary}}>
      {children}
    </BeneficiaryContext.Provider>
  );
};
BeneficiaryContext.displayName = 'BeneficiaryContext';

export const useBeneficiaryStore = () => {
  const store = useContext(BeneficiaryContext);
  return store;
};
