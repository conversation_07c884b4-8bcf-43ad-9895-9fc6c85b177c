import {createMSBStyleSheet, SizeButton} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias}) => {
  return {
    button: {
      // backgroundColor: ColorGlobal.Red500,
      width: '30%',
      margin: '1.5%',
      // height: 68, // TODO: Designer
      paddingVertical: 12,
      justifyContent: 'center',
      alignItems: 'center',
      gap: SizeButton.SmallSpacingVertical,
    },
    containerFeatureButton: {
      flexDirection: 'row',
      flexWrap: 'wrap', // Tự động xuống dòng
      justifyContent: 'flex-start', // Căn trái
    },
    containerGroupApp: {
      // backgroundColor: ColorGlobal.Red500,
      height: 52, // TODO: Designer
      borderTopWidth: SizeGlobal.Size25,
      borderColor: ColorAlias.BorderDefault,
      flexDirection: 'row',
    },
    gridIconSize: {
      height: SizeAlias.IconLarge,
      width: SizeAlias.IconLarge,
    },

    functionIconSize: {
      height: SizeAlias.IconMedium,
      width: SizeAlias.IconMedium,
    },
  };
});
