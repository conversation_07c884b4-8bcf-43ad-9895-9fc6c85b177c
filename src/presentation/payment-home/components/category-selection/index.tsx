import {MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import {Image, View} from 'react-native';
import Skeleton from 'react-native-reanimated-skeleton';

import React from 'react';
import Images from '../../../../assets/images/Images.js';
import {CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel.ts';
import {makeStyle} from './style.ts';

interface CategoryProps {
  categories: CategoryModel[];
  onSelect: (category: CategoryModel) => void;
  loading?: boolean;
}

const CategorySection: React.FC<CategoryProps> = ({categories, onSelect: onSlect, loading}) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  const iconFromCode = (code: string) => {
    switch (code.toUpperCase()) {
      case 'EL': // dien
        return <Image source={Images.icElectric} style={styles.gridIconSize} />;
      case 'WC': // nuoc
        return <Image source={Images.icWater} style={styles.gridIconSize} />;
      case 'MB-MR':
        return <Image source={Images.icPhone} style={styles.gridIconSize} />;
      case 'TV':
        return <Image source={Images.icTv} style={styles.gridIconSize} />;
      case 'IP':
        return <Image source={Images.icInternet} style={styles.gridIconSize} />;
      case 'VETC':
        return <Image source={Images.icCar} style={styles.gridIconSize} />;
      default:
        return <></>;
    }
  };

  if (loading) {
    return (
      <View style={styles.containerFeatureButton}>
        {[1, 2, 3].map((_, index) => (
          <View key={index} style={styles.button}>
            <Skeleton
              isLoading={loading}
              containerStyle={{alignItems: 'center', flexDirection: 'column'}}
              layout={[
                {
                  width: 48,
                  height: 48,
                  borderRadius: 12,
                  marginBottom: theme.SizeGlobal.Size200,
                },
                {
                  width: 80,
                  height: 16,
                  borderRadius: 999,
                },
              ]}
            />
          </View>
        ))}
      </View>
    );
  }

  return (
    <View style={styles.containerFeatureButton}>
      {categories.map((item, index) => (
        <MSBTouchable
          testID={item.id}
          key={index}
          style={styles.button}
          onPress={() => {
            onSlect(item);
          }}>
          {iconFromCode(item.id ?? '') ?? <></>}
          <MSBTextBase style={{}} content={item.description} type={theme.Typography?.caption_regular} />
        </MSBTouchable>
      ))}
    </View>
  );
};

export default CategorySection;
