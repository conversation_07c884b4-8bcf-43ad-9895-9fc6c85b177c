import {MSBFastImage, MSBFolderImage, MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import {View} from 'react-native';
import Skeleton from 'react-native-reanimated-skeleton';

import React from 'react';
// import Images from '../../../../assets/images/Images.js';
import {CategoryModel} from '../../../../domain/entities/category-list/CategoryListModel.ts';
import {makeStyle} from './style.ts';

interface CategoryProps {
  categories: CategoryModel[];
  onSelect: (category: CategoryModel) => void;
  loading?: boolean;
}

const CategorySection: React.FC<CategoryProps> = ({categories, onSelect: onSlect, loading}) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  const iconFromCode = (code: string) => {
    switch (code.toUpperCase()) {
      case 'EL': // dien
        // return <Image source={Images.icElectric} style={styles.gridIconSize} />;
        return (
          <MSBFastImage
            nameImage={'tone-electric-flash-lightning'}
            style={styles.gridIconSize}
            folder={MSBFolderImage.ICON_SVG}
          />
        );
      case 'WC': // nuoc
        return (
          <MSBFastImage nameImage={'tone-water-drop'} style={styles.gridIconSize} folder={MSBFolderImage.ICON_SVG} />
        );
      case 'MB-MR':
        return (
          <MSBFastImage
            nameImage={'tone-mobile-pay-late'}
            style={styles.gridIconSize}
            folder={MSBFolderImage.ICON_SVG}
          />
        );
      case 'TV':
        return <MSBFastImage nameImage={'tone-tv'} style={styles.gridIconSize} folder={MSBFolderImage.ICON_SVG} />;
      case 'IP':
        return (
          <MSBFastImage
            nameImage={'tone - global - international'}
            style={styles.gridIconSize}
            folder={MSBFolderImage.ICON_SVG}
          />
        );
      case 'VETC':
        return <MSBFastImage nameImage={'tone-car'} style={styles.gridIconSize} folder={MSBFolderImage.ICON_SVG} />;
      default:
        return <></>;
    }
  };

  if (loading) {
    return (
      <View style={styles.containerFeatureButton}>
        {[1, 2, 3].map((_, index) => (
          <View key={index} style={styles.button}>
            <Skeleton
              isLoading={loading}
              containerStyle={{alignItems: 'center', flexDirection: 'column'}}
              layout={[
                {
                  width: 48,
                  height: 48,
                  borderRadius: 12,
                  marginBottom: theme.SizeGlobal.Size200,
                },
                {
                  width: 80,
                  height: 16,
                  borderRadius: 999,
                },
              ]}
            />
          </View>
        ))}
      </View>
    );
  }

  return (
    <View style={styles.containerFeatureButton}>
      {categories.map((item, index) => (
        <MSBTouchable
          testID={item.id}
          key={index}
          style={styles.button}
          onPress={() => {
            onSlect(item);
          }}>
          {iconFromCode(item.id ?? '') ?? <></>}
          <MSBTextBase style={{}} content={item.description} type={theme.Typography?.caption_regular} />
        </MSBTouchable>
      ))}
    </View>
  );
};

export default CategorySection;
