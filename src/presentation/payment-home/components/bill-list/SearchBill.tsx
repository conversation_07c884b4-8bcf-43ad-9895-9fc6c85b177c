import {createMSBStyleSheet, MSBSearchInput, useMSBStyles} from 'msb-shared-component';
import {View} from 'react-native';

import {translate} from '../../../../locales/i18n.ts';
import {useState} from 'react';
import React from 'react';

interface SearchBillProps {
  isShowAddContact: boolean | undefined;
  onAddNewContact?: () => void;
  onSearch: (text: string) => void;
}

export const SearchBill = (props: SearchBillProps) => {
  const [searchText, setSearchText] = useState('');
  const {styles} = useMSBStyles(makeStyle);

  const handleTextChange = (text: string) => {
    setSearchText(text);
    props.onSearch(text);
  };

  const handleOpenAddContact = () => {
    props?.onAddNewContact?.();
  };

  return (
    <View style={styles.container}>
      <MSBSearchInput
        testID={'payment.SavedTab.enterSearch'}
        value={searchText}
        placeholder={translate('billingTab.hintSearch')}
        onChangeText={handleTextChange}
        containerSearchStyle={styles.containerSearchInput}
        maxLength={255}
      />
      {/* {props.isShowAddContact && (
        // <MSBIcon
        //   testID={'payment.SavedTab.pressAddContact'}
        //   icon={MSBIcons.IconUserPlusAdd}
        //   styleContainer={styles.searchInput}
        //   onIconClick={handleOpenAddContact}
        // />
        <MSBTouchable
          testID={'payment.SavedTab.pressAddContact'}
          onPress={handleOpenAddContact}
          style={styles.searchInput}>
          <Image source={Images.icAddBill} style={styles.searchIconSize} />
        </MSBTouchable>
      )} */}
    </View>
  );
};

export const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorAlias, SizeAlias}) => {
  return {
    container: {
      alignItems: 'center',
      borderTopWidth: SizeGlobal.Size25,
      borderBottomWidth: SizeGlobal.Size25,
      borderTopColor: ColorAlias.BorderDefault,
      borderBottomColor: ColorAlias.BorderDefault,
      flexDirection: 'row',
      width: '100%',
    },
    containerSearchInput: {
      flex: 1,
      padding: SizeAlias.SpacingSmall,
      width: '100%',
    },
    searchInput: {
      marginEnd: SizeAlias.SpacingSmall,
    },
    searchIconSize: {
      height: SizeAlias.IconLarge,
      width: SizeAlias.IconLarge,
    },
  };
});
