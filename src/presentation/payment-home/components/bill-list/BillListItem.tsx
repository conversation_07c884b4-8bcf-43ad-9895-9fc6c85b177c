import {View} from 'react-native';
import React from 'react';
import {showDialogIMBM} from '../..';
import {BillItem, SwipeableBillItem} from '../../../../components/bill-item/index';
import {IBillContact} from '../../../../domain/entities/IBillContact';
import {ColorGlobal, createMSBStyleSheet, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import FormatUtils from '../../../../utils/FormatUtils';
type ItemProps = {
  isBlocked: boolean;
  isEditable: boolean;
  item: IBillContact;
  highlight: string;
  onClick: (item: IBillContact) => void;
  onEdit?: (item: IBillContact) => void;
  onDelete?: (item: IBillContact) => void;
  onPayment?: (item: IBillContact) => void;
};

export const BillListItem = ({
  isBlocked,
  isEditable,
  item,
  highlight,
  onEdit,
  onClick,
  onDelete,
  onPayment,
}: ItemProps) => {
  const {styles} = useMSBStyles(makeStyle);
  const reminderStatus = item.getReminderStatus() === 'ACTIVE';

  // const alteredItem: any = beneficiaryStore?.isBeneficiary ? {...item, localType: 'MYACCOUNT'} : {...item};
  // return <MSBTextBase content="ITEM" />;
  return (
    <View>
      {isEditable ? (
        <SwipeableBillItem
          item={item}
          editOnClick={onEdit}
          deleteOnClick={onDelete}
          onClick={accountItem => {
            if (isBlocked) {
              showDialogIMBM();
              return;
            }
            onClick(accountItem);
          }}
          highlight={highlight}
          onPressPayment={onPayment}
        />
      ) : (
        <BillItem
          item={item}
          onClick={accountItem => {
            if (isBlocked) {
              showDialogIMBM();
              return;
            }
            onClick(accountItem);
          }}
          highlight={highlight}
          onPressPayment={onPayment}
        />
      )}
      <View style={styles.line} />
      {reminderStatus && (
        <View style={styles.amountPaymentContainer}>
          {/* <MSBTextBase>{item.getCustomerName()}</MSBTextBase> */}
          <MSBTextBase>Hóa đơn T3/2025</MSBTextBase>
          <MSBTextBase style={styles.amountPaymentText}>
            {FormatUtils.formatMoney(item.getPayableAmount())} <MSBTextBase style={styles.currencyText} content="VND" />
          </MSBTextBase>
        </View>
      )}
    </View>
  );
};

export const makeStyle = createMSBStyleSheet(({ColorAlias, SizeAlias, Typography}) => {
  return {
    container: {
      backgroundColor: ColorGlobal.Gray200, //TODO: check again
      height: 36,
      justifyContent: 'center',
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    line: {
      backgroundColor: ColorAlias.BorderDefault,
      height: 1,
      marginHorizontal: SizeAlias.SpacingSmall,
    },
    amountPaymentContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: SizeAlias.SpacingSmall,
      borderBottomWidth: 1,
      borderBottomColor: ColorGlobal.Neutral100,
      paddingTop: SizeAlias.SpacingSmall,
      paddingBottom: SizeAlias.SpacingSmall,
    },
    amountPaymentText: {
      ...Typography?.small_semiBold,
      color: ColorGlobal.Neutral800,
    },
    currencyText: {
      ...Typography?.small_regular,
      color: ColorGlobal.Neutral600,
    },
  };
});
