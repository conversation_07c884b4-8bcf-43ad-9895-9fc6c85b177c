import React from 'react';
import {Image, ScrollView, View} from 'react-native';

// import Images from '../../../../assets/images/Images';
import {translate} from '../../../../locales/i18n';
import {
  createMSBStyleSheet,
  EmptyType,
  getSize,
  MSBEmptyState,
  MSBFastImage,
  MSBFolderImage,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';

export const EmptyScreen = ({isRecent}: any) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <ScrollView>
      <View style={styles.container}>
        {/* <Image style={styles.image} source={Images.icEmptyBill} /> */}
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase style={theme.Typography?.base_semiBold} content={translate('billingTab.titleEmpty')} />
        <MSBTextBase
          style={[theme.Typography?.small_regular, styles.textAlign]}
          content={translate(isRecent ? 'billingTab.contentEmptyRecent' : 'billingTab.contentEmpty')}
        />
      </View>
    </ScrollView>
  );
};

export const EmptyBillSystemErrorScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <ScrollView>
      <View style={styles.containerEmptySavedSystemError}>
        {/* <Image style={styles.image} source={Images.icEmptyBillSystemError} /> */}
        <MSBFastImage nameImage={'coffee'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase
          style={theme.Typography?.base_semiBold}
          content={translate('billingTab.titleEmptyContactSystemError')}
        />
        <MSBTextBase
          style={[theme.Typography?.small_regular, styles.textAlign]}
          content={translate('billingTab.contentEmptyContactSystemError')}
        />
      </View>
    </ScrollView>
  );
};

export const EmptyBillFilteredScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <ScrollView>
      <View style={styles.containerEmptySavedSystemError}>
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase
          style={theme.Typography?.base_semiBold}
          content={translate('billingTab.titleEmptyContactFiltered')}
        />
        <MSBTextBase
          style={theme.Typography?.small_regular}
          content={translate('billingTab.contentEmptyContactFiltered')}
        />
      </View>
    </ScrollView>
  );
};

export const EmptyTransactionHistoryScreen = ({
  title,
  content,
  type,
}: {
  title: string;
  content: string;
  type: EmptyType;
}) => {
  return (
    <View>
      <MSBEmptyState emptyTitle={title} emptySubTitle={content} type={type} />
    </View>
  );
};

const makeStyle = createMSBStyleSheet(({SizeGlobal}) => ({
  container: {
    alignItems: 'center',
    flexDirection: 'column',
    height: 320,
    justifyContent: 'center',
    width: '100%',
  },
  containerEmptySavedSystemError: {
    alignItems: 'center',
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    width: '100%',
  },
  image: {
    height: getSize(144),
    marginBottom: SizeGlobal.Size500,
    marginTop: SizeGlobal.Size500,
    width: getSize(144),
  },
  textAlign: {
    textAlign: 'center',
  },
}));
