import React from 'react';
import {Image, ScrollView, Text, View} from 'react-native';

// import Images from '../../../../assets/images/Images';
import {translate} from '../../../../locales/i18n';
import {
  createMSBStyleSheet,
  EmptyType,
  getSize,
  MSBEmptyState,
  MSBFastImage,
  MSBFolderImage,
  useMSBStyles,
} from 'msb-shared-component';

export const EmptyScreen = ({isRecent}: any) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <ScrollView>
      <View style={styles.container}>
        {/* <Image style={styles.image} source={Images.icEmptyBill} /> */}
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <Text style={theme.Typography?.base_semiBold}>{translate('billingTab.titleEmpty')}</Text>
        <Text style={[theme.Typography?.small_regular, styles.textAlign]}>
          {translate(isRecent ? 'billingTab.contentEmptyRecent' : 'billingTab.contentEmpty')}
        </Text>
      </View>
    </ScrollView>
  );
};

export const EmptyBillSystemErrorScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <ScrollView>
      <View style={styles.containerEmptySavedSystemError}>
        {/* <Image style={styles.image} source={Images.icEmptyBillSystemError} /> */}
        <MSBFastImage nameImage={'coffee'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <Text style={theme.Typography?.base_semiBold}>{translate('billingTab.titleEmptyContactSystemError')}</Text>
        <Text style={[theme.Typography?.small_regular, styles.textAlign]}>
          {translate('billingTab.contentEmptyContactSystemError')}
        </Text>
      </View>
    </ScrollView>
  );
};

export const EmptyBillFilteredScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <ScrollView>
      <View style={styles.containerEmptySavedSystemError}>
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <Text style={theme.Typography?.base_semiBold}>{translate('billingTab.titleEmptyContactFiltered')}</Text>
        <Text style={theme.Typography?.small_regular}>{translate('billingTab.contentEmptyContactFiltered')}</Text>
      </View>
    </ScrollView>
  );
};

export const EmptyTransactionHistoryScreen = ({
  title,
  content,
  type,
}: {
  title: string;
  content: string;
  type: EmptyType;
}) => {
  return (
    <View>
      <MSBEmptyState emptyTitle={title} emptySubTitle={content} type={type} />
    </View>
  );
};

const makeStyle = createMSBStyleSheet(({SizeGlobal}) => ({
  container: {
    alignItems: 'center',
    flexDirection: 'column',
    height: 320,
    justifyContent: 'center',
    width: '100%',
  },
  containerEmptySavedSystemError: {
    alignItems: 'center',
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    width: '100%',
  },
  image: {
    height: getSize(144),
    marginBottom: SizeGlobal.Size500,
    marginTop: SizeGlobal.Size500,
    width: getSize(144),
  },
  textAlign: {
    textAlign: 'center',
  },
}));
