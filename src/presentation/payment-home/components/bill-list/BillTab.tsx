import {StyleSheet, View} from 'react-native';
import React, {useState} from 'react';

import {BillList} from './BillList.tsx';
import {SearchBill} from './SearchBill.tsx';
import {useContacts} from './hook.ts';
import {IBillContact} from '../../../../domain/entities/IBillContact.ts';

interface BillTabProps {
  isBlocked?: boolean;
  isShowAddContact?: boolean;
  contacts?: IBillContact[];
  onAddNewContact?: () => void;
}

const BillTab = (props: BillTabProps) => {
  const hook = useContacts(props.isShowAddContact === true, props.contacts);

  const [searchText, setSearchText] = useState('');

  return (
    <View style={styles.container}>
      <SearchBill
        onSearch={value => {
          setSearchText(value);
        }}
        isShowAddContact={props.isShowAddContact}
        onAddNewContact={props.onAddNewContact}
      />
      <BillList
        isBlocked={props.isBlocked ?? false}
        searchText={searchText}
        isEditable={props.isShowAddContact ?? false}
        bills={hook.contacts}
        onClick={item => {
          hook.gotoDetailScreen(item);
        }}
        onEdit={item => {
          hook.gotoEditScreen(item);
        }}
        onDelete={item => {
          hook.showConfirmDialog(item);
        }}
        onPayment={item => {
          console.log('onPayment ==>>', item);
          hook.gotoPaymentScreen(item);
        }}
      />
    </View>
  );
};

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    width: '100%',
  },
});

export default BillTab;
