import {SafeAny} from '../../../../commons/Constants';

type EventCallback = (data: SafeAny) => void;

class EventEmitter {
  private events: {[key: string]: EventCallback[]} = {};

  on(event: string, callback: EventCallback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);

    // Return unsubscribe function
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    };
  }

  emit(event: string, data: SafeAny) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }
}

export const eventEmitter = new EventEmitter();
