import {useCallback, useEffect, useState} from 'react';
import {DIContainer} from '../../../../di/DIContainer';
import {hostSharedModule, PopupType} from 'msb-host-shared-module';
import Utils from '../../../../utils/Utils';
import {DeleteBillContactRequest} from '../../../../data/models/delete-bill-contact/DeleteBillContactRequest';
import {IBillContact} from '../../../../domain/entities/IBillContact';
import {useBeneficiaryStore} from '../hook';
import ScreenNames from '../../../../commons/ScreenNames';
import {GetBillDetailRequest} from '../../../../data/models/get-bill-detail/GetBillDetailRequest';
import {showErrorPopup} from '../../../../utils/PopupUtils';
import {PaymentInfoModel} from '../../../../navigation/types';
import {translate} from '../../../../locales/i18n';

export const useContacts = (isMyContact: boolean, data?: IBillContact[] | undefined) => {
  const [contacts, setContacts] = useState<IBillContact[]>([]);
  const [isDeleted, setDeleted] = useState<boolean | undefined>();
  const beneficiaryStore = useBeneficiaryStore();
  // const navigation = useNavigation<NavigationProp<PaymentStackParamList>>();
  useEffect(() => {
    beneficiaryStore?.navigation.addListener('focus', () => {
      isMyContact ? fetchContacts() : fetchRecentContact();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (data) {
      setContacts(data);
    } else {
      isMyContact ? fetchContacts() : fetchRecentContact();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, isMyContact]);

  const fetchContacts = useCallback(async () => {
    const result = await DIContainer.getInstance().getMyBillContactListUseCase().execute();
    if (result.status === 'ERROR') {
      return;
    }
    console.log('fetchContacts ==>>', result.data);
    setContacts(result.data ?? []);
  }, []);

  const fetchRecentContact = useCallback(async () => {
    const result = await DIContainer.getInstance().getGetMyBillContactRecentListUseCase().execute({});

    if (result.status === 'ERROR') {
      return;
    } else if (result.status === 'SUCCESS') {
      setContacts(result.data ?? []);
    }
  }, []);

  const gotoEditScreen = (item: IBillContact) => {
    console.log('ONEDIT___------------', item);
    beneficiaryStore?.navigation.navigate(ScreenNames.EditBillContactScreen, {
      contact: item,
    });
  };

  const gotoPaymentScreen = async (item: IBillContact) => {
    console.log('gotoPaymentScreen ==>>', item);
    const providerRequest = {code: item.getCategoryCode?.() ?? ''};
    const providerResult = await DIContainer.getInstance().getProviderListUseCase().execute(providerRequest);
    if (providerResult.status === 'ERROR') {
      showErrorPopup(providerResult.error);
      return;
    }
    console.log('gotoPaymentScreen2 ==>>', item);
    const request: GetBillDetailRequest = {
      billCode: item.getBillCode?.() ?? '',
      serviceCode: item.getExternalId?.() ?? '',
      accountingType: 'ACCT',
    };
    const result = await DIContainer.getInstance().getGetBillDetailUseCase().execute(request);
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
      return;
    }
    console.log('gotoPaymentScreen3 ==>>', item.getCategoryCode?.(), item.getExternalId());

    beneficiaryStore?.setIsBeneficiary(true);
    // hostSharedModule.d.domainService.undevelopedFeature();
    const providerSelected = providerResult.data?.find(provider => provider?.serviceCode === item.getExternalId());

    const paymentInfo: PaymentInfoModel = {
      title: `${translate('paymentBill.title')} ${providerSelected?.getName() ?? ''}`,
      categoryName: providerSelected?.getName() ?? '',
      billInfo: result.data,
      contractName: result.data?.billList?.[0]?.custName,
      provider: providerSelected,
    };
    console.log('paymentInfo=======>>>>>', paymentInfo);
    beneficiaryStore?.navigation.navigate(ScreenNames.PaymentInfoScreen, {paymentInfo});
  };

  const gotoDetailScreen = (item: IBillContact) => {
    beneficiaryStore?.setIsBeneficiary(true);
    // hostSharedModule.d.domainService.undevelopedFeature();
    beneficiaryStore?.navigation.navigate(ScreenNames.BillDetailScreen, {
      account: {
        accountNumber: item.getBillCode?.(),
        bankCode: item.getExternalId() || item.getServiceCode(),
        bankName: item.getSubtitle(),
      },
      contact: item,
    });
  };

  const showConfirmDialog = (item: IBillContact) => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: PopupType.WARNING,
      title: 'Xác nhận xoá',
      content: 'Hoá đơn sẽ bị xoá khỏi danh sách đã lưu.',
      cancelBtnText: 'Đóng',
      confirmBtnText: 'Xác nhận',
      onConfirm: () => deleteContact(item),
    });
  };

  const deleteContact = async (item: IBillContact) => {
    const request: DeleteBillContactRequest = {id: item.getId() ?? ''};
    const reusult = await DIContainer.getInstance().getDeleteBillContactUseCase().execute(request);
    if (reusult.status === 'SUCCESS') {
      Utils.showToastSuccess('Đã xóa hoá đơn khỏi danh sách đã lưu');
      setDeleted(!isDeleted);
      fetchContacts();
    } else {
      Utils.showToastError('Xóa hoá đơn không thành công');
    }
  };

  return {
    contacts,
    gotoEditScreen,
    gotoPaymentScreen,
    showConfirmDialog,
    gotoDetailScreen,
  };
};
