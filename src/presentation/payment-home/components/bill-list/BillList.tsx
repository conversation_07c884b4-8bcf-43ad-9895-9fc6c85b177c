import {StyleSheet, View} from 'react-native';
import React from 'react';
import {FlatList} from 'react-native-gesture-handler';

import {EmptyBillFilteredScreen, EmptyBillSystemErrorScreen, EmptyScreen} from './EmptyBill.tsx';
import {BillListItem} from './BillListItem.tsx';
import {IBillContact} from '../../../../domain/entities/IBillContact.ts';

type Props = {
  isBlocked: boolean;
  isEditable: boolean;
  searchText: string;
  bills?: IBillContact[];
  onClick: (item: IBillContact) => void;
  onEdit?: (item: IBillContact) => void;
  onDelete?: (item: IBillContact) => void;
  onPayment?: (item: IBillContact) => void;
};

export const BillList = ({isBlocked, isEditable, searchText, bills, onEdit, onClick, onDelete, onPayment}: Props) => {
  const normalizedSearch = searchText.trim().toLowerCase();
  let content: React.JSX.Element;
  if (bills === undefined) {
    console.log('BillList: ==>>>>> undefined bills');
    content = <EmptyBillSystemErrorScreen />;
  } else {
    let filtered: IBillContact[] | undefined = [];
    if (bills.length === 0) {
      console.log('BillList: ==>>>>> bills.length === 0');
      content = <EmptyScreen isRecent={!isEditable} />;
    } else {
      filtered = bills.filter(bill => {
        return bill.getSearchContent().includes(normalizedSearch);
      });

      // console.log('filteredAccountContact', filteredAccountContact);

      if (filtered.length === 0) {
        console.log('BillList: ==>>>>> empty filtered bills');
        content = <EmptyBillFilteredScreen />;
      } else {
        console.log('BillList: ==>>>>> NGON DATA');
        content = (
          <View style={styles.container}>
            <FlatList
              data={filtered}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({item}) => (
                <BillListItem
                  key={item.getId()}
                  item={item}
                  isEditable={isEditable}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onClick={onClick}
                  onPayment={onPayment}
                  isBlocked={isBlocked}
                  highlight={(searchText ?? '').trim()}
                />
              )}
            />
          </View>
        );
      }
    }
  }
  return <View style={styles.container}>{content}</View>;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'center',
    width: '100%',
  },
});
