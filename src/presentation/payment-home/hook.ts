import {useEffect, useState} from 'react';
import {hostSharedModule} from 'msb-host-shared-module';
import {DIContainer} from '../../di/DIContainer';
import {showErrorPopup} from '../../utils/PopupUtils';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import ScreenNames from '../../commons/ScreenNames';

//TODO: need to be refactor because call multiple times usecase at the same time
const usePaymentHome = () => {
  const navigation = useNavigation<NavigationProp<PaymentStackParamList>>();
  const [userDetail, setUserDetail] = useState<any | null>();
  const [isLoadingCate, setIsLoadingCate] = useState(false);
  const [categories, setCategories] = useState<CategoryModel[] | undefined>();
  const [state, setState] = useState<'INIT' | 'LOADING' | 'SUCCESS' | 'ERROR'>('INIT');
  const [isBlocked, setBlocked] = useState<boolean>(false);

  useEffect(() => {
    checkCustomerDetail();
    getCategories();
  }, []);

  useEffect(() => {
    console.log('userDetail', userDetail?.serviceGroup);
    if (userDetail) {
      setBlocked(userDetail.serviceGroup === 'M_INQUIRY');
    }
  }, [userDetail]);

  const checkCustomerDetail = async () => {
    console.log('checkCustomerDetail');
    const result = await DIContainer.getInstance().getProfileUseCase().execute();
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
    } else if (result.status === 'SUCCESS') {
      setBlocked(result?.data?.isBlocked() ?? true);
    }
  };

  const getCategories = async () => {
    console.log('start call useCategory');
    setIsLoadingCate(true);
    const result = await DIContainer.getInstance().getCategoryListUseCase().execute();
    setState(result.status);
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
    } else if (result.status === 'SUCCESS') {
      setCategories(result?.data?.filter(e => !e.categoryCode.includes('SUB')));
    }
    setIsLoadingCate(false);
  };

  const gotoPaymentBill = async (category: CategoryModel, isAddContactFlow: boolean = false) => {
    if (isAddContactFlow) {
      navigation.navigate(ScreenNames.SaveBillContactScreen, {category});
      return;
    }
    console.log('checkCustomerDetail');
    const result = await DIContainer.getInstance().getValidateCustomerUseCase().execute();
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
    } //TODO : navigate to next screen
    if (category.id === 'MB-MR') {
      navigation.navigate('PaymentPhoneScreen', {category});
    } else {
      navigation.navigate('PaymentBillScreen', {category});
    }
  };

  const undevelopedFeature = () => {
    hostSharedModule.d.domainService?.undevelopedFeature();
  };

  return {
    isBlocked,
    undevelopedFeature,
    gotoPaymentBill,
    categories,
    isLoadingCate,
  };
};

export type Props = ReturnType<typeof usePaymentHome>;

export default usePaymentHome;
