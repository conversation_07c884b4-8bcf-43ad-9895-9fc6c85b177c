import {hostSharedModule} from 'msb-host-shared-module';
import {MSBPage, useMSBStyles} from 'msb-shared-component';
import {View} from 'react-native';

import BillContainerTab from './components/BillContainerTab.tsx';
import FeatureSection from './components/feature-section/index.tsx';
import React from 'react';
import {translate} from '../../locales/i18n.ts';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import usePaymentHome from './hook.ts';
import CategorySection from './components/category-selection/index.tsx';
import {BeneficiaryProvider} from './components/hook.tsx';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {makeStyle} from './styles.ts';

const PaymentHomePage = () => {
  // const navigation = useNavigation();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentHomePage'>>();
  const hook = usePaymentHome();
  const {styles} = useMSBStyles(makeStyle);

  const onShowBottomSheet = (isAddContactFlow: boolean = false) => {
    hostSharedModule.d.domainService.showBottomSheet({
      header: translate('paymentHome.selectService'),
      children: (
        <BottomSheetView>
          <View style={styles.bottomSheetContentContainer}>
            <CategorySection
              categories={hook.categories ?? []}
              onSelect={item => {
                hostSharedModule.d.domainService.hideBottomSheet();

                hook.gotoPaymentBill(item, isAddContactFlow);
              }}
            />
          </View>
        </BottomSheetView>
      ),
    });
  };

  return (
    <BeneficiaryProvider navigation={navigation}>
      <MSBPage
        isScrollable={false}
        headerProps={{hasBack: true, title: translate('paymentHome.title')}}
        style={styles.container}>
        {/* <ImageBackground source={Images.bgMain} style={styles.container}> */}

        <View style={styles.container}>
          <FeatureSection
            categories={hook.categories?.slice(0, 3) ?? []}
            onSelect={hook.gotoPaymentBill}
            onOpenAllCategory={onShowBottomSheet}
            onOpenAutomaticRewards={hostSharedModule.d.domainService.undevelopedFeature}
            loading={hook.isLoadingCate}
          />
          <View style={styles.contactCard}>
            {
              <BillContainerTab
                isBlocked={hook.isBlocked}
                onAddNewContact={() => {
                  onShowBottomSheet(true);
                }}
              />
            }
          </View>
        </View>
      </MSBPage>
    </BeneficiaryProvider>
  );
};

export const showDialogIMBM = () => {
  hostSharedModule.d.domainService?.showPopup({
    title: 'Không thể thực hiện giao dịch',
    content:
      'Quý khách đang sử dụng dịch vụ truy vấn (thuộc gói IBMB), vì vậy không thể sử dụng tính năng này \n\n DC-12345',
    confirmBtnText: 'Đóng',
  });
};

export default PaymentHomePage;
