// import StyleCommon from '../../commons/Styles';
import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({SizeGlobal, ColorGlobal, ColorAlias, SizeAlias, Shadow}) => {
  return {
    contactCard: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.Radius3,
      borderWidth: SizeGlobal.Size25,
      borderColor: ColorAlias.BorderDefault,
      flexDirection: 'column',
      flex: 1,
      marginHorizontal: SizeAlias.SpacingSmall,
      // marginBottom: SizeAlias.SpacingMedium,
      // ...StyleCommon.boxShadow,
      ...Shadow.center,
    },
    container: {
      flex: 1,
      paddingBottom: 0,
    },
    bottomSheetContentContainer: {
      flex: 1,
      marginBottom: SizeAlias.SpacingMedium,
    },
    iconSize: {
      height: SizeAlias.IconMedium,
      width: SizeAlias.IconMedium,
    },
    moneyHubContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    moneyHubItem: {
      alignItems: 'center',
      flexDirection: 'column',
    },
    scheduleTransfer: {
      flexDirection: 'row',
      justifyContent: 'center',
    },
    scheduleTransferItem: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    spacing: {
      height: SizeGlobal.Size150,
    },
    spacingHorizontal: {
      width: SizeGlobal.Size150,
    },
  };
});
