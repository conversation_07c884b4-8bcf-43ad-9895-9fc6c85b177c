import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {useMemo} from 'react';
import {PAYMENT_TYPE} from '../../commons/Constants';

const usePaymentResultDetail = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentResultDetailScreen'>>();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentResultDetailScreen'>>();
  const {paymentInfo} = route.params;
  const paymentType = paymentInfo.paymentValidate?.paymentType;
  const isTopupAccount = paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT;

  const totalAmount = useMemo(() => {
    if (
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT
    ) {
      return paymentInfo.paymentValidate.transferTransactionInformation?.instructedAmount?.amount;
    }
    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);
    return _totalAmount;
  }, [paymentInfo]);

  const goBack = () => {
    navigation.goBack();
  };

  return {
    paymentInfo,
    totalAmount,
    goBack,
    isTopupAccount,
  };
};

export type Props = ReturnType<typeof usePaymentResultDetail>;

export default usePaymentResultDetail;
