import {MSBFolderImage, MSBIcon, MSBIconSize, MSBPage, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {ScrollView, Text, View} from 'react-native';

import {translate} from '../../locales/i18n';
import useTransferResultDetail from './hook';
import BillInfo from '../../components/bill-info';
import BillItemInfo from '../../components/bill-item-info';
import TransferResultView from '../payment-result/components/transfer-result';
import {makeStyle} from './styles';
import FormatUtils from '../../utils/FormatUtils';
import Utils from '../../utils/Utils';
import PaymentBillInfo from '../../components/payment-bill-info';

const PaymentResultDetailScreen = () => {
  const {paymentInfo, goBack, totalAmount, isTopupAccount} = useTransferResultDetail();
  const {styles} = useMSBStyles(makeStyle);

  return (
    <MSBPage
      headerProps={{
        title: paymentInfo.title,
        isLogo: true,
        rightButtons: [
          {
            icon: 'delete-cancel-close',
            onPress: goBack,
          },
        ],
      }}
      style={styles.container}>
      <ScrollView style={styles.scrollViewContainer}>
        <View style={styles.contentContainer}>
          <TransferResultView
            resultType={paymentInfo.paymentResultType}
            amount={totalAmount}
            date={new Date().toString()}
            paymentType={paymentInfo.paymentValidate?.paymentType}
          />
          {Utils.isEmpty(paymentInfo.qrPaymentInfo) ? (
            <BillInfo
              title={translate('paymentBill.title')}
              style={styles.marginTop16}
              styleName={styles.txtName}
              name={paymentInfo?.billInfo?.billList?.[0].custName}
              bankName={paymentInfo?.categoryName}
              bankLogo={paymentInfo?.provider?.subGroupId?.toString()}
              bankAlias={paymentInfo?.billInfo?.billCode}
            />
          ) : (
            <PaymentBillInfo
              style={styles.accountInfo}
              merchantName={paymentInfo?.qrPaymentInfo?.merchantName ?? ''}
              storeId={paymentInfo?.qrPaymentInfo?.storeId ?? ''}
            />
          )}
          <View style={styles.marginTop16} />
          <Text style={styles.txtTitle}>{translate('paymentInfor.sourceAccount')}</Text>
          <View style={styles.sender}>
            <MSBIcon folderIcon={MSBFolderImage.LOGO_BANK} icon={'MSB'} iconSize={MSBIconSize.SIZE_32} />
            <MSBTextBase
              style={[styles.senderName]}
              numberOfLines={1}
              content={FormatUtils.removeVietnameseTones(
                paymentInfo?.originatorAccount?.name?.toLocaleUpperCase() ?? '',
              )}
            />
          </View>

          {!Utils.isEmpty(paymentInfo.qrPaymentInfo) && (
            <BillItemInfo
              style={styles.marginTop16}
              title={translate('paymentConfirm.content')}
              value={paymentInfo?.qrPaymentInfo?.remark}
            />
          )}
          <BillItemInfo
            style={styles.marginTop16}
            title={translate('paymentConfirm.transactionFee')}
            value={translate('paymentResult.free')}
          />

          {!isTopupAccount && Utils.isEmpty(paymentInfo.qrPaymentInfo) && (
            <View style={[styles.flex]}>
              <View style={styles.marginTop16} />
              <MSBTextBase style={styles.txtTitle}>{'Kỳ thanh toán'}</MSBTextBase>
              {paymentInfo?.billInfo?.billList?.map(item => (
                <MSBTextBase style={styles.txtValue} content={item.period} />
              ))}
            </View>
          )}

          {/* <View style={[styles.flex]}>
            <View style={styles.marginTop16} />
            <MSBTextBase style={styles.txtTitle}>{translate('paymentConfirm.paymentCode')}</MSBTextBase>
            {paymentInfo.additionalInfo?.split(',')?.map(item => (
              <MSBTextBase style={styles.txtValue} content={item} />
            ))}
          </View> */}

          {paymentInfo.additionalInfo && (
            <View>
              <View style={styles.marginTop16} />
              <MSBTextBase style={styles.txtTitle}>{translate('paymentConfirm.paymentCode')}</MSBTextBase>
              {paymentInfo.additionalInfo?.split(',')?.map(item => (
                <MSBTextBase style={styles.txtValue} content={item} />
              ))}
            </View>
          )}

          {/* <BillItemInfo
            style={styles.marginTop16}
            title={translate('paymentConfirm.paymentCode')}
            value={paymentInfo.additionalInfo}
          /> */}
          {/*
          <BillItemInfo
            style={styles.marginTop16}
            title={translate('paymentResult.paymentRefCode')}
            value={paymentInfo.billInfo?.queryRef}
          /> */}
        </View>
      </ScrollView>
    </MSBPage>
  );
};

export default PaymentResultDetailScreen;
