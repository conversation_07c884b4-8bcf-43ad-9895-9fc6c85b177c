import {ColorAlias, createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({SizeAlias, ColorDataView, Typography, SizeGlobal}) => {
  return {
    container: {
      flex: 1,
      paddingVertical: SizeAlias.Spacing2xSmall,
    },
    contentContainer: {
      backgroundColor: ColorAlias.BackgroundWhite,
      paddingHorizontal: SizeAlias.SpacingSmall,
      borderRadius: SizeAlias.Radius3,
      padding: SizeAlias.SpacingSmall,
    },
    scrollViewContainer: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.Spacing2xSmall,
    },
    sender: {
      marginTop: SizeAlias.SpacingXMSmall,
      flexDirection: 'row',
      alignItems: 'center',
    },
    senderName: {
      ...Typography?.base_semiBold,
      color: ColorDataView.TextMain,
      marginLeft: 12,
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch',
    },
    marginTop16: {
      marginTop: SizeAlias.SpacingSmall,
      backgroundColor: 'transparent',
    },
    txtName: {
      ...Typography?.base_semiBold,
      // ...Tpg.base_semiBold,
      color: ColorDataView.TextMain,
    },
    txtTitle: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
      marginBottom: SizeAlias.Spacing4xSmall,
    },
    txtValue: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },
    flex: {
      flex: 1,
    },
    accountInfo: {
      marginTop: SizeGlobal.Size500,
    },
  };
});
