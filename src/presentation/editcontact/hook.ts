import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {hostSharedModule} from 'msb-host-shared-module';
import {useEffect, useState} from 'react';
import {PopupType} from 'msb-host-shared-module/dist/types/PopupType';

import {ContactType} from '../../commons/Constants.ts';
import Utils from '../../utils/Utils.ts';
import {DIContainer} from '../../di/DIContainer.ts';
import {showErrorPopup} from '../../utils/PopupUtils.ts';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {MyBillContactModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel.ts';
import {EditBillContactRequest} from '../../data/models/edit-bill-contact/EditBillContactRequest.ts';
import {SaveBillContactRequest} from '../../data/models/save-bill-contact/SaveBillContactRequest.ts';
import {CustomError} from '../../core/MSBCustomError.ts';

const useEditContact = (data: MyBillContactModel) => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'EditBillContactScreen'>>();
  //  const {contact} = route.params || {};
  const [aliasName, setAliasName] = useState<string>(data.alias ?? '');
  const [isEnableAutomatic, setEnableAutomatic] = useState<boolean>(false);
  const [typing, setTyping] = useState<boolean>(false);
  const [continueEnable, setContinueEnable] = useState<boolean>(false);
  const [contacts, setContacts] = useState<MyBillContactModel[]>([]);
  const navigation = useNavigation();

  // #region effects
  useEffect(() => {
    fetchContacts();
  }, []);

  useEffect(() => {
    setAliasName(prevState => {
      setContinueEnable(Utils.isEmpty(data?.id ?? '') || prevState !== data.alias);
      return prevState;
    });
  }, [aliasName, data.alias, data?.id]);

  // #endregion

  //#region get data functions

  const fetchContacts = async () => {
    const result = await DIContainer.getInstance().getMyBillContactListUseCase().execute();
    if (result.status === 'ERROR') {
      return;
    }
    setContacts(result.data ?? []);
  };

  // #endregion

  // Navigation

  const goHome = () => {
    navigation?.reset({
      index: 0,
      routes: [
        {
          name: 'SegmentStack' as never,
        },
      ],
    });
  };

  const verifyDuplicateContact = (_contacts: MyBillContactModel[]): string | null => {
    const bankCode = data.accounts[0].bankCode;
    const accNumber = data.accounts[0].accountNumber;
    console.log('CHECK DUPLICATE', data, _contacts);
    return (
      _contacts.find(item =>
        item?.accounts?.some(acc => acc.accountNumber === accNumber?.trim() && acc.bankCode === bankCode),
      )?.id ?? null
    );
  };

  const onSubmit = async () => {
    const idTemp = verifyDuplicateContact(contacts);
    if (idTemp == null) {
      await createContact();
      return;
    }
    editContact(idTemp ?? data.id);
  };

  const editContact = async (id: string) => {
    const request: EditBillContactRequest = {
      id: id,
      name: data.name ?? '',
      alias: aliasName.trim(),
      category: ContactType.BILLPAY,
      accounts: [
        {
          bankName: data.category ?? '',
          accountNumber: data.accounts[0].accountNumber ?? '',
          accountType: data.accounts[0].accountType ?? '',
          bankCode: data.accounts[0].bankCode ?? '',
          externalId: data.accounts[0].externalId ?? '',
        },
      ],
      additions: {
        favoriteStatus: isEnableAutomatic ? 'ACTIVE' : 'INACTIVE',
        reminderStatus: data.getReminderStatus() ?? 'INACTIVE',
        payableAmount: data.getPayableAmount() ?? '0',
      },
    };
    console.log('====================================');
    console.log('request update', request);
    console.log('====================================');
    const result = await DIContainer.getInstance().getEditBillContactUseCase().execute(request);
    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
      return;
    }
    Utils.showToastSuccess(`Cập nhật hoá đơn thành công`);

    navigation.goBack();
  };

  const createContact = async () => {
    const request: SaveBillContactRequest = {
      name: data.name ?? '',
      alias: aliasName.trim(),
      category: ContactType.BILLPAY,
      accounts: [
        {
          bankName: data.category ?? '',
          accountNumber: data.accounts[0].accountNumber ?? '',
          accountType: data.accounts[0].accountType ?? '',
          bankCode: data.accounts[0].bankCode ?? '',
          externalId: data.accounts[0].externalId ?? '',
        },
      ],
      additions: {
        favoriteStatus: isEnableAutomatic ? 'ACTIVE' : 'INACTIVE',
        reminderStatus: 'INACTIVE',
        payableAmount: '0',
      },
    };
    console.log('request create', request);
    console.log('====================================');
    const result = await DIContainer.getInstance().getSaveBillContactUseCase().execute(request);
    if (result.status === 'ERROR') {
      Utils.showToastError('Thêm hoá đơn không thành công');
      return;
    }
    Utils.showToastSuccess(`Thêm hoá đơn thành công`);
    navigation.goBack();
  };

  const showTransferDialog = (error?: CustomError | null | undefined, onConfirm?: (() => void) | undefined) => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: PopupType.WARNING,
      title: error?.title,
      content: error?.message,
      cancelBtnText: error?.getPrimaryAction()?.label,
      confirmBtnText: error?.getSecondaryAction()?.label,
      onConfirm: onConfirm,
    });
  };
  return {
    aliasName,
    setAliasName,
    typing,
    setTyping,
    isEnableAutomatic,
    setEnableAutomatic,
    continueEnable,
    setContinueEnable,
    onSubmit,
    goHome,
  };
};

export default useEditContact;
