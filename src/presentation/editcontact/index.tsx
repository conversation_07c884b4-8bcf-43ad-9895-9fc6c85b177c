import {
  ButtonType,
  createMSBStyleSheet,
  MSBButton,
  MSBFastImage,
  MSBFolderImage,
  MSBIcons,
  MSBInputBase,
  MSBPage,
  MSBTouchable,
  useMSBStyles,
} from 'msb-shared-component';
import React from 'react';
import {Keyboard, Pressable, View} from 'react-native';

import {translate} from '../../locales/i18n.ts';
import Utils from '../../utils/Utils.ts';
import useEditContact from './hook.ts';
import {useRoute, RouteProp} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {MyBillContactModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel.ts';

const EditBillContactScreen = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'EditBillContactScreen'>>();
  const {contact} = route.params || {};
  console.log('================================>>>>>> EDIT contact', contact);
  const hook = useEditContact(contact);
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <View style={styles.container}>
      <MSBPage
        headerProps={{
          title: translate('addContact.title'),
          hasBack: true,
          rightButtons: [{icon: MSBIcons.IconHome, onPress: hook.goHome}],
        }}
        style={styles.container}>
        <View style={styles.container}>
          <Pressable style={styles.body} onPress={Keyboard.dismiss}>
            <View style={styles.card}>
              <MSBTouchable onPress={() => {}}>
                <MSBInputBase
                  testID={'transfer.editContactScreen.enterBankRecipient'}
                  label={translate('editContact.beneficiary_bank')}
                  value={(contact as MyBillContactModel)?.accounts?.[0].bankName ?? ''}
                  onChangeText={() => {}}
                  placeholder={translate('editContact.select_bank')}
                  isDisableRemoveIcon={true}
                  childrenIconLeft={
                    (contact as MyBillContactModel)?.accounts?.[0].bankCode || '' ? (
                      <MSBFastImage
                        nameImage={(contact as MyBillContactModel)?.accounts?.[0].bankCode || ''}
                        style={styles.logo}
                        folder={contact.isTopup() ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
                      />
                    ) : (
                      <></>
                    )
                  }
                  disabled={true}
                  backgroundColorInput={theme.ColorField.SurfaceDisable}
                />
              </MSBTouchable>

              <View style={styles.space} />
              <MSBInputBase
                // containerStyle={styles.space}
                testID={'transfer.editContactScreen.enterNumberAccount'}
                label={translate('editContact.account_number')}
                value={(contact as MyBillContactModel)?.accounts?.[0].accountNumber ?? ''}
                disabled={true}
                isDisableRemoveIcon={true}
                backgroundColorInput={theme.ColorField.SurfaceDisable}
              />
              <View style={styles.space} />
              <MSBInputBase
                // containerStyle={styles.space}
                testID={'transfer.beneficiaryScreen.enterBeneficiaryName'}
                label={translate('addContact.customerName')}
                value={(contact as MyBillContactModel)?.name ?? ''}
                disabled={true}
                isDisableRemoveIcon={true}
                backgroundColorInput={theme.ColorField.SurfaceDisable}
              />
              <View style={styles.space} />
              <MSBInputBase
                testID={'transfer.editContactScreen.enterNameHint'}
                label={translate('editContact.name_alias')}
                value={hook.aliasName}
                onChangeText={(text: string) => {
                  hook.setAliasName(Utils.removeEmoji(text));
                }}
                onBlur={() => {
                  hook.setAliasName(Utils.regexNickName(hook.aliasName));
                }}
                placeholder={translate('editContact.enter_content')}
                maxLength={80}
              />
              {/* <View style={styles.space} />
              <View style={styles.favorite}>
                <View style={styles.leftFavorite}>
                  <MSBTextBase style={Tpg.base_medium} content={translate('addContact.enableAutomatic')} />
                </View>
                <View style={{paddingTop: SizeGlobal.Size100}}>
                  <ToggleSwitch isFavorite={hook.isFavorite} setFavorite={hook.setFavorite} />
                </View>
              </View> */}
            </View>
            <View style={styles.fullFlex} />
            <MSBButton
              testID={'transfer.editContactScreen.pressContinue'}
              buttonType={ButtonType.Primary}
              label={translate('paymentBill.btnContinue')}
              disabled={!hook.continueEnable}
              onPress={hook.onSubmit}
              style={styles.spaceBottom}
            />
          </Pressable>
        </View>
      </MSBPage>
    </View>
  );
};

// type switchProps = {
//   isFavorite: boolean;
//   setFavorite: (updater: (prev: boolean) => boolean) => void;
// };

// const ToggleSwitch = ({isFavorite, setFavorite}: switchProps) => {
//   const toggleSwitch = () => setFavorite(previousState => !previousState);
//   const {styles, theme} = useMSBStyles(makeStyle);
//   return (
//     <View style={styles.container}>
//       <Switch
//         trackColor={{false: theme.ColorToggle.SurfaceDefault, true: theme.ColorToggle.SurfaceSelect}}
//         thumbColor={isFavorite ? theme.ColorToggle.KnobDefault : theme.ColorToggle.KnobDisable}
//         onValueChange={toggleSwitch}
//         value={isFavorite}
//       />
//     </View>
//   );
// };

export const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, ColorField, SizeField, SizeAlias}) => {
  return {
    body: {
      flex: 1,
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    card: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.Radius3,
      flexDirection: 'column',
      justifyContent: 'space-between',
      paddingVertical: SizeAlias.SpacingSmall,
      padding: SizeAlias.SpacingSmall,
    },
    container: {
      flex: 1,
    },
    favorite: {
      borderColor: ColorField.BorderDefault,
      borderRadius: SizeField.BorderRadius,
      borderWidth: SizeField.BorderStroke,
      flexDirection: 'row',
      justifyContent: 'space-between',
      minHeight: SizeGlobal.Size1000,
      paddingHorizontal: SizeGlobal.Size400,
    },
    fullFlex: {
      flex: 1,
    },
    leftFavorite: {
      flexWrap: 'wrap',
      justifyContent: 'center',
      width: '85%',
    },
    logo: {
      height: SizeGlobal.Size800,
      marginRight: SizeAlias.SpacingSmall,
      resizeMode: 'contain',
      width: SizeGlobal.Size800,
    },
    space: {
      height: SizeGlobal.Size300,
    },
    spaceBottom: {
      marginBottom: SizeAlias.SpacingLarge,
    },
  };
});

export default EditBillContactScreen;
