import {
  ButtonType,
  createMSBStyleSheet,
  MSBButton,
  MSBIcons,
  MSBInputBase,
  MSBPage,
  MSBTouchable,
  useMSBStyles,
} from 'msb-shared-component';
import React from 'react';
import {Image, Keyboard, Pressable, Switch, View} from 'react-native';

import {translate} from '../../locales/i18n.ts';
import Utils from '../../utils/Utils.ts';
import useEditContact from './hook.ts';
import {useRoute, RouteProp} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack.tsx';
import {MyBillContactModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel.ts';

const EditBillContactScreen = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'EditBillContactScreen'>>();
  const {contact} = route.params || {};
  console.log('================================>>>>>> EDIT contact', contact);
  const hook = useEditContact(contact);
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <View style={styles.container}>
      <MSBPage
        headerProps={{
          title: translate('addContact.title'),
          hasBack: true,
          rightButtons: [{icon: MSBIcons.IconHome, onPress: hook.goHome}],
        }}
        style={styles.container}>
        <View style={styles.container}>
          <Pressable style={styles.body} onPress={Keyboard.dismiss}>
            <View style={styles.card}>
              <MSBTouchable onPress={() => {}}>
                <MSBInputBase
                  testID={'transfer.editContactScreen.enterBankRecipient'}
                  label={translate('editContact.beneficiary_bank')}
                  value={(contact as MyBillContactModel)?.accounts?.[0].bankName ?? ''}
                  onChangeText={() => {}}
                  placeholder={translate('editContact.select_bank')}
                  isDisableRemoveIcon={true}
                  childrenIconLeft={
                    Utils.getProviderIcon((contact as MyBillContactModel)?.accounts?.[0].bankCode || '') ? (
                      <Image
                        source={Utils.getProviderIcon((contact as MyBillContactModel)?.accounts?.[0].bankCode || '')}
                        style={styles.logo}
                      />
                    ) : (
                      <></>
                    )
                  }
                  disabled={true}
                  backgroundColorInput={theme.ColorField.SurfaceDisable}
                />
              </MSBTouchable>

              <View style={styles.space} />
              <MSBInputBase
                // containerStyle={styles.space}
                testID={'transfer.editContactScreen.enterNumberAccount'}
                label={translate('editContact.account_number')}
                value={(contact as MyBillContactModel)?.accounts?.[0].accountNumber ?? ''}
                disabled={true}
                isDisableRemoveIcon={true}
                backgroundColorInput={theme.ColorField.SurfaceDisable}
              />
              <View style={styles.space} />
              <MSBInputBase
                // containerStyle={styles.space}
                testID={'transfer.beneficiaryScreen.enterBeneficiaryName'}
                label={translate('addContact.customerName')}
                value={(contact as MyBillContactModel)?.name ?? ''}
                disabled={true}
                isDisableRemoveIcon={true}
                backgroundColorInput={theme.ColorField.SurfaceDisable}
              />
              <View style={styles.space} />
              <MSBInputBase
                testID={'transfer.editContactScreen.enterNameHint'}
                label={translate('editContact.name_alias')}
                value={hook.aliasName}
                onChangeText={(text: string) => {
                  hook.setAliasName(Utils.removeEmoji(text));
                }}
                onBlur={() => {
                  hook.setAliasName(Utils.regexNickName(hook.aliasName));
                }}
                placeholder={translate('editContact.enter_content')}
                maxLength={80}
              />
              {/* <View style={styles.space} />
              <View style={styles.favorite}>
                <View style={styles.leftFavorite}>
                  <MSBTextBase style={Tpg.base_medium} content={translate('addContact.enableAutomatic')} />
                </View>
                <View style={{paddingTop: SizeGlobal.Size100}}>
                  <ToggleSwitch isFavorite={hook.isFavorite} setFavorite={hook.setFavorite} />
                </View>
              </View> */}
            </View>
            <View style={styles.fullFlex} />
            <MSBButton
              testID={'transfer.editContactScreen.pressContinue'}
              buttonType={ButtonType.Primary}
              label={translate('paymentBill.btnContinue')}
              disabled={!hook.continueEnable}
              onPress={hook.onSubmit}
              style={styles.spaceBottom}
            />
          </Pressable>
        </View>
      </MSBPage>
    </View>
  );
};

type switchProps = {
  isFavorite: boolean;
  setFavorite: (updater: (prev: boolean) => boolean) => void;
};

const ToggleSwitch = ({isFavorite, setFavorite}: switchProps) => {
  const toggleSwitch = () => setFavorite(previousState => !previousState);
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={styles.container}>
      <Switch
        trackColor={{false: '#767577', true: 'orange'}}
        thumbColor={isFavorite ? '#ffffff' : '#f4f3f4'}
        onValueChange={toggleSwitch}
        value={isFavorite}
      />
    </View>
  );
};

export const makeStyle = createMSBStyleSheet(
  ({ColorGlobal, SizeGlobal, ColorField, SizeField, ColorDataView, SizeAlias, Shadow, Typography}) => {
    return {
      body: {
        flex: 1,
        paddingHorizontal: 16,
      },
      card: {
        backgroundColor: ColorGlobal.NeutralWhite,
        borderRadius: 12,
        flexDirection: 'column',
        justifyContent: 'space-between',
        paddingVertical: 16,
        padding: 16,
      },
      container: {
        flex: 1,
      },
      favorite: {
        borderColor: ColorField.BorderDefault,
        borderRadius: SizeField.BorderRadius,
        borderWidth: SizeField.BorderStroke,
        flexDirection: 'row',
        justifyContent: 'space-between',
        minHeight: 40,
        paddingHorizontal: SizeGlobal.Size400,
      },
      fullFlex: {
        flex: 1,
      },
      leftFavorite: {
        flexWrap: 'wrap',
        justifyContent: 'center',
        width: '85%',
      },
      logo: {
        height: 32,
        marginRight: 16,
        resizeMode: 'contain',
        width: 32,
      },
      space: {
        height: 12,
      },
      spaceBottom: {
        marginBottom: 25,
      },
    };
  },
);

export default EditBillContactScreen;
