import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(
  ({ColorGlobal, ColorField, ColorAlias, ColorDataView, SizeAlias, Shadow, Typography}) => {
    return {
      btnConfirm: {
        marginHorizontal: SizeAlias.SpacingSmall,
      },
      container: {
        flex: 1,
      },

      contentContainer: {
        backgroundColor: ColorGlobal.NeutralWhite,
        borderRadius: SizeAlias.SpacingXSmall,
        padding: SizeAlias.SpacingSmall,
        ...Shadow.center,
      },
      contentContainerScrollView: {
        padding: SizeAlias.SpacingSmall,
      },

      imageBackground: {
        flex: 1,
        resizeMode: 'stretch',
      },

      space: {
        marginTop: SizeAlias.SpacingSmall,
      },
      txtName: {
        ...Typography?.base_semiBold,
        color: ColorDataView.TextMain,
      },
      // transfer confirm
      transferConfirm: {
        borderBottomColor: ColorField.BorderDefault,
        borderBottomWidth: 1,
        paddingBottom: SizeAlias.SpacingSmall,
      },
      txtAmount: {
        ...Typography?.h4_bold,
        color: ColorDataView.TextMain,
        marginTop: SizeAlias.Spacing4xSmall,
      },
      txtCurrency: {
        ...Typography?.base_semiBold,
        color: ColorGlobal.Neutral600,
      },
      txtAmountWords: {
        ...Typography?.small_regular,
        color: ColorDataView.TextSub,
        marginTop: SizeAlias.Spacing4xSmall,
      },
      txtTransferConfirm: {
        ...Typography?.base_semiBold,
        color: ColorDataView.TextSub,
      },
      txtTitle: {
        ...Typography?.small_regular,
        color: ColorDataView.TextSub,
        marginTop: SizeAlias.Spacing4xSmall,
      },
      txtValue: {
        ...Typography?.base_medium,
        color: ColorDataView.TextMain,
        marginTop: SizeAlias.Spacing4xSmall,
      },
    };
  },
);
