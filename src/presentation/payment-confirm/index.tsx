import {MSBButton, MSBPage, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {ScrollView, View} from 'react-native';

import {translate} from '../../locales/i18n';
import FormatUtils from '../../utils/FormatUtils';
// import useTransferConfirm from './hook';
import BillInfo from '../../components/bill-info';
import BillItemInfo from '../../components/bill-item-info';
import usePaymentConfirm from './hook';
import {makeStyle} from './styles';
import Utils from '../../utils/Utils';
import PaymentBillInfo from '../../components/payment-bill-info';

const PaymentConfirmScreen = () => {
  const {onConfirm, paymentInfo, totalAmount, isLoading, hasPeriod, isTopup, endTransactionConfirm} =
    usePaymentConfirm();
  const {styles} = useMSBStyles(makeStyle);

  return (
    <MSBPage
      isScrollable={false}
      headerProps={{
        hasBack: true,
        rightButtons: [{icon: 'tone-home', onPress: endTransactionConfirm}],
        title: paymentInfo.title,
      }}
      style={styles.container}>
      <View style={styles.container}>
        <ScrollView style={styles.container} contentContainerStyle={styles.contentContainerScrollView}>
          <View style={styles.contentContainer}>
            {!paymentInfo.provider?.isViettelBill() || Utils.isEmpty(paymentInfo.qrPaymentInfo) ? (
              <ConfirmTransfer amount={totalAmount?.toString()} />
            ) : null}
            {Utils.isEmpty(paymentInfo.qrPaymentInfo) ? (
              <BillInfo
                title={isTopup ? translate('paymentHome.topup') : translate('paymentHome.billing')}
                style={styles.space}
                styleName={styles.txtName}
                name={paymentInfo?.billInfo?.billList?.[0].custName} //TODO: check cho dien thoai tra truoc/tra sau
                bankName={paymentInfo?.categoryName}
                bankLogo={paymentInfo?.provider?.subGroupId?.toString()}
                bankAlias={paymentInfo?.billInfo?.billCode}
                isUppercase={!paymentInfo.provider?.isTopup()}
              />
            ) : (
              <PaymentBillInfo
                style={styles.accountInfo}
                merchantName={paymentInfo?.qrPaymentInfo?.merchantName ?? ''}
                storeId={paymentInfo?.qrPaymentInfo?.storeId ?? ''}
              />
            )}

            <BillInfo
              title={translate('paymentInfor.sourceAccount')}
              style={styles.space}
              styleName={styles.txtName}
              name={FormatUtils.removeVietnameseTones(paymentInfo?.originatorAccount?.name?.toLocaleUpperCase() ?? '')}
              bankName={paymentInfo?.originatorAccount?.bankName}
              bankLogo={paymentInfo?.originatorAccount?.bankName}
              bankAlias={paymentInfo?.originatorAccount?.accountNo}
            />
            {!Utils.isEmpty(paymentInfo.qrPaymentInfo) && (
              <BillItemInfo
                style={styles.space}
                title={translate('paymentConfirm.content')}
                value={paymentInfo?.qrPaymentInfo?.remark}
              />
            )}
            <BillItemInfo
              style={styles.space}
              title={translate('paymentConfirm.transactionFee')}
              value={translate('screens.paymentConfirm.transactionFee')}
            />
            {hasPeriod && Utils.isEmpty(paymentInfo.qrPaymentInfo) && (
              <View style={[styles.container]}>
                <View style={styles.space} />
                <MSBTextBase style={styles.txtTitle} content={translate('screens.paymentConfirm.paymentPeriodTitle')} />
                {paymentInfo?.billInfo?.billList?.map(item => (
                  <MSBTextBase style={styles.txtValue} content={`${item.period}`} />
                ))}
              </View>
            )}
          </View>
        </ScrollView>
      </View>
      <MSBButton
        testID={'payment.TransferConfirmScreen.pressToConfirm'}
        label={translate('paymentConfirm.confirm')}
        style={styles.btnConfirm}
        onPress={onConfirm}
        isLoading={isLoading}
      />
    </MSBPage>
  );
};

const ConfirmTransfer = ({amount}: {amount?: string}) => {
  const {styles} = useMSBStyles(makeStyle);

  return (
    <View style={styles.transferConfirm}>
      <MSBTextBase style={styles.txtTransferConfirm} content={translate('paymentConfirm.transferConfirm')} />
      <MSBTextBase style={styles.txtAmount}>
        {`${FormatUtils.formatPrice(amount)}`}
        <MSBTextBase style={styles.txtCurrency} content={translate('screens.paymentConfirm.currencyVnd')} />
      </MSBTextBase>
      <MSBTextBase style={styles.txtAmountWords} content={FormatUtils.numberToWordsVi(Number(amount))} />
    </View>
  );
};

export default PaymentConfirmScreen;
