import {RouteProp, useNavigation, useRoute, NavigationProp} from '@react-navigation/native';

import ScreenNames from '../../commons/ScreenNames';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {hostSharedModule} from 'msb-host-shared-module';
import {translate} from '../../locales/i18n';
import {useMemo, useState} from 'react';
import {DIContainer} from '../../di/DIContainer';
import {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';
import {PAYMENT_ORDER_STATUS, PAYMENT_TYPE} from '../../commons/Constants';
import {showErrorPopup} from '../../utils/PopupUtils';
// import Utils from '../../utils/Utils';

const usePaymentConfirm = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentConfirmScreen'>>();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentConfirmScreen'>>();

  const {paymentInfo, hasPeriod = true} = route.params;

  const isTopup = paymentInfo.provider?.isTopup();

  const [isLoading, setLoading] = useState<boolean>(false);

  const totalAmount = useMemo(() => {
    if (
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_ACCOUNT ||
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.TOPUP_CREDIT ||
      paymentInfo.paymentValidate?.paymentType === PAYMENT_TYPE.QR_PAYMENT
    ) {
      return paymentInfo.paymentValidate?.transferTransactionInformation?.instructedAmount?.amount;
    }
    const _totalAmount = paymentInfo?.billInfo?.billList?.reduce((sum, bill) => sum + (bill.amount || 0), 0);
    return _totalAmount;
  }, [paymentInfo]);

  const onConfirm = () => {
    // navigation.navigate(ScreenNames.PaymentResultScreen);
    paymentOrder();
  };

  const paymentOrder = async () => {
    if (paymentInfo.paymentValidate) {
      setLoading(true);
      const params: PaymentOrderRequest = {
        ...paymentInfo.paymentValidate,
      };
      console.log('request params', params);
      const result = await DIContainer.getInstance().getPaymentOrderUseCase().execute(params);
      console.log('result', result);

      setLoading(false);
      if (result.status === 'SUCCESS') {
        getOrderStatus(paymentInfo?.paymentValidate.id ?? '');
      } else {
        showErrorPopup(result.error);
        navigation.goBack();
      }
    }
  };

  const getOrderStatus = async (id: string) => {
    const result = await DIContainer.getInstance().getPaymentOrderStatusUseCase().execute({paymentMode: '', id: id});
    console.log('result', result);
    if (result.status === 'SUCCESS') {
      setLoading(false);
      navigation.navigate(ScreenNames.PaymentResultScreen, {
        paymentInfo: {
          ...paymentInfo,
          paymentResultType: result.data?.bankStatus ?? PAYMENT_ORDER_STATUS.REJECTED,
          additionalInfo: result.data?.additions?.t24TraceCode,
        },
      });
    } else {
      setLoading(false);
      showErrorPopup(result.error);
      // Utils.checkErrorSystem();
    }
  };

  const endTransactionConfirm = () => {
    hostSharedModule.d.domainService?.showPopup({
      iconType: 'WARNING',
      title: translate('paymentConfirm.endOfTransaction'),
      content: translate('paymentConfirm.endOfTransactionDescription'),
      cancelBtnText: translate('paymentConfirm.endOfTransaction'),
      confirmBtnText: translate('paymentConfirm.close'),
      onCancel: () => endOfTransaction(),
    });
  };

  const endOfTransaction = () => {
    goHome();
  };

  const goHome = () => {
    navigation?.reset({
      index: 0,
      routes: [
        {
          name: 'SegmentStack',
        },
      ],
    });
  };

  return {
    onConfirm,
    paymentInfo,
    totalAmount,
    endTransactionConfirm,
    isLoading,
    hasPeriod,
    isTopup,
  };
};

export type Props = ReturnType<typeof usePaymentConfirm>;

export default usePaymentConfirm;
