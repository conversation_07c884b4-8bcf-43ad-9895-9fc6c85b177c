import {Color<PERSON>lias, ColorLabelCaption, createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({Size<PERSON>lias, Typography, Shadow}) => {
  return {
    accountInfo: {
      backgroundColor: ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.SpacingXSmall,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.SpacingXSmall,
      ...Shadow.center,
    },
    amountInput: {
      ...Typography?.base_medium,
      marginTop: SizeAlias.SpacingSmall,
    },
    btnContinue: {
      marginHorizontal: SizeAlias.SpacingSmall,
    },
    container: {
      flex: 1,
    },

    contentContainerScrollView: {
      padding: SizeAlias.SpacingSmall,
    },

    contentContainer: {
      backgroundColor: ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.Radius3,
      marginTop: Size<PERSON>lias.SpacingXLarge,
      padding: SizeAlias.SpacingSmall,
      ...Shadow.center,
    },
    contentLabel: {
      color: ColorLabelCaption.TextMain,
      marginRight: SizeAlias.Spacing4xSmall,
      paddingBottom: SizeAlias.Spacing4xSmall,
    },
    flexDirectionRow: {
      flexDirection: 'row',
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch',
    },
    scrollViewContentContainer: {
      paddingBottom: SizeAlias.Spacing3xLarge,
    },
  };
});
