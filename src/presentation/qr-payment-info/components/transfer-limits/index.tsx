// import {createMSBStyleSheet, MSBTextBase, MSBIcon, useMSBStyles, ColorItem, SizeGlobal} from 'msb-shared-component';
// import Tpg from 'msb-shared-component/dist/theme/design-system/fonts/typography';
// import React from 'react';
// import {View} from 'react-native';

// import {translate} from '@locales/i18n';
// import {TransferLimitProps} from './types';
// import {BottomSheetView} from '@gorhom/bottom-sheet';

// const TransferLimits = ({style, limits}: TransferLimitProps) => {
//   const {styles} = useMSBStyles(makeStyle);
//   return (
//     <BottomSheetView style={[styles.container, style]}>
//       <View style={styles.itemContainer}>
//         <MSBIcon iconColor={ColorItem.IconBrand} icon={'tone-money'} iconSize={SizeGlobal.Size800} />
//         <View style={styles.itemContent}>
//           <MSBTextBase style={styles.txtTitle}>{translate('transferInfo.dayTransactionLimit')}</MSBTextBase>
//           {/* <TextAmount amountStyle={styles.txtAmount} currencyStyle={styles.txtAmount} amount={limits?.dailyLimit} /> */}
//         </View>
//       </View>
//       <View style={styles.itemContainer}>
//         <MSBIcon iconColor={ColorItem.IconBrand} icon={'tone-money'} iconSize={SizeGlobal.Size800} />
//         <View style={styles.itemContent}>
//           <MSBTextBase style={styles.txtTitle}>{translate('transferInfo.dayRemainingLimit')}</MSBTextBase>
//           {/* <TextAmount
//             amountStyle={styles.txtAmount}
//             currencyStyle={styles.txtAmount}
//             amount={limits?.dailyAvailableLimit}
//           /> */}
//         </View>
//       </View>
//     </BottomSheetView>
//   );
// };

// export default TransferLimits;
// const makeStyle = createMSBStyleSheet(({SizeGlobal: SizeGlobalStyle, ColorItem: ColorItemStyle}) => {
//   return {
//     btn: {
//       marginVertical: SizeGlobalStyle.Size300,
//     },
//     container: {
//       paddingHorizontal: SizeGlobalStyle.Size400,
//     },
//     itemContainer: {
//       alignItems: 'center',
//       flexDirection: 'row',
//       marginVertical: SizeGlobalStyle.Size300,
//     },
//     itemContent: {
//       marginLeft: SizeGlobalStyle.Size300,
//     },
//     txtAmount: {
//       ...Tpg.small_regular,
//       color: ColorItemStyle.TextSub,
//     },
//     txtTitle: {
//       ...Tpg.base_medium,
//       color: ColorItemStyle.TextMain,
//     },
//   };
// });
