import {
  MSBAmountInput,
  MSBButton,
  MSBFastImage,
  MSBFolderImage,
  MSBLoadingItemSkeleton,
  MSBPage,
  MSBTextBase,
  MSBTouchable,
  SizeGlobal,
  Tpg,
  useMSBStyles,
} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import useQRPaymentInfo from './hook.ts';
import {translate} from '../../locales/i18n.ts';
import {makeStyle} from './styles.ts';
import {Federated} from '@callstack/repack/client';
import SourceAccountList from '../../components/source-account-list/index.tsx';
import PaymentBillInfo from '../../components/payment-bill-info/index.tsx';
import TransferContentInput from '../../components/transfer-content-input/index.tsx';
import Utils from '../../utils/Utils.ts';

/**
 * <PERSON>hi ở build time sẽ sử dụng MFv1 nên là không thể sử dụng dynamic import của MFv2
 * => Sử dụng {@link Federated.importModule} để bundle
 *
 * Lưu ý!: không tách biệt điều kiện process.env.NODE_ENV === 'production' ra hàm riêng biệt vì lúc build sẽ làm cho dynamic import() được bundle vào
 * => gây ra lỗi lúc build
 *
 * Không thể sử dụng được dynamic import() từ hàm
 * => !Nên mỗi khi load module thì phải định nghĩa lại như dưới
 */
const SourceAccount = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('TransferModule', './SourceAccount')
    : import('TransferModule/SourceAccount'),
);
const QRPaymentInfoScreen = () => {
  const {styles} = useMSBStyles(makeStyle);

  const {
    onContinue,
    paymentInfo,
    sourceAccDefault,
    isLoadingSourceAccount,
    onSelectAccount,
    isLoading,
    goQrScreen,
    amountErrorMessage,
    disableTransferBtn,
    amount,
    remark, // nội dung chuyển khoanr
    setRemark,
    amountOnChangeText,
  } = useQRPaymentInfo(
    accountList => (
      <SourceAccountList accSelected={sourceAccDefault} accountList={accountList!} onSelectAcount={onSelectAccount} />
    ),
    // (transferLimits, onChangeTransferLimits) => (
    //   <TransferLimits limits={transferLimits} onChangeTransferLimits={onChangeTransferLimits} />
    // ),
  );
  // console.log('paymentInfo====>', paymentInfo);
  return (
    <MSBPage
      isScrollable={false}
      headerProps={{
        hasBack: true,
        rightButtons: [{icon: 'tone-qr', onPress: goQrScreen}],
        title: `${translate('qrPaymentInfo.payment')}`,
      }}
      style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.contentContainerScrollView}
        contentContainerStyle={styles.scrollViewContentContainer}>
        <PaymentBillInfo
          style={styles.accountInfo}
          merchantName={paymentInfo?.merchantName ?? ''}
          storeId={paymentInfo?.storeId ?? ''}
        />

        <View style={styles.contentContainer}>
          {isLoadingSourceAccount ? (
            <MSBLoadingItemSkeleton loading={isLoadingSourceAccount} />
          ) : (
            <SourceAccount
              title={translate('paymentInfor.sourceAccount')}
              accNo={sourceAccDefault?.BBAN}
              onSelectAccount={onSelectAccount}
            />
          )}
          <MSBAmountInput
            testID={'transfer.transferInfoScreen.enterAmount'}
            value={amount}
            placeholder={translate('qrPaymentInfo.amountPlaceholder')}
            containerStyle={styles.amountInput}
            maxLength={12}
            disabled={!Utils.isEmpty(paymentInfo?.amount)}
            onChangeText={amountOnChangeText}
            contentLabel={<AmountLabel openBottomSheetTransferLimits={() => {}} />}
            errorContent={amountErrorMessage}
            label={''}
            isDisableRemoveIcon={!Utils.isEmpty(paymentInfo?.amount)}
          />
          <TransferContentInput
            testID={'transfer.transferInfoScreen.enterContent'}
            containerStyle={styles.amountInput}
            label={translate('common.content')}
            placeholder={translate('common.contentPlaceholder')}
            disabled={!Utils.isEmpty(paymentInfo?.remark)}
            value={remark}
            onChangeText={(text: string) => {
              setRemark(text);
            }}
            isDisableRemoveIcon={!Utils.isEmpty(paymentInfo?.remark)}
          />
        </View>
      </KeyboardAwareScrollView>
      <MSBButton
        testID={'payment.transferInfoScreen.pressToContinue'}
        label={'Tiếp tục'}
        isLoading={isLoading}
        style={styles.btnContinue}
        onPress={onContinue}
        disabled={disableTransferBtn}
      />
    </MSBPage>
  );
};

const AmountLabel = ({openBottomSheetTransferLimits}: {openBottomSheetTransferLimits: () => void}) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={styles.flexDirectionRow}>
      <MSBTextBase content={translate('qrPaymentInfo.amount')} type={Tpg.small_medium} style={styles.contentLabel} />
      <MSBTouchable onPress={openBottomSheetTransferLimits}>
        <MSBFastImage
          folder={MSBFolderImage.ICON_SVG}
          nameImage="info-tooltip-circle"
          style={{width: SizeGlobal.Size500, height: SizeGlobal.Size500}}
        />
      </MSBTouchable>
    </View>
  );
};
export default QRPaymentInfoScreen;
