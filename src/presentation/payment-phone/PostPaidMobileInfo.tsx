import React, {useMemo, useState} from 'react';
import {View} from 'react-native';
import {
  ButtonType,
  MSBButton,
  MSBFastImage,
  MSBFolderImage,
  MSBTextBase,
  createMSBStyleSheet,
  useMSBStyles,
} from 'msb-shared-component';
import {translate} from '../../locales/i18n';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import {usePaymentMobile} from './hook';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Federated} from '@callstack/repack/client';

/**
 * Khi ở build time sẽ sử dụng MFv1 nên là không thể sử dụng dynamic import của MFv2
 * => Sử dụng {@link Federated.importModule} để bundle
 *
 * Lưu ý!: không tách biệt điều kiện process.env.NODE_ENV === 'production' ra hàm riêng biệt vì lúc build sẽ làm cho dynamic import() được bundle vào
 * => gây ra lỗi lúc build
 *
 * Không thể sử dụng được dynamic import() từ hàm
 * => !Nên mỗi khi load module thì phải định nghĩa lại như dưới
 */
const SourceAccount = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('TransferModule', './SourceAccount')
    : import('TransferModule/SourceAccount'),
);
const PostPaidMobileInfoScreen = ({
  customerName,
  phoneNumber,
  provider,
  category,
  amount,
}: {
  customerName: string | undefined;
  phoneNumber: string;
  provider?: ProviderModel;
  category: CategoryModel;
  amount: number;
}) => {
  const {sourceAccDefault, paymentBill, handleBillValidate, isLoadingValidate, onSelectAccount} = usePaymentMobile();
  const {styles} = useMSBStyles(makeStyle);
  const errorTitle = useMemo(() => {
    return sourceAccDefault?.availableBalance !== undefined && sourceAccDefault?.availableBalance !== null
      ? +sourceAccDefault?.availableBalance > amount
        ? ''
        : 'Số dư tài khoản không đủ '
      : '';
  }, [sourceAccDefault, amount]);

  const [promoCode, setPromoCode] = useState('');

  const formatMoney = (value: number) => {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleContinue = () => {
    // Navigate to confirmation screen or process payment
    // For now, just go back to demonstrateprov
    handleBillValidate(amount, category, provider);
  };

  return (
    <>
      <KeyboardAwareScrollView style={styles.container}>
        {/* Bill Information */}
        <View style={styles.billInfoContainer}>
          <View style={styles.billHeaderContainer}>
            <MSBFastImage
              nameImage={provider?.id?.toString() || ''}
              style={styles.providerLogo}
              folder={provider?.isTopup() ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
            />
            <View style={styles.billHeader}>
              <MSBTextBase style={styles.billHeaderText} content={customerName?.toUpperCase()} />
              <View style={styles.providerInfo}>
                <MSBTextBase style={styles.providerText} content={provider?.subgroupNameVn} />
                <MSBTextBase style={styles.dotSeparator} content=" • " />
                <MSBTextBase style={styles.phoneText} content={phoneNumber} />
              </View>
            </View>
          </View>

          {!provider?.isViettelBill() && (
            <View style={styles.billAmount}>
              <MSBTextBase
                style={styles.billLabel}
                content={
                  paymentBill?.billList?.[0]?.period ? `Hóa đơn ${paymentBill?.billList?.[0]?.period}` : 'Tổng tiền'
                }
              />
              <View style={styles.amountContainer}>
                <MSBTextBase style={styles.amountText} content={`${formatMoney(amount)}`}>
                  <MSBTextBase style={styles.amountCurrencyText} content={' VND'} />
                </MSBTextBase>
              </View>
            </View>
          )}
        </View>

        {/* Account Source */}
        <View style={styles.accountContainer}>
          <SourceAccount
            title={translate('paymentInfor.sourceAccount')}
            onSelectAccount={onSelectAccount}
            errorTitle={errorTitle}
          />

          {/* Promo Code */}
          {/* <View style={styles.promoContainer}>
            <MSBTextBase style={styles.sectionTitle} content="Mã ưu đãi (Nếu có)" />
            <View style={styles.promoInputContainer}>
              <TextInput
                style={styles.promoInput}
                value={promoCode}
                onChangeText={setPromoCode}
                placeholder="Chọn hoặc nhập mã ưu đãi"
                placeholderTextColor={ColorGlobal.Neutral400}
              />
              <View style={styles.giftIcon}>
                <MSBIcon icon={MSBIcons.IconGift} iconColor={ColorGlobal.Brand500} />
              </View>
            </View>
          </View> */}
        </View>
      </KeyboardAwareScrollView>
      {/* Continue Button */}
      <View style={[styles.buttonContainer]}>
        <MSBButton
          testID="prepaid.mobileInfo.pressToContinue"
          buttonType={ButtonType.Primary}
          label={translate('paymentBill.btnContinue')}
          onPress={handleContinue}
          style={styles.bottomSpace}
          isLoading={isLoadingValidate}
        />
      </View>
    </>
  );
};

export const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => {
  return {
    container: {
      flex: 1,
    },
    billInfoContainer: {
      margin: SizeGlobal.Size400,
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      paddingHorizontal: SizeGlobal.Size400,
      paddingVertical: SizeGlobal.Size300,
    },
    billHeaderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingBottom: SizeGlobal.Size100,
    },
    billHeader: {
      flexDirection: 'column',
      gap: SizeGlobal.Size100,
    },
    billHeaderText: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Neutral800,
    },
    providerInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    providerLogo: {
      width: SizeGlobal.Size800,
      height: SizeGlobal.Size800,
      marginRight: SizeGlobal.Size300,
    },
    providerText: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
    },
    dotSeparator: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral400,
    },
    phoneText: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
    },
    billAmount: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderTopWidth: 1,
      borderTopColor: ColorGlobal.Neutral100,
      paddingTop: SizeGlobal.Size300,
    },
    billLabel: {
      ...Typography?.small_regular,
      color: ColorGlobal.Neutral600,
    },
    amountContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    amountText: {
      ...Typography?.small_semiBold,
      color: ColorGlobal.Neutral800,
    },
    amountCurrencyText: {
      ...Typography?.small_regular,
      color: ColorGlobal.Neutral600,
    },
    accountContainer: {
      margin: SizeGlobal.Size400,
      marginTop: 0,
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      padding: SizeGlobal.Size400,
    },
    sectionTitle: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Neutral800,
      marginBottom: SizeGlobal.Size300,
    },
    accountInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: SizeGlobal.Size200,
      borderWidth: 1,
      borderColor: ColorGlobal.Neutral100,
      borderRadius: SizeGlobal.Size200,
      paddingHorizontal: SizeGlobal.Size400,
    },
    accountDetails: {
      flex: 1,
    },
    accountNumber: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral600,
      marginBottom: SizeGlobal.Size100,
    },
    accountBalance: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Neutral800,
    },
    promoContainer: {
      marginTop: SizeGlobal.Size500,
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
    },
    promoInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: ColorGlobal.Neutral100,
      borderRadius: SizeGlobal.Size200,
      paddingHorizontal: SizeGlobal.Size400,
    },
    promoInput: {
      flex: 1,
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
      paddingVertical: SizeGlobal.Size300,
    },
    giftIcon: {
      padding: SizeGlobal.Size100,
    },
    bottomSpace: {
      marginHorizontal: SizeGlobal.Size400,
    },
    buttonContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
    },
  };
});

export default PostPaidMobileInfoScreen;
