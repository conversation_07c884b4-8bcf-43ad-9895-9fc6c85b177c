import React, {useMemo, useState} from 'react';
import {View} from 'react-native';
import {
  MSBTextBase,
  ButtonType,
  MSBButton,
  MSBChip,
  MSBTouchable,
  MSBChipState,
  MSBChipStyle,
  createMSBStyleSheet,
  useMSBStyles,
  MAX_WIDTH,
  MSBFastImage,
  MSBFolderImage,
} from 'msb-shared-component';
import {translate} from '../../locales/i18n';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {usePaymentMobile} from './hook';
import Utils from '../../utils/Utils';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {Federated} from '@callstack/repack/client';

/**
 * Khi ở build time sẽ sử dụng MFv1 nên là không thể sử dụng dynamic import của MFv2
 * => Sử dụng {@link Federated.importModule} để bundle
 *
 * Lưu ý!: không tách biệt điều kiện process.env.NODE_ENV === 'production' ra hàm riêng biệt vì lúc build sẽ làm cho dynamic import() được bundle vào
 * => gây ra lỗi lúc build
 *
 * Không thể sử dụng được dynamic import() từ hàm
 * => !Nên mỗi khi load module thì phải định nghĩa lại như dưới
 */
const SourceAccount = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('TransferModule', './SourceAccount')
    : import('TransferModule/SourceAccount'),
);
const PrepaidMobileInfoScreen = ({
  phoneNumber,
  provider,
  category,
}: {
  phoneNumber: string;
  provider?: ProviderModel;
  category: CategoryModel;
}) => {
  const [selectedAmount, setSelectedAmount] = useState(100000);
  const {sourceAccDefault, paymentBill, handleBillValidate, isLoadingValidate, onSelectAccount} = usePaymentMobile();
  const {styles} = useMSBStyles(makeStyle);

  const errorTitle = useMemo(() => {
    return sourceAccDefault?.availableBalance !== undefined && sourceAccDefault?.availableBalance !== null
      ? +sourceAccDefault?.availableBalance > selectedAmount
        ? ''
        : 'Số dư tài khoản không đủ '
      : '';
  }, [sourceAccDefault, selectedAmount]);

  const formatMoney = (value: number) => {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleContinue = () => {
    // Navigate to confirmation screen or process payment
    handleBillValidate(selectedAmount, category, provider);
  };

  const amountOptions = useMemo(() => {
    return paymentBill?.billList?.map(bill => bill.amount) || [];
  }, [paymentBill]);

  return (
    <>
      <KeyboardAwareScrollView style={styles.container}>
        {/* Bill Information */}
        <View style={styles.billInfoContainer}>
          {/* <MSBFastImage source={Utils.getProviderIcon(provider?.id?.toString() || '')} style={styles.providerLogo} /> */}
          <MSBFastImage
            nameImage={provider?.id?.toString()}
            style={styles.providerLogo}
            folder={provider?.isTopup() ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
          />
          <View style={styles.billInfo}>
            <View style={styles.billHeader}>
              <MSBTextBase style={styles.billHeaderText} content={translate('paymentBill.phonePrepaid')} />
            </View>
            <View style={styles.providerInfo}>
              <MSBTextBase style={styles.providerText} content={provider?.subgroupNameVn} />
              <MSBTextBase style={styles.dotSeparator} content=" • " />
              <MSBTextBase style={styles.phoneText} content={phoneNumber} />
            </View>
          </View>
        </View>

        {/* Amount Selection */}
        <View style={styles.amountSelectionContainer}>
          {/* Account Source */}
          <View style={styles.accountContainer}>
            <SourceAccount
              title={translate('paymentInfor.sourceAccount')}
              onSelectAccount={onSelectAccount}
              errorTitle={errorTitle}
            />
          </View>
          <MSBTextBase style={styles.sectionTitle} content={translate('common.selectAmount')} />
          <View style={styles.amountGrid}>
            {amountOptions.map(amountOption => (
              <MSBTouchable style={styles.amountOption} onPress={() => amountOption && setSelectedAmount(amountOption)}>
                <MSBChip
                  chipStyle={MSBChipStyle.Large}
                  chipState={selectedAmount === amountOption ? MSBChipState.Active : MSBChipState.Default}
                  style={styles.priceOption}
                  key={amountOption}
                  title={`${amountOption && formatMoney(amountOption)}`}
                />
              </MSBTouchable>
            ))}
          </View>
          {/* Promo Code */}
          {/* <View>
            <MSBTextBase style={styles.sectionTitle} content="Mã ưu đãi (Nếu có)" />
            <View style={styles.promoInputContainer}>
              <TextInput
                style={styles.promoInput}
                value={promoCode}
                onChangeText={setPromoCode}
                placeholder="Chọn hoặc nhập mã ưu đãi"
                placeholderTextColor={styles.placeholderTextColor.color}
              />
              <View style={styles.giftIcon}>
                <MSBIcon icon={MSBIcons.IconGift} />
              </View>
            </View>
          </View> */}
        </View>
        <View style={styles.spacer} />
      </KeyboardAwareScrollView>
      {/* Continue Button */}
      <View style={[styles.buttonContainer]}>
        <MSBButton
          testID="prepaid.mobileInfo.pressToContinue"
          buttonType={ButtonType.Primary}
          label={translate('paymentBill.btnContinue')}
          onPress={handleContinue}
          style={styles.bottomSpace}
          isLoading={isLoadingValidate}
          disabled={errorTitle !== ''}
        />
      </View>
    </>
  );
};

export const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => {
  return {
    container: {
      flex: 1,
    },
    billInfoContainer: {
      margin: SizeGlobal.Size400,
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SizeGlobal.Size400,
      paddingVertical: SizeGlobal.Size300,
      shadowColor: ColorGlobal.Neutral800,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 5,
    },
    billHeader: {},
    billInfo: {
      flexDirection: 'column',
      alignItems: 'flex-start',
      gap: SizeGlobal.Size100,
    },
    billHeaderText: {
      ...Typography?.base_medium,
      color: ColorGlobal.Neutral800,
    },
    providerInfo: {
      flexDirection: 'row',
    },
    providerLogo: {
      width: SizeGlobal.Size800,
      height: SizeGlobal.Size800,
      marginRight: SizeGlobal.Size300,
    },
    providerText: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
    },
    dotSeparator: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral400,
    },
    phoneText: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
    },
    accountContainer: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      marginBottom: SizeGlobal.Size500,
    },
    sectionTitle: {
      ...Typography?.small_medium,
      color: ColorGlobal.Neutral800,
      marginBottom: SizeGlobal.Size200,
    },
    accountInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: SizeGlobal.Size200,
      borderWidth: 1,
      borderColor: ColorGlobal.Neutral100,
      borderRadius: SizeGlobal.Size200,
      paddingHorizontal: SizeGlobal.Size400,
    },
    accountDetails: {
      flex: 1,
    },
    placeholderTextColor: {
      color: ColorGlobal.Neutral400,
    },
    accountNumber: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral600,
      marginBottom: SizeGlobal.Size100,
    },
    accountBalance: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Neutral800,
    },
    amountSelectionContainer: {
      margin: SizeGlobal.Size400,
      marginTop: 0,
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      padding: SizeGlobal.Size400,
      shadowColor: ColorGlobal.Neutral800,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 5,
    },
    amountGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: SizeGlobal.Size500,
      justifyContent: 'space-between',
    },
    amountOption: {
      borderColor: ColorGlobal.Neutral200,
      borderRadius: SizeGlobal.Size300,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: SizeGlobal.Size200,
      marginBottom: SizeGlobal.Size300,
    },
    priceOption: {
      minWidth: MAX_WIDTH / 3 - SizeGlobal.Size800,
      textAlign: 'center',
      alignItems: 'center',
    },
    selectedAmountOption: {
      borderColor: ColorGlobal.Brand500,
      backgroundColor: ColorGlobal.Brand50,
    },
    amountOptionText: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
    },
    selectedAmountOptionText: {
      color: ColorGlobal.Brand500,
      ...Typography?.base_semiBold,
    },
    promoContainer: {
      margin: SizeGlobal.Size400,
      marginTop: 0,
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      padding: SizeGlobal.Size400,
    },
    promoInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: ColorGlobal.Neutral100,
      borderRadius: SizeGlobal.Size200,
      paddingHorizontal: SizeGlobal.Size400,
    },
    promoInput: {
      flex: 1,
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
      paddingVertical: SizeGlobal.Size300,
    },
    giftIcon: {
      padding: SizeGlobal.Size100,
    },
    bottomSpace: {
      marginHorizontal: SizeGlobal.Size400,
    },
    spacer: {
      height: SizeGlobal.Size2400,
    },
    buttonContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
    },
  };
});

export default PrepaidMobileInfoScreen;
