import {
  ButtonType,
  createMSBStyleSheet,
  MSBButton,
  MSBFolderImage,
  MSBIcon,
  MSBIconSize,
  useMSBStyles,
} from 'msb-shared-component';
import {useState} from 'react';

import React, {useRef, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import TransferAccountNumberInput from '../../components/transfer-account-number-input';
import MSBProviderSelection from '../../components/provider-selection';
import {translate} from '../../locales/i18n';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import Animated, {useSharedValue, withTiming, useAnimatedStyle, Easing} from 'react-native-reanimated';
import {useEffect} from 'react';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {usePaymentMobile} from './hook';
import PostPaidMobileInfoScreen from './PostPaidMobileInfo';
import {hostSharedModule} from 'msb-host-shared-module';
import {selectContactPhone} from 'react-native-select-contact';

export const PostpaidMobile = ({category}: {category: CategoryModel}) => {
  const {getPaymentBill, paymentBill} = usePaymentMobile();
  const providerRef = useRef(null);
  const [phone, setPhone] = useState('');
  const [errorPhone, setErrorPhone] = useState('');
  const [provider, setProvider] = useState<ProviderModel>();
  const [statusStep, setStatusStep] = useState<'INIT' | 'CONFIRM'>('INIT');
  const formOpacity = useSharedValue(1);
  const infoOpacity = useSharedValue(0);
  const {styles} = useMSBStyles(makeStyle);

  const isDisableButton = useMemo(() => {
    return phone.length === 0 || provider?.serviceCode === undefined || errorPhone.length > 0;
  }, [phone, provider, errorPhone]);

  useEffect(() => {
    if (statusStep === 'CONFIRM') {
      formOpacity.value = withTiming(0, {duration: 300, easing: Easing.linear});
      infoOpacity.value = withTiming(1, {duration: 300, easing: Easing.linear});
    } else {
      formOpacity.value = withTiming(1, {duration: 300, easing: Easing.linear});
      infoOpacity.value = withTiming(0, {duration: 300, easing: Easing.linear});
    }
  }, [statusStep, formOpacity, infoOpacity]);

  const formAnimatedStyle = useAnimatedStyle(() => ({
    opacity: formOpacity.value,
    pointerEvents: formOpacity.value > 0.5 ? 'auto' : 'none',
  }));

  const infoAnimatedStyle = useAnimatedStyle(() => ({
    opacity: infoOpacity.value,
    pointerEvents: infoOpacity.value > 0.5 ? 'auto' : 'none',
  }));

  const validatePhone = (num: string) => {
    // Vietnamese phone number: 10 digits, starts with 03, 05, 07, 08, or 09
    const vietnamPhoneRegex = /^(03|05|07|08|09)[0-9]{8}$/;
    if (!vietnamPhoneRegex.test(num.replace('+84', '0'))) {
      setErrorPhone(translate('error.validation.errorPhone'));
    } else {
      setErrorPhone('');
    }
  };

  const getPhoneNumber = () => {
    return selectContactPhone()
      .then(select => {
        if (!select) {
          return null;
        }
        const {contact, selectedPhone} = select;
        const phoneNum = selectedPhone?.number;
        const phoneStr = phoneNum?.split(' ')?.join('');

        setPhone(phoneStr);
        console.log(`Selected ${selectedPhone.type} phone number ${selectedPhone.number} from ${contact.name}`);
        validatePhone(phoneStr);
        return phoneStr;
      })
      .catch(e => {
        console.log('====================================');
        console.log(e);
        console.log('====================================');
      });
  };

  const handleContinue = () => {
    getPaymentBill({
      billCode: phone,
      serviceCode: provider?.serviceCode || '',
      accountingType: 'ACCT',
    }).then(result => {
      console.log('paymentBill', result);
      if (result?.result === 'OK') {
        if (result.service?.code === '810001') {
          // Scenario 2: Màn hình truy vấn thành công thông tin thuê bao trả sau là nhà mạng Viettel
          hostSharedModule.d.domainService.showPopup({
            iconType: 'WARNING',
            title: translate('error.phoneViettelDebt'),
            content:
              'Do quy định của NCCDV Viettel về việc không hiển thị thông tin cước nợ trả sau. Quý khách vui lòng nhấn “Tiếp tục” để thanh toán toàn bộ cước nợ.',
            confirmBtnText: translate('paymentBill.btnContinue'),
            onConfirm() {
              setStatusStep('CONFIRM');
            },
          });
          return;
        }
        setStatusStep('CONFIRM');
      }
    });
  };

  return (
    <View style={styles.container}>
      <Animated.View style={[StyleSheet.absoluteFill, formAnimatedStyle]}>
        <View style={styles.formContainer}>
          <TransferAccountNumberInput
            placeholder={translate('editContact.enter_account_number')}
            value={phone}
            onChangeText={(text: string) => setPhone(text)}
            containerStyle={styles.input}
            childrenIconRight={
              <MSBIcon
                folderIcon={MSBFolderImage.ICON_SVG}
                icon={'tone-bill'}
                iconSize={MSBIconSize.SIZE_24}
                onIconClick={getPhoneNumber}
                // styleContainer={styles.iconContact}
              />
            }
            label={translate('paymentBill.numberPhone')}
            errorContent={errorPhone}
            onBlur={() => validatePhone(phone)}
          />
          <MSBProviderSelection
            disabled={errorPhone.length > 0}
            ref={providerRef}
            code={category?.id}
            onSelected={setProvider}
          />
        </View>
        <View style={[styles.buttonContainer]}>
          <MSBButton
            buttonType={ButtonType.Primary}
            label={translate('paymentBill.btnContinue')}
            onPress={handleContinue}
            style={styles.button}
            disabled={isDisableButton}
          />
        </View>
      </Animated.View>

      <Animated.View style={[StyleSheet.absoluteFill, infoAnimatedStyle]}>
        <PostPaidMobileInfoScreen
          customerName={paymentBill?.billList?.[0].custName}
          phoneNumber={phone}
          provider={provider}
          amount={paymentBill?.billList?.[0].amount ?? 0}
          category={category}
        />
      </Animated.View>
    </View>
  );
};

const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => {
  return {
    container: {
      flex: 1,
    },
    formContainer: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      margin: SizeGlobal.Size400,
      padding: SizeGlobal.Size400,
      ...Typography?.base_regular,
      shadowColor: ColorGlobal.NeutralWhite,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 5,
    },
    label: {
      ...Typography?.base_medium,
      marginBottom: SizeGlobal.Size200,
      color: ColorGlobal.Neutral100,
    },
    input: {
      marginBottom: SizeGlobal.Size400,
    },
    button: {
      marginTop: SizeGlobal.Size800,
    },
    buttonContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      paddingHorizontal: SizeGlobal.Size400,
    },
  };
});
