import React, {PropsWithChildren, createContext, useCallback, useContext, useEffect, useState} from 'react';
import {DIContainer} from '../../di/DIContainer';
import {showErrorPopup} from '../../utils/PopupUtils';
import {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';
import {hostSharedModule} from 'msb-host-shared-module';
import {translate} from '../../locales/i18n';
import {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';
import {BillValidateRequest} from '../../data/models/bill-validate/BillValidateRequest';
import {PAYMENT_TYPE} from '../../commons/Constants';
import ScreenNames from '../../commons/ScreenNames';
import {Configs} from '../../commons/Configs';
import {useNavigation, NavigationProp, useRoute} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {RouteProp} from '@react-navigation/native';
import {PaymentInfoModel} from '../../navigation/types';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import DimensionUtils from '../../utils/DimensionUtils';
import SourceAccountList from '../../components/source-account-list';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import PaymentInfoUtils from '../payment-info/utils/Utils';
import BiometricAuthentication from '../payment-info/components/biometric-authentication';

// Create context with default values
interface PaymentMobileContextType {
  sourceAcc: SourceAccountModel[];
  sourceAccDefault?: SourceAccountModel;
  isLoadingSourceAccount: boolean;
  paymentBill?: GetBillDetailModel;
  errorContent?: string;
  isLoadingBill: boolean;
  isLoadingValidate: boolean;
  totalAmount?: number;
  getSourceAccountList: () => Promise<void>;
  getPaymentBill: (request: GetBillDetailRequest) => Promise<GetBillDetailModel | undefined>;
  handleBillValidate: (
    totalAmount: number,
    category: CategoryModel,
    provider?: ProviderModel,
  ) => Promise<{params: BillValidateRequest; id: string} | undefined>;
  openSelectAccount: (
    renderSourceAccountList: (
      accountList: SourceAccountModel[],
      onSelectAccount: (account?: SourceAccountModel) => void,
    ) => React.ReactNode,
  ) => void;
  onSelectAccount: (selectedAccount?: SourceAccountModel) => void;
  renderSourceAccountList: (
    accountList: SourceAccountModel[],
    onSelectAccount: (account?: SourceAccountModel) => void,
  ) => React.ReactNode;
}

const PaymentMobileContext = createContext<PaymentMobileContextType | undefined>(undefined);

// Custom hook for using the context
export const usePaymentMobile = () => {
  const context = useContext(PaymentMobileContext);
  if (!context) {
    throw new Error('usePaymentMobile must be used within a PaymentMobileProvider');
  }
  return context;
};

// Provider component
export const PaymentMobileProvider = ({children}: PropsWithChildren) => {
  // State management
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentPhoneScreen'>>();
  const navigation = useNavigation<NavigationProp<PaymentStackParamList, 'PaymentPhoneScreen'>>();

  const {category} = route.params || {};
  const [sourceAcc, setSourceAcc] = useState<SourceAccountModel[]>([]);
  const [sourceAccDefault, setSourceAccDefault] = useState<SourceAccountModel>();
  const [isLoadingSourceAccount, setLoadingSourceAccount] = useState<boolean>(false);
  const [paymentBill, setPaymentBill] = useState<GetBillDetailModel | undefined>();
  const [errorContent, setErrorContent] = useState<string | undefined>();
  const [isLoadingBill, setIsLoadingBill] = useState<boolean>(false);
  const [isLoadingValidate, setIsLoadingValidate] = useState<boolean>(false);

  // Fetch source accounts
  const getSourceAccountList = useCallback(async () => {
    setLoadingSourceAccount(true);
    const result = await DIContainer.getInstance().getSourceAccountListUseCase().execute();
    setLoadingSourceAccount(false);

    if (result.status === 'ERROR') {
      showErrorPopup(result.error);
      return;
    }

    // Filter visible accounts
    const sourceAccount: SourceAccountModel[] = (result?.data?.data ?? []).filter(
      item => item?.userPreferences?.visible !== false,
    );

    // Find default account
    const sourceAccountDefault = sourceAccount?.find(arrangement => arrangement?.isDefault === 'Y');

    setSourceAcc(sourceAccount);

    if (sourceAccountDefault) {
      setSourceAccDefault(sourceAccountDefault);
    } else {
      setSourceAccDefault(sourceAccount[0]);
    }
  }, []);

  const renderBiometricAuthentication = useCallback(() => {
    return <BiometricAuthentication content={translate('paymentInfor.biometricAuthentication')} />;
  }, []);

  const renderIdentification = useCallback(() => {
    return <BiometricAuthentication content={translate('paymentInfor.identification')} />;
  }, []);

  // Get payment bill details
  const getPaymentBill = useCallback(async (request: GetBillDetailRequest) => {
    setIsLoadingBill(true);
    setErrorContent(undefined);

    try {
      const result = await DIContainer.getInstance().getGetBillDetailUseCase().execute(request);

      if (result.status === 'ERROR') {
        setErrorContent('Không tìm thấy thông tin hoá đơn');
        showErrorPopup(result.error);
        return undefined;
      }

      if (!result.data?.billCode) {
        hostSharedModule.d.domainService.showPopup({
          iconType: 'WARNING',
          title: translate('error.oops'),
          content: translate('error.errorOccurred'),
          confirmBtnText: translate('paymentConfirm.close'),
        });
        setPaymentBill(undefined);
        return undefined;
      }

      setPaymentBill(result.data);
      return result.data;
    } catch (error) {
      setErrorContent('Đã xảy ra lỗi khi lấy thông tin hoá đơn');
      setPaymentBill(undefined);
      return undefined;
    } finally {
      setIsLoadingBill(false);
    }
  }, []);

  const goPaymentConfirm = useCallback(
    (billValidateInfo?: BillValidateRequest, id?: string, cate?: CategoryModel, provider?: ProviderModel) => {
      console.log('billValidateInfo', billValidateInfo, cate);
      const p = paymentBill;
      const contractName = paymentBill?.billList?.[0]?.custName;
      const title = category?.categoryName + ' ' + (cate?.categoryName ?? '').toLocaleLowerCase();
      p?.setBillList(
        p?.billList?.map(e => {
          if (cate?.id === 'MR') {
            return {...e, custName: translate('paymentBill.phonePrepaid')};
          }
          if (cate?.id === 'MB') {
            return {...e, custName: e.custName};
          }
          return {...e, custName: provider?.partnerName};
        }),
      );
      provider?.setPartnerName(provider?.subgroupNameVn ?? '');
      const paymentInfo: PaymentInfoModel = {
        title,
        categoryName: provider?.getName() ?? '',
        billInfo: p,
        contractName,
        provider: provider,
      };

      navigation.navigate(ScreenNames.PaymentConfirmScreen, {
        paymentInfo: {
          ...paymentInfo,
          paymentValidate: {...billValidateInfo, id: id ?? ''},
          originatorAccount: {
            identification: sourceAccDefault?.id ?? '',
            name: sourceAccDefault?.name ?? '',
            accountNo: sourceAccDefault?.BBAN ?? '',
            bankName: 'MSB',
            bankCode: Configs.MSB_BANKID,
          },
        },
        hasPeriod: false,
      });
    },
    [navigation, paymentBill, sourceAccDefault, category],
  );

  // Validate bill payment
  const handleBillValidate = useCallback(
    async (totalAmount: number, cate: CategoryModel, provider?: ProviderModel) => {
      setIsLoadingValidate(true);
      try {
        const requestedExecutionDate: string = new Date().toISOString();
        const summary = {
          totalAmount: totalAmount,
          debitAmount: totalAmount,
          billQuantity: 1,
          cashbackAmount: 0,
          discountAmount: 0,
        };

        let paymentType = '';

        if (cate?.id === 'MR') {
          paymentType = PAYMENT_TYPE.TOPUP_ACCOUNT;
          if (paymentBill?.paymentRule === 1) {
            paymentType = PAYMENT_TYPE.TOPUP_ACCOUNT;
          } else if (paymentBill?.paymentRule === 2) {
            paymentType = PAYMENT_TYPE.TOPUP_CREDIT;
          }
        } else if (cate?.id === 'MB') {
          paymentType = PAYMENT_TYPE.BILLING_ACCOUNT;
          if (paymentBill?.paymentRule === 1) {
            paymentType = PAYMENT_TYPE.BILLING_ACCOUNT;
          } else if (paymentBill?.paymentRule === 2) {
            paymentType = PAYMENT_TYPE.BILLING_CREDIT;
          }
        }

        const params: BillValidateRequest = {
          originatorAccount: {
            identification: {
              identification: sourceAccDefault?.id ?? '',
              schemeName: 'ID',
            },
          },
          requestedExecutionDate,
          paymentType,
          transferTransactionInformation: {
            instructedAmount: {
              amount: totalAmount?.toString() ?? '',
              currencyCode: 'VND',
            },
            counterparty: {
              name: paymentBill?.customerInfo?.name ?? '',
            },
            counterpartyAccount: {
              identification: {
                identification: paymentBill?.billCode ?? '',
                schemeName: 'IBAN',
              },
            },
            additions: {
              bpQueryRef: paymentBill?.queryRef ?? '',
              bpBillList: JSON.stringify(paymentBill?.billList?.filter(e => e.amount === totalAmount)),
              bpSummary: JSON.stringify(summary),
              bpServiceCode: paymentBill?.service?.code ?? '',
              cifNo: paymentBill?.customerInfo?.cif ?? '',
              bpCategory: cate?.id,
              // bpAccountingNumber: sourceAccDefault?.BBAN ?? '',
            },
          },
        };

        const result = await DIContainer.getInstance().getBillValidateUseCase().execute(params);

        if (result.status === 'SUCCESS') {
          goPaymentConfirm(params, result.data?.id ?? '', cate, provider);
        } else if (result.status === 'ERROR') {
          const errorKey = result?.error?.code;
          switch (errorKey) {
            case 'FTES0001': // Gói truy vấn
              PaymentInfoUtils.checkIBMB();
              break;
            case 'FTES0008': // Sinh trắc học
              if (renderBiometricAuthentication) {
                PaymentInfoUtils.checkBiometricAuthentication(renderBiometricAuthentication());
              }
              break;
            case 'FTES0009': // Giấy tờ tuỳ thân
              if (renderIdentification) {
                PaymentInfoUtils.checkIdentification(renderIdentification());
              }
              break;
            case 'BMS0017': // Tài khoản thụ hưởng không hợp lệ
              PaymentInfoUtils.userNotValid(() => navigation.goBack());
              break;
            default:
              PaymentInfoUtils.checkErrorSystem('', () => navigation.goBack(), errorKey);
              break;
          }
        }

        return undefined;
      } catch (error) {
        console.error('Bill validation error:', error);
        return undefined;
      } finally {
        setIsLoadingValidate(false);
      }
    },
    [
      sourceAccDefault?.id,
      paymentBill?.customerInfo?.name,
      paymentBill?.customerInfo?.cif,
      paymentBill?.billCode,
      paymentBill?.queryRef,
      paymentBill?.billList,
      paymentBill?.service?.code,
      paymentBill?.paymentRule,
      goPaymentConfirm,
      renderBiometricAuthentication,
      renderIdentification,
      navigation,
    ],
  );

  const _renderSourceAccountList = (
    accountList: SourceAccountModel[],
    onSelectAccount: (account?: SourceAccountModel) => void,
  ) => {
    return (
      <BottomSheetView
        style={{
          height: (DimensionUtils.getWindowHeight() * 80) / 100,
          paddingBottom: DimensionUtils.getPaddingBottomByDevice(),
        }}>
        <SourceAccountList accSelected={sourceAccDefault} accountList={accountList!} onSelectAcount={onSelectAccount} />
      </BottomSheetView>
    );
  };
  // Handle account selection
  const onSelectAccount = useCallback((selectedAccount?: SourceAccountModel) => {
    hostSharedModule.d.domainService?.hideBottomSheet();
    if (selectedAccount) {
      setSourceAccDefault(selectedAccount);
    }
  }, []);

  // Show bottom sheet to select source account
  const openSelectAccount = useCallback(
    (
      renderSourceAccountList: (
        accountList: SourceAccountModel[],
        onSelectAccount: (account?: SourceAccountModel) => void,
      ) => React.ReactNode,
    ) => {
      hostSharedModule.d.domainService?.showBottomSheet({
        header: translate('paymentInfor.sourceAccount'),
        children: renderSourceAccountList(sourceAcc, onSelectAccount),
      });
    },
    [sourceAcc, onSelectAccount],
  );

  // Load source accounts on component mount
  useEffect(() => {
    getSourceAccountList();
  }, [getSourceAccountList]);

  // Value to be provided to consumers
  const contextValue: PaymentMobileContextType = {
    sourceAcc,
    sourceAccDefault,
    isLoadingSourceAccount,
    paymentBill,
    errorContent,
    isLoadingBill,
    isLoadingValidate,
    getSourceAccountList,
    getPaymentBill,
    handleBillValidate,
    openSelectAccount,
    onSelectAccount,
    renderSourceAccountList: _renderSourceAccountList,
  };

  return <PaymentMobileContext.Provider value={contextValue}>{children}</PaymentMobileContext.Provider>;
};

export type UsePaymentMobileProps = ReturnType<typeof usePaymentMobile>;
