import React, {useState} from 'react';
import {View, useWindowDimensions} from 'react-native';
import {TabB<PERSON>, TabView} from 'react-native-tab-view';
import {
  MSBTextBase,
  MAX_HEIGHT,
  MAX_WIDTH,
  MSBTouchable,
  createMSBStyleSheet,
  useMSBStyles,
  MSBPage,
} from 'msb-shared-component';
import {PrepaidMobile} from './PrepaidMobile';
import {PostpaidMobile} from './PostpaidMobile';
import DimensionUtils from '../../utils/DimensionUtils';
import {RouteProp} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {useRoute} from '@react-navigation/native';
import {CategoryModel} from '../../domain/entities/category-list/CategoryListModel';
import {PaymentMobileProvider} from './hook';
import {translate} from '../../locales/i18n';
type RouteType = {key: string; title: string; categoryCode: string};

const PaymentPhoneScreen = () => {
  const {styles} = useMSBStyles(makeStyle);
  const layout = useWindowDimensions();
  const [index, setIndex] = useState(0);
  const [routes] = useState<RouteType[]>([
    {key: 'prepaid', title: translate('paymentBill.prepaid'), categoryCode: 'MR'},
    {key: 'postpaid', title: translate('paymentBill.postpaid'), categoryCode: 'MB'},
  ]);
  const route = useRoute<RouteProp<PaymentStackParamList, 'PaymentPhoneScreen'>>();
  const {category} = route.params || {};

  const renderScene = ({route: routeTab}: {route: RouteType}) => {
    const _category = category.children.find(c => c.id === routeTab.categoryCode);
    console.log('renderScene-----------=-====-=-=-=-=-=-=-=-=-<<<<', category, routeTab, _category);
    switch (routeTab.key) {
      case 'prepaid':
        return <PrepaidMobile category={_category as CategoryModel} />;
      case 'postpaid':
        return <PostpaidMobile category={_category as CategoryModel} />;
      default:
        return <View />;
    }
  };

  return (
    <PaymentMobileProvider>
      <MSBPage
        isScrollable={false}
        headerProps={{
          hasBack: true,
          barStyle: true,
          title: `${translate('paymentBill.title')} ${category?.description.toLowerCase()}`,
        }}
        style={styles.container}>
        <TabView
          onIndexChange={setIndex}
          navigationState={{index, routes}}
          renderScene={renderScene}
          initialLayout={{width: layout.width}}
          swipeEnabled={true}
          renderTabBar={props => (
            <TabBar
              {...props}
              style={styles.tabBar}
              indicatorStyle={styles.indicator}
              contentContainerStyle={styles.tabContainer}
              renderTabBarItem={({route: routeTab, onPress}) => {
                const focused = index === props.navigationState.routes.findIndex(r => r.key === routeTab.key);
                return (
                  <MSBTouchable onPress={onPress} style={styles.tabItemWrapper}>
                    <MSBTextBase style={[styles.tabText, focused && styles.tabTextActive]} content={routeTab.title} />
                    {focused && <View style={{...styles.tabIndicator}} />}
                  </MSBTouchable>
                );
              }}
            />
          )}
        />
      </MSBPage>
    </PaymentMobileProvider>
  );
};

const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => {
  return {
    container: {
      flex: 1,
    },
    ctnImage: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: MAX_WIDTH,
      height: MAX_HEIGHT,
    },
    imageBg: {
      width: '100%',
      height: '100%',
    },
    bottomSpace: {
      marginBottom: DimensionUtils.getPaddingBottomByDevice(),
      marginHorizontal: SizeGlobal.Size400,
    },
    header: {
      backgroundColor: ColorGlobal.NeutralWhite,
    },
    headerTitle: {
      ...Typography?.h3_semiBold,
      flex: 1,
      textAlign: 'left',
    },
    tabBar: {
      backgroundColor: ColorGlobal.NeutralWhite,
      elevation: 2,
      shadowColor: ColorGlobal.Neutral300,
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: 0.2,
      shadowRadius: 2,
      borderBottomWidth: 1,
      borderBottomColor: ColorGlobal.Neutral100,
    },
    tabContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      flex: 1,
    },
    tabItemWrapper: {
      alignItems: 'center',
      width: MAX_WIDTH / 2,
      paddingVertical: SizeGlobal.Size300,
    },
    tabText: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral800,
      textAlign: 'center',
    },
    tabTextActive: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Brand500,
    },
    tabIndicator: {
      backgroundColor: ColorGlobal.Brand500,
      height: SizeGlobal.Size50,
      width: '100%',
      borderRadius: SizeGlobal.Size50,
      position: 'absolute',
      bottom: 0,
    },
    indicator: {
      height: 0,
      backgroundColor: 'white',
    },
    buttonContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      bottom: SizeGlobal.Size400,
      alignItems: 'center',
    },
    disabledButton: {
      backgroundColor: ColorGlobal.Neutral300,
      borderRadius: SizeGlobal.Size200,
      paddingVertical: SizeGlobal.Size400,
      paddingHorizontal: SizeGlobal.Size800,
      alignItems: 'center',
      width: '90%',
    },
    buttonText: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Neutral500,
      textAlign: 'center',
    },
  };
});

export default PaymentPhoneScreen;
