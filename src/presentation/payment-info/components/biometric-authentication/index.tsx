import React from 'react';
import {View} from 'react-native';
import {createMSBStyleSheet, MSBTouchable, useMSBStyles, MSBTextBase, MSBFastImage} from 'msb-shared-component';

import {BiometricAuthenticationProps} from './types';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {translate} from '../../../../locales/i18n';
import Images from '../../../../assets/images/Images';
import {hostSharedModule} from 'msb-host-shared-module';

//TODO: Update CDN Image
const BiometricAuthentication = ({style, content}: BiometricAuthenticationProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={[styles.container, style]}>
      <BottomSheetScrollView contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <MSBFastImage style={styles.icPhoneWarning} resizeMode="cover" source={Images.imgBiometricWarning} />
          <MSBTextBase style={styles.txtIDInvalid}>{content}</MSBTextBase>
          <View style={styles.benefitContainer}>
            <View style={styles.imgContainer}>
              <MSBFastImage style={styles.icPhoneSuccess} resizeMode="cover" source={Images.imgBiometricPhone} />
            </View>
            <View style={styles.boxContainer}>
              <MSBTextBase style={styles.txtIDBenefit}>{translate('paymentInfor.biometricAuthentication')}</MSBTextBase>
              <MSBTouchable
                onPress={() => {
                  hostSharedModule.d.domainService.undevelopedFeature();
                }}>
                <MSBTextBase style={styles.txtSeeDetail}>{translate('paymentInfor.identification')}</MSBTextBase>
              </MSBTouchable>
            </View>
          </View>
        </View>
      </BottomSheetScrollView>
    </View>
  );
};

export default BiometricAuthentication;
const makeStyle = createMSBStyleSheet(({ColorAlias, ColorDataView, ColorGlobal, SizeGlobal, Typography}) => {
  return {
    benefitContainer: {
      alignItems: 'center',
      backgroundColor: ColorGlobal.Neutral50,
      borderRadius: SizeGlobal.Size400,
      flexDirection: 'row',
      marginTop: SizeGlobal.Size400,
      padding: SizeGlobal.Size400,
      width: '100%',
    },
    boxContainer: {
      flex: 1,
      marginLeft: SizeGlobal.Size400,
    },
    container: {
      flex: 1,
      paddingHorizontal: SizeGlobal.Size400,
    },
    content: {
      alignItems: 'center',
      flex: 1,
    },
    contentContainer: {
      paddingBottom: SizeGlobal.Size400,
    },
    icPhoneSuccess: {
      height: 115,
      width: 122,
    },
    icPhoneWarning: {
      height: 144,
      marginVertical: SizeGlobal.Size1200,
      width: 144,
    },
    imgContainer: {
      alignItems: 'center',
      borderRadius: SizeGlobal.Size300,
      height: 100,
      justifyContent: 'center',
      width: 100,
    },
    txtIDBenefit: {
      ...Typography?.small_regular,
      color: ColorAlias.TextPrimary,
    },
    txtIDInvalid: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
      textAlign: 'center',
    },
    txtSeeDetail: {
      ...Typography?.small_medium,
      color: ColorGlobal.Blue500,
      textDecorationLine: 'underline',
    },
    txtUpdateIDOnline: {
      ...Typography?.small_semiBold,
      color: ColorAlias.TextPrimary,
    },
  };
});
