// for test

import React from 'react';
import {View, Text, FlatList} from 'react-native';
import {createMSBStyleSheet, MSBFolderImage, MSBIcon, MSBIconSize, useMSBStyles} from 'msb-shared-component';
import {PaymentBillInfoProps} from './types';
import FormatUtils from '../../../../utils/FormatUtils';
import {translate} from '../../../../locales/i18n';

const PaymentBillInfo = ({style, isTopup, customerInfo, billList}: PaymentBillInfoProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={[styles.container, style]}>
      <View style={styles.customerInfo}>
        <MSBIcon
          folderIcon={isTopup ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
          icon={customerInfo.logo}
          iconSize={MSBIconSize.SIZE_40}
          styleContainer={styles.icLogo}
        />
        <View style={styles.accInfo}>
          <Text style={[styles.txtName]}>{customerInfo.fullName}</Text>

          <View style={styles.accNumber}>
            <Text style={[styles.txtBank]} numberOfLines={1}>
              {customerInfo.categoryName}
            </Text>
            <View style={styles.dot} />
            <Text style={[styles.txtBank, {flex: 1}]} numberOfLines={1}>
              {customerInfo.billCode}
            </Text>
          </View>
        </View>
      </View>
      <FlatList
        data={billList}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({item}) => (
          <View style={styles.billItem}>
            <Text style={styles.dateTime}>
              {item?.dateTime ? `${translate('paymentInfor.bill')} ${item.dateTime}` : translate('paymentInfor.total')}
            </Text>
            <View style={styles.row}>
              <Text style={styles.amount}>{`${FormatUtils.formatMoney(item.amount.toString())}`}</Text>
              <Text style={styles.currency}>{' VND'}</Text>
            </View>
          </View>
        )}
        showsHorizontalScrollIndicator={false}
        // ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      {billList.length > 1 && (
        <View>
          <View style={styles.billItem}>
            <Text style={styles.dateTime}>{translate('paymentInfor.total')}</Text>
            <View style={styles.row}>
              <Text style={styles.totalAmount}>
                {`${FormatUtils.formatMoney(
                  billList.reduce((accumulator, currentValue) => accumulator + currentValue.amount, 0).toString(),
                )}`}
              </Text>
              <Text style={styles.totalCurrency}>{' VND'}</Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default PaymentBillInfo;

export const makeStyle = createMSBStyleSheet(({ColorGlobal, ColorAlias, ColorField, ColorDataView, Typography}) => {
  return {
    accInfo: {
      flex: 1,
      marginLeft: 12,
    },

    customerInfo: {
      alignItems: 'center',
      flexDirection: 'row',
      borderBottomWidth: 1,
      paddingBottom: 4,
      borderBottomColor: ColorField.BorderDefault,
    },
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 4,
      // flex: 1,
    },
    container: {
      backgroundColor: ColorGlobal.NeutralWhite,
    },
    dot: {
      backgroundColor: ColorDataView.IconDefault,
      borderRadius: 2,
      height: 4,
      marginHorizontal: 8,
      width: 4,
    },
    icLogo: {
      height: 32,
      width: 32,
    },
    txtBank: {
      ...Typography?.small_regular,
      color: ColorDataView.IconDefault,
    },
    txtName: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },

    // list
    billItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 8,
    },
    dateTime: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    amount: {
      ...Typography?.small_semiBold,
      color: ColorDataView.TextMain,
    },

    totalAmount: {
      ...Typography?.title_semiBold,
      color: ColorDataView.TextMain,
    },

    row: {
      flexDirection: 'row',
    },
    currency: {
      ...Typography?.small_regular,
      color: ColorDataView.TextCurrency,
    },
    totalCurrency: {
      ...Typography?.title_medium,
      color: ColorDataView.TextCurrency,
    },
  };
});
