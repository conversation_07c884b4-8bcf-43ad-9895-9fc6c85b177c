// for test

import React from 'react';
import {View, FlatList} from 'react-native';
import {
  createMSBStyleSheet,
  MSBFolderImage,
  MSBIcon,
  MSBIconSize,
  MSBTextBase,
  SizeAlias,
  SizeGlobal,
  useMSBStyles,
} from 'msb-shared-component';
import {PaymentBillInfoProps} from './types';
import FormatUtils from '../../../../utils/FormatUtils';
import {translate} from '../../../../locales/i18n';

const PaymentBillInfo = ({style, isTopup, customerInfo, billList}: PaymentBillInfoProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={[styles.container, style]}>
      <View style={styles.customerInfo}>
        <MSBIcon
          folderIcon={isTopup ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
          icon={customerInfo.logo}
          iconSize={MSBIconSize.SIZE_40}
          styleContainer={styles.icLogo}
        />
        <View style={styles.accInfo}>
          <MSBTextBase style={[styles.txtName]} content={customerInfo.fullName} />

          <View style={styles.accNumber}>
            <MSBTextBase style={[styles.txtBank]} numberOfLines={1} content={customerInfo.categoryName} />
            <View style={styles.dot} />
            <MSBTextBase style={[styles.txtBank, {flex: 1}]} numberOfLines={1} content={customerInfo.billCode} />
          </View>
        </View>
      </View>
      <FlatList
        data={billList}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({item}) => (
          <View style={styles.billItem}>
            <MSBTextBase
              style={styles.dateTime}
              content={
                item?.dateTime ? `${translate('paymentInfor.bill')} ${item.dateTime}` : translate('paymentInfor.total')
              }
            />
            <View style={styles.row}>
              <MSBTextBase style={styles.amount} content={`${FormatUtils.formatMoney(item.amount.toString())}`} />
              <MSBTextBase style={styles.currency} content=" VND" />
            </View>
          </View>
        )}
        showsHorizontalScrollIndicator={false}
        // ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      {billList.length > 1 && (
        <View>
          <View style={styles.billItem}>
            <MSBTextBase style={styles.dateTime} content={translate('paymentInfor.total')} />
            <View style={styles.row}>
              <MSBTextBase
                style={styles.totalAmount}
                content={`${FormatUtils.formatMoney(
                  billList.reduce((accumulator, currentValue) => accumulator + currentValue.amount, 0).toString(),
                )}`}
              />
              <MSBTextBase style={styles.totalCurrency} content=" VND" />
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default PaymentBillInfo;

export const makeStyle = createMSBStyleSheet(({ColorGlobal, ColorAlias, ColorField, ColorDataView, Typography}) => {
  return {
    accInfo: {
      flex: 1,
      marginLeft: SizeAlias.SpacingXSmall,
    },

    customerInfo: {
      alignItems: 'center',
      flexDirection: 'row',
      borderBottomWidth: 1,
      paddingBottom: SizeAlias.Spacing4xSmall,
      borderBottomColor: ColorField.BorderDefault,
    },
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: SizeAlias.Spacing4xSmall,
      // flex: 1,
    },
    container: {
      backgroundColor: ColorGlobal.NeutralWhite,
    },
    dot: {
      backgroundColor: ColorDataView.IconDefault,
      borderRadius: SizeGlobal.Size50,
      height: SizeGlobal.Size100,
      marginHorizontal: SizeAlias.Spacing2xSmall,
      width: SizeGlobal.Size100,
    },
    icLogo: {
      height: SizeGlobal.Size800,
      width: SizeGlobal.Size800,
    },
    txtBank: {
      ...Typography?.small_regular,
      color: ColorDataView.IconDefault,
    },
    txtName: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },

    // list
    billItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: SizeAlias.Spacing2xSmall,
    },
    dateTime: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    amount: {
      ...Typography?.small_semiBold,
      color: ColorDataView.TextMain,
    },

    totalAmount: {
      ...Typography?.title_semiBold,
      color: ColorDataView.TextMain,
    },

    row: {
      flexDirection: 'row',
    },
    currency: {
      ...Typography?.small_regular,
      color: ColorDataView.TextCurrency,
    },
    totalCurrency: {
      ...Typography?.title_medium,
      color: ColorDataView.TextCurrency,
    },
  };
});
