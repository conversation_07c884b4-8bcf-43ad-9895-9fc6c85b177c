import {ColorAlias, ColorLabelCaption, createMSBStyleSheet} from 'msb-shared-component';

import DimensionUtils from '../../utils/DimensionUtils';

export const makeStyle = createMSBStyleSheet(({Size<PERSON>lia<PERSON>, Typography, Shadow}) => {
  return {
    accountInfo: {
      backgroundColor: ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.SpacingXSmall,
      paddingHorizontal: SizeAlias.SpacingSmall,
      paddingVertical: SizeAlias.SpacingXSmall,
      ...Shadow.center,
    },
    amountInput: {
      ...Typography?.base_medium,
      marginTop: SizeAlias.SpacingSmall,
    },
    btnContinue: {
      marginHorizontal: SizeAlias.SpacingSmall,
    },
    container: {
      flex: 1,
    },

    contentContainerScrollView: {
      padding: SizeAlias.SpacingSmall,
    },

    contentContainer: {
      backgroundColor: ColorAlias.BackgroundWhite,
      borderRadius: SizeAlias.Radius3,
      marginTop: SizeAlias.SpacingXLarge,
      padding: SizeAlias.SpacingSmall,
      ...Shadow.center,
    },
    contentLabel: {
      color: ColorLabelCaption.TextMain,
      marginRight: SizeAlias.Spacing4xSmall,
      paddingBottom: SizeAlias.Spacing4xSmall,
    },
    flexDirectionRow: {
      flexDirection: 'row',
    },
    imageBackground: {
      flex: 1,
      resizeMode: 'stretch',
    },
    scrollViewContentContainer: {
      paddingBottom: SizeAlias.Spacing3xLarge,
    },
  };
});
