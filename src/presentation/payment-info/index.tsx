import {MSBButton, MSBLoadingItemSkeleton, MSBPage, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import usePaymentInfo from './hook.ts';
import PaymentBillInfo from './components/payment-bill-info/index.tsx';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import SourceAccountList from '../../components/source-account-list/index.tsx';
import DimensionUtils from '../../utils/DimensionUtils.ts';
import {translate} from '../../locales/i18n.ts';
import {makeStyle} from './style.ts';
import {Federated} from '@callstack/repack/client';
import BiometricAuthentication from './components/biometric-authentication/index.tsx';

/**
 * Khi ở build time sẽ sử dụng MFv1 nên là không thể sử dụng dynamic import của MFv2
 * => Sử dụng {@link Federated.importModule} để bundle
 *
 * Lưu ý!: không tách biệt điều kiện process.env.NODE_ENV === 'production' ra hàm riêng biệt vì lúc build sẽ làm cho dynamic import() được bundle vào
 * => gây ra lỗi lúc build
 *
 * Không thể sử dụng được dynamic import() từ hàm
 * => !Nên mỗi khi load module thì phải định nghĩa lại như dưới
 */
const SourceAccount = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('TransferModule', './SourceAccount')
    : import('TransferModule/SourceAccount'),
);
const PaymentInfoScreen = () => {
  const {styles} = useMSBStyles(makeStyle);

  const {onContinue, paymentInfo, sourceAccDefault, isLoadingSourceAccount, onSelectAccount, goHome, isLoading} =
    usePaymentInfo(
      accountList => (
        <BottomSheetView
          style={{
            height: (DimensionUtils.getWindowHeight() * 80) / 100,
            paddingBottom: DimensionUtils.getPaddingBottomByDevice(),
          }}>
          <SourceAccountList
            accSelected={sourceAccDefault}
            accountList={accountList!}
            onSelectAcount={onSelectAccount}
          />
        </BottomSheetView>
      ),
      () => <BiometricAuthentication content={translate('paymentInfor.biometricAuthentication')} />,
      () => <BiometricAuthentication content={translate('paymentInfor.identification')} />,
    );
  console.log('paymentInfo====>', paymentInfo);
  return (
    <MSBPage
      isScrollable={false}
      headerProps={{
        hasBack: true,
        rightButtons: [{icon: 'tone-home', onPress: goHome}],
        title: `${translate('paymentBill.title')} ${paymentInfo?.categoryName?.toLocaleLowerCase()}`,
      }}
      style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.contentContainerScrollView}
        contentContainerStyle={styles.scrollViewContentContainer}>
        <PaymentBillInfo
          // title={}
          style={styles.accountInfo}
          isTopup={paymentInfo?.provider?.isTopup() === true}
          customerInfo={{
            logo: paymentInfo.provider?.subGroupId?.toString() ?? '',
            fullName: paymentInfo?.billInfo?.billList?.[0].custName?.toUpperCase() ?? '',
            categoryName: paymentInfo.categoryName ?? '',
            billCode: paymentInfo?.billInfo?.billCode ?? '',
          }}
          billList={
            paymentInfo?.billInfo?.billList?.map(item => ({
              dateTime: item.period ?? '',
              amount: item.amount ?? 0,
            })) ?? []
          }
        />

        <View style={styles.contentContainer}>
          {isLoadingSourceAccount ? (
            <MSBLoadingItemSkeleton loading={isLoadingSourceAccount} />
          ) : (
            <SourceAccount
              title={translate('paymentInfor.sourceAccount')}
              accNo={sourceAccDefault?.BBAN}
              onSelectAccount={onSelectAccount}
            />
          )}
        </View>
      </KeyboardAwareScrollView>
      <MSBButton
        testID={'payment.transferInfoScreen.pressToContinue'}
        label={'Tiếp tục'}
        isLoading={isLoading}
        style={styles.btnContinue}
        onPress={onContinue}
        // disabled={disableTransferBtn}
      />
    </MSBPage>
  );
};

export default PaymentInfoScreen;
