import {hostSharedModule, PopupType} from 'msb-host-shared-module';
import {ReactNode} from 'react';
import {translate} from '../../../locales/i18n';
import Utils from '../../../utils/Utils';

// chec gói truy vấn
const checkIBMB = (onConfirm?: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('paymentInfor.paymentInfoValidate.checkIBMB'),
    content: translate('paymentInfor.paymentInfoValidate.checkIBMBDescription'),
    confirmBtnText: translate('close'),
    onConfirm,
  });
};

// chưa thu nhập sinh trắc học
const checkBiometricAuthentication = (
  BiometricAuthentication: ReactNode,
  onConfirm?: () => void,
  onCancel?: () => void,
) => {
  hostSharedModule.d.domainService?.showBottomSheet({
    header: translate('paymentConfirm.transferNotYetExecuted'),
    children: BiometricAuthentication,
    cancelBtnText: translate('paymentConfirm.doItLater'),
    confirmBtnText: translate('paymentConfirm.updateNow'),
    onCancel,
    onConfirm,
    enableDynamicSizing: false,
  });
};
// giấy tờ tuỳ thân hết hạn
const checkIdentification = (Identification: ReactNode, onConfirm?: () => void, onCancel?: () => void) => {
  hostSharedModule.d.domainService?.showBottomSheet({
    header: translate('paymentConfirm.transferNotYetExecuted'),
    children: Identification,
    cancelBtnText: translate('paymentConfirm.doItLater'),
    confirmBtnText: translate('paymentConfirm.updateNow'),
    onCancel,
    onConfirm,
    enableDynamicSizing: false,
  });
};

// Có gián đoạn tạm thời
const checkErrorSystem = (errorMessage?: string, onConfirm?: () => void, errorCode?: string) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('paymentConfirm.haveTemporaryInterruption'),
    content: errorMessage || translate('paymentConfirm.explainHaveTemporaryInterruption'),
    confirmBtnText: translate('close'),
    onConfirm,
    errorCode: errorCode ? `Mã: ${errorCode}` : '',
  });
};

// check tài khoản nguồn trùng tài khoản thụ hưởng
const checkDuplicateSourceAccount = (onConfirm?: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('paymentInfor.paymentInfoValidate.duplicateSourceAccount'),
    content: translate('paymentInfor.paymentInfoValidate.duplicateSourceAccountDescription'),
    confirmBtnText: translate('close'),
    onConfirm,
  });
};

// số tiền giao dịch vượt quá hạn mức còn lại trong ngayf
const checkDailyAvailableLimit = (dailyAvailableLimit: string, onConfirm: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('paymentInfor.paymentInfoValidateTitle'),
    content: translate('paymentInfor.paymentInfoValidateDescription', {
      value: dailyAvailableLimit,
    }),
    cancelBtnText: translate('close'),
    confirmBtnText: 'Cài đặt hạn mức',
    onConfirm,
  });
};

// không tìm thấy thông tin người nhận Nội bộ
const checkVisibleCounterPartyMSB = (onConfirm?: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('paymentInfor.userNotFound'),
    content: translate('paymentInfor.userNotFoundDescription'),
    confirmBtnText: translate('close'),
    onConfirm,
  });
};

const userNotValid = (onConfirm?: () => void) => {
  Utils.showPopup({
    iconType: PopupType.WARNING,
    title: translate('paymentInfor.userNotValid'),
    content: translate('paymentInfor.userNotValidDescription'),
    confirmBtnText: translate('close'),
    onConfirm,
  });
};

export default {
  checkIBMB,
  checkBiometricAuthentication,
  checkIdentification,
  checkErrorSystem,
  checkDuplicateSourceAccount,
  checkDailyAvailableLimit,
  checkVisibleCounterPartyMSB,
  userNotValid,
};
