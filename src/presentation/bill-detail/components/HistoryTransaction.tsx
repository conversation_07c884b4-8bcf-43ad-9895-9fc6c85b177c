import React from 'react';
import {View, ActivityIndicator, SectionList} from 'react-native';
import {EmptyType, MSBTextBase, createMSBStyleSheet, useMSBStyles} from 'msb-shared-component';
import {EmptyTransactionHistoryScreen} from '../../payment-home/components/bill-list/EmptyBill';
import FormatUtils from '../../../utils/FormatUtils';
import {CustomError} from '../../../core/MSBCustomError';
import {MSBErrorCode} from '../../../core/MSBErrorCode';
import {I18nKeys, translate} from '../../../locales/i18n';

export interface TransactionItem {
  id: string;
  transName: string;
  content: string;
  amount: string;
  transDate: string;
}

export interface HistoryTransactionProps {
  data: {id: string; title?: string; data: TransactionItem[]}[];
  loading?: boolean;
  fetching?: boolean;
  containerStyle?: any;
  error?: CustomError;
}

const HistoryTransaction: React.FC<HistoryTransactionProps> = ({data, loading, fetching, containerStyle, error}) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  if (loading || fetching) {
    return (
      <View style={[styles.loadingContainer, containerStyle]}>
        <ActivityIndicator size="small" color={theme.ColorGlobal.Neutral600} />
      </View>
    );
  }

  const renderSectionHeader = ({section}: {section: {title?: string}}) => {
    if (!section.title) {
      return null;
    }

    return (
      <View style={styles.sectionHeader}>
        <MSBTextBase style={styles.sectionHeaderText} content={section.title} />
      </View>
    );
  };

  const renderItem = ({item}: {item: TransactionItem}) => (
    <View style={styles.itemContainer}>
      <View style={[styles.itemLeft]}>
        <MSBTextBase style={styles.transName} content={item.transName} />
        <MSBTextBase style={styles.content} content={item.content} />
      </View>
      <View style={styles.itemRight}>
        <MSBTextBase style={styles.amount} content={'+' + FormatUtils.formatMoney(item.amount)} />
        <MSBTextBase style={styles.time} content={FormatUtils.formatDateHHMM(item.transDate)} />
      </View>
    </View>
  );

  if (error) {
    return (
      <EmptyTransactionHistoryScreen
        type={error.code === MSBErrorCode.EMPTY_DATA ? EmptyType.Editable : EmptyType.Connection}
        title={translate(error.title as I18nKeys)}
        content={translate(error.message as I18nKeys)}
      />
    );
  }

  return (
    <SectionList
      sections={data}
      keyExtractor={item => item.id}
      style={containerStyle}
      renderItem={renderItem}
      renderSectionHeader={renderSectionHeader}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
      stickySectionHeadersEnabled={false}
    />
  );
};

const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => ({
  sectionHeader: {
    backgroundColor: ColorGlobal.Neutral50,
    paddingVertical: SizeGlobal.Size300,
    paddingHorizontal: SizeGlobal.Size400,
  },
  sectionHeaderText: {
    ...Typography?.caption_medium,
    color: ColorGlobal.Neutral600,
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: SizeGlobal.Size400,
    borderBottomWidth: 1,
    borderBottomColor: ColorGlobal.Neutral100,
    marginHorizontal: 0,
  },
  itemLeft: {
    flex: 0.7,
    marginRight: SizeGlobal.Size200,
  },
  transName: {
    ...Typography?.small_semiBold,
    color: ColorGlobal.Neutral800,
    marginBottom: SizeGlobal.Size100,
  },
  content: {
    ...Typography?.small_regular,
    color: ColorGlobal.Neutral600,
  },
  itemRight: {
    alignItems: 'flex-end',
    flex: 0.3,
  },
  amount: {
    ...Typography?.small_semiBold,
    color: ColorGlobal.Green500,
    marginBottom: SizeGlobal.Size100,
  },
  time: {
    ...Typography?.small_medium,
    color: ColorGlobal.Neutral600,
  },
  separator: {
    height: 1,
    backgroundColor: ColorGlobal.Neutral100,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 120,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 120,
  },
  emptyText: {
    ...Typography?.small_regular,
    color: ColorGlobal.Neutral400,
  },
}));

export default HistoryTransaction;
