import {useState, useCallback, useEffect, useMemo} from 'react';
import {DIContainer} from '../../di/DIContainer';
import {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest';
import {PaymentOrderStatusModel} from '../../domain/entities/payment-order-status/PaymentOrderStatusModel';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';
import {RouteProp, useRoute} from '@react-navigation/native';
import {PaymentStackParamList} from '../../navigation/PaymentStack';
import {GetMyBillHistoryListRequest} from '../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';
import {GetMyBillHistoryListModel} from '../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel';
import {TransactionItem} from './components/HistoryTransaction';
import {HistoryTransactionProps} from './components/HistoryTransaction';
import FormatUtils from '../../utils/FormatUtils';
import {CustomError} from '../../core/MSBCustomError';

export const useBillDetail = () => {
  const route = useRoute<RouteProp<PaymentStackParamList, 'BillDetailScreen'>>();
  const account = route.params?.account;
  const contact = route.params?.contact;
  // State for getPaymentBill
  const [billLoading, setBillLoading] = useState(false);
  const [billError, setBillError] = useState<CustomError | undefined>();
  const [billDetail, setBillDetail] = useState<GetBillDetailModel | undefined>(undefined); // Replace 'any' with GetBillDetailModel

  // State for getOrderStatus
  const [orderStatusLoading, setOrderStatusLoading] = useState(false);
  const [orderStatus, setOrderStatus] = useState<PaymentOrderStatusModel | undefined>(undefined); // Replace 'any' with actual status type
  const [orderStatusError, setOrderStatusError] = useState<string | undefined>();

  // State for getBillHistory
  const [billHistoryLoading, setBillHistoryLoading] = useState(false);
  const [billHistoryError, setBillHistoryError] = useState<CustomError | undefined>();
  const [billHistory, setBillHistory] = useState<GetMyBillHistoryListModel[]>();

  const historiesTransaction = useMemo<HistoryTransactionProps['data']>(() => {
    if (!billHistory || billHistory.length === 0) {
      return [];
    }

    // Group transactions by formatted payment date
    const groupedByDate = billHistory.reduce((acc, item) => {
      const paymentDate = item.paymentDate ?? '';
      const formattedDate = paymentDate ? FormatUtils.formatDateDDMMYYYY(paymentDate) : '';

      if (!acc[formattedDate]) {
        acc[formattedDate] = [];
      }

      acc[formattedDate].push({
        id: item.id ?? '',
        transName: item.period ? 'Hoá đơn tháng ' + item.period : '',
        content: item.coreRef ? `${item.customerName} THANH TOAN ${item.coreRef}` : '',
        amount: item.totalAmount?.toString() ?? '',
        transDate: item.paymentDate ?? '',
      });

      return acc;
    }, {} as Record<string, TransactionItem[]>);

    // Convert grouped data to the required format
    return Object.entries(groupedByDate)
      .filter(([date]) => date !== '') // Filter out empty dates
      .sort(([dateA], [dateB]) => {
        // Sort by original date for proper chronological order
        const originalDateA =
          billHistory.find(item => FormatUtils.formatDateDDMMYYYY(item.paymentDate ?? '') === dateA)?.paymentDate ?? '';
        const originalDateB =
          billHistory.find(item => FormatUtils.formatDateDDMMYYYY(item.paymentDate ?? '') === dateB)?.paymentDate ?? '';
        return new Date(originalDateB).getTime() - new Date(originalDateA).getTime();
      })
      .map(([formattedDate, transactions]) => ({
        id: formattedDate,
        title: formattedDate, // Use the formatted date as the section title
        data: transactions.sort((a, b) => new Date(b.transDate).getTime() - new Date(a.transDate).getTime()), // Sort transactions within each group
      }));
  }, [billHistory]);

  // getPaymentBill function (like payment-phone/hook.tsx)
  const getPaymentBill = useCallback(async (request: GetBillDetailRequest /* GetBillDetailRequest */) => {
    setBillLoading(true);
    setBillError(undefined);
    try {
      // Replace with actual DIContainer usage
      const result = await DIContainer.getInstance().getGetBillDetailUseCase().execute(request);
      if (result?.status === 'ERROR') {
        setBillError(result.error);
        setBillDetail(undefined);
        return undefined;
      }
      if (!result?.data?.billCode) {
        setBillDetail(undefined);
        return undefined;
      }
      setBillDetail(result.data);
      return result.data;
    } catch (error: any) {
      setBillError(error);
      setBillDetail(undefined);
      return undefined;
    } finally {
      setBillLoading(false);
    }
  }, []);

  // getOrderStatus function (like payment-confirm/hook.ts)
  const getOrderStatus = useCallback(async (paymentMode: string = 'RECURRING') => {
    setOrderStatusLoading(true);
    setOrderStatusError(undefined);
    try {
      // Replace with actual DIContainer usage
      const result = await DIContainer.getInstance().getPaymentOrderStatusUseCase().execute({paymentMode});
      if (result?.status === 'SUCCESS') {
        setOrderStatus(result.data);
        return result.data;
      } else {
        setOrderStatusError('Failed to get order status');
        setOrderStatus(undefined);
        return undefined;
      }
    } catch (error) {
      setOrderStatusError('Error occurred while getting order status');
      setOrderStatus(undefined);
      return undefined;
    } finally {
      setOrderStatusLoading(false);
    }
  }, []);

  // getBillHistory function
  const getBillHistory = useCallback(async (request: GetMyBillHistoryListRequest) => {
    setBillHistoryLoading(true);
    setBillHistoryError(undefined);
    try {
      const result = await DIContainer.getInstance().getGetMyBillHistoryListUseCase().execute(request);
      if (result?.status === 'ERROR') {
        setBillHistoryError(result.error);
        setBillHistory(undefined);
        return undefined;
      }
      if (result?.status === 'SUCCESS' && result.data) {
        console.log('🛠 LOG: 🚀 --> -----------------------------------------------------------🛠 LOG: 🚀 -->');
        console.log('🛠 LOG: 🚀 --> ~ getBillHistory ~ result.data:', result.data);
        console.log('🛠 LOG: 🚀 --> -----------------------------------------------------------🛠 LOG: 🚀 -->');
        // if (result.data.length === 0) {
        //   setBillHistoryError(
        //     new CustomError(
        //       MSBErrorCode.EMPTY_DATA,
        //       'Chưa có giao dịch được thực hiện',
        //       'Thông tin về các giao dịch thanh toán sẽ được hiển thị ở đây',
        //       ['Đóng'],
        //     ),
        //   );
        //   setBillHistory(undefined);
        //   return undefined;
        // }
        // setBillHistory(result.data);
        return result.data;
      }
      setBillHistory(undefined);

      return undefined;
    } catch (error: any) {
      setBillHistoryError(error);
      setBillHistory(undefined);
      return undefined;
    } finally {
      setBillHistoryLoading(false);
    }
  }, []);

  useEffect(() => {
    getPaymentBill({
      billCode: account?.accountNumber ?? '',
      serviceCode: account?.bankCode ?? '',
      accountingType: 'ACCT',
    });

    getOrderStatus();
    getBillHistory({
      typeGroup: 'PAYMENT',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    // Account
    account,
    contact,
    // Payment bill
    billLoading,
    billError,
    billDetail,
    getPaymentBill,
    // Order status
    orderStatusLoading,
    orderStatus,
    orderStatusError,
    getOrderStatus,
    // Bill history
    billHistoryLoading,
    billHistoryError,
    billHistory,
    getBillHistory,
    historiesTransaction,
  };
};

export default useBillDetail;
