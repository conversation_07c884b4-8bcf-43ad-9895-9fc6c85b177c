import React from 'react';
import {View, ScrollView} from 'react-native';
import {
  MSBTextBase,
  createMSBStyleSheet,
  useMSBStyles,
  MSBPage,
  MSBChip,
  MSBChipStyle,
  MSBChipState,
  MSBLoadingItemSkeleton,
  MSBTouchable,
  MSBFastImage,
} from 'msb-shared-component';
import HistoryTransaction from './components/HistoryTransaction';
import {useBillDetail} from './hook';
import {MSBErrorCode} from '../../core/MSBErrorCode';
import Images from '../../assets/images/Images';
import FormatUtils from '../../utils/FormatUtils';
import {translate} from '../../locales/i18n';
import Utils from '../../utils/Utils';

const BillDetailScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  const {billDetail, billError, billLoading, account, historiesTransaction, billHistoryError, contact} =
    useBillDetail();

  console.log('billDetail', billDetail);

  const hasBilling = billDetail?.billList && billDetail?.billList?.length > 0;

  const isPaid = billError?.code === MSBErrorCode.PIS0101 || billError?.code === MSBErrorCode.PIS0102;

  const isAutoPay = billDetail?.paymentRule === 1;

  const icon = Utils.getProviderIcon(contact.getIcon());

  return (
    <View style={styles.container}>
      <MSBPage isScrollable={false} headerProps={{hasBack: true, title: 'Chi tiết hóa đơn'}} style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {/* Bill Info Card */}
          <View style={styles.billInfoContainer}>
            <View style={styles.billInfoRow}>
              <View style={styles.iconCircle}>
                {icon ? (
                  <MSBFastImage source={icon} style={styles.iconProvider} />
                ) : (
                  <MSBFastImage source={Images.icBill} style={styles.iconProvider} />
                )}
              </View>
              <View style={styles.billInfoText}>
                <MSBTextBase
                  style={styles.customerName}
                  content={billDetail?.billList?.[0]?.custName ?? contact.getCustomerName?.()}
                />
                <View style={styles.billInfoSubRow}>
                  <MSBTextBase style={styles.billInfoSubText} content={account?.bankName ?? ''} />
                  <MSBTextBase style={styles.dotSeparator} content=" • " />
                  <MSBTextBase
                    style={styles.billInfoSubText}
                    content={billDetail?.billCode ?? contact.getBillCode?.()}
                  />
                </View>
              </View>
            </View>
            <View>
              {billLoading ? (
                <MSBLoadingItemSkeleton loading />
              ) : billDetail?.billList?.length ? (
                <View style={styles.billDetail}>
                  {billDetail?.billList?.map((item, index) => (
                    <View key={index} style={styles.billInfoBottomRow}>
                      <MSBTextBase style={styles.billLabel} content={`Hóa đơn T${item.period}`} />
                      <MSBTextBase
                        style={styles.amountText}
                        content={`${FormatUtils.formatMoney(item?.amount?.toString() ?? '')} VND`}
                      />
                    </View>
                  ))}
                </View>
              ) : null}
              {hasBilling && (
                <View style={styles.billInfoBottomRow}>
                  <MSBTextBase style={styles.statusLabel} content={translate('common.status')} />
                  <MSBChip
                    style={{backgroundColor: theme.ColorGlobal.Red50, borderWidth: 0}}
                    chipState={MSBChipState.Default}
                    chipStyle={MSBChipStyle.Large}
                    title={'Chưa thanh toán'}
                  />
                </View>
              )}
              {isPaid && (
                <View style={styles.billInfoBottomRow}>
                  <MSBTextBase style={styles.statusLabel} content={translate('common.status')} />
                  <MSBChip
                    style={{backgroundColor: theme.ColorGlobal.Green50, borderWidth: 0}}
                    chipState={MSBChipState.Default}
                    chipStyle={MSBChipStyle.Large}
                    title={'Đã thanh toán'}
                  />
                </View>
              )}
            </View>
          </View>
          {/* Action Buttons */}
          <View style={styles.actionRow}>
            {!isPaid && (
              <View style={styles.actionOptionContainer}>
                <MSBTouchable style={styles.actionOption} onPress={() => {}}>
                  <MSBFastImage source={Images.icDiagonalRight} style={styles.actionIcon} />
                  <MSBTextBase style={styles.actionLabel} content={translate('common.payment')} />
                </MSBTouchable>
              </View>
            )}
            <View style={styles.actionOptionContainer}>
              <MSBTouchable style={styles.actionOption} onPress={() => {}}>
                <MSBFastImage source={Images.icClock} style={styles.actionIcon} />
                <MSBTextBase style={styles.actionLabel} content={translate('common.paymentAuto')} />
              </MSBTouchable>
            </View>
          </View>
          {/* Payment History */}
          <View style={styles.historyContainer}>
            <MSBTextBase style={styles.historyTitle} content={translate('common.historyPayment')} />
            <HistoryTransaction
              data={historiesTransaction ?? []}
              loading={false}
              fetching={false}
              containerStyle={styles.historyList}
              error={billHistoryError}
            />
          </View>
        </ScrollView>
      </MSBPage>
    </View>
  );
};

const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => ({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SizeGlobal.Size2400,
  },
  billInfoContainer: {
    marginHorizontal: SizeGlobal.Size400,
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeGlobal.Size300,
    padding: SizeGlobal.Size400,
    shadowColor: ColorGlobal.Neutral800,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 5,
  },
  billInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconCircle: {
    width: SizeGlobal.Size800,
    height: SizeGlobal.Size800,
    borderRadius: SizeGlobal.Size800 / 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SizeGlobal.Size300,
  },
  iconProvider: {
    width: SizeGlobal.Size800,
    height: SizeGlobal.Size800,
    resizeMode: 'contain',
  },
  billInfoText: {
    flex: 1,
  },
  customerName: {
    ...Typography?.base_medium,
    color: ColorGlobal.Neutral800,
    marginBottom: SizeGlobal.Size100,
  },
  billInfoSubRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  billInfoSubText: {
    ...Typography?.small_regular,
    color: ColorGlobal.Neutral500,
  },
  dotSeparator: {
    ...Typography?.small_regular,
    color: ColorGlobal.Neutral500,
  },
  billInfoBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  billLabel: {
    ...Typography?.small_medium,
    color: ColorGlobal.Neutral800,
    marginBottom: SizeGlobal.Size100,
  },
  billDetail: {
    marginTop: SizeGlobal.Size200,
    paddingTop: SizeGlobal.Size300,
    borderTopWidth: 1,
    borderColor: ColorGlobal.Neutral200,
  },
  statusLabel: {
    ...Typography?.small_regular,
    color: ColorGlobal.Neutral400,
  },
  amountText: {
    ...Typography?.base_semiBold,
    color: ColorGlobal.Neutral800,
    marginBottom: SizeGlobal.Size100,
  },
  statusBadge: {
    backgroundColor: ColorGlobal.Red50,
    borderRadius: SizeGlobal.Size200,
    paddingHorizontal: SizeGlobal.Size300,
    paddingVertical: SizeGlobal.Size100,
    alignSelf: 'flex-end',
  },
  statusBadgeText: {
    ...Typography?.small_medium,
    color: ColorGlobal.Red500,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: SizeGlobal.Size400,
    marginVertical: SizeGlobal.Size600,
    paddingHorizontal: SizeGlobal.Size200,
    paddingVertical: SizeGlobal.Size300,
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeGlobal.Size300,
    shadowColor: ColorGlobal.Neutral800,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  actionOptionContainer: {
    flex: 1,
    alignItems: 'center',
  },
  actionOption: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    flexDirection: 'column',
    gap: SizeGlobal.Size100,
  },
  actionIcon: {
    width: SizeGlobal.Size800,
    height: SizeGlobal.Size800,
  },
  actionLabel: {
    ...Typography?.caption_regular,
    color: ColorGlobal.Neutral800,
  },
  historyContainer: {
    marginHorizontal: SizeGlobal.Size400,
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeGlobal.Size300,
    padding: SizeGlobal.Size400,
    shadowColor: ColorGlobal.Neutral800,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 5,
    marginBottom: SizeGlobal.Size400,
  },
  historyTitle: {
    ...Typography?.small_medium,
    color: ColorGlobal.Neutral800,
    marginBottom: SizeGlobal.Size400,
  },
  historyList: {
    backgroundColor: 'transparent',
    borderRadius: 0,
    elevation: 0,
    shadowColor: 'transparent',
    paddingBottom: 0,
  },
}));

export default BillDetailScreen;
