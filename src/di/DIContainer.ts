import {GetMyBillHistoryListUseCase} from '../domain/usecases/bill-contact/GetMyBillHistoryListUseCase';
import {GetMyBillContactRecentListUseCase} from '../domain/usecases/bill-contact/GetMyBillContactRecentListUseCase';
import {MyBillContactListUseCase} from '../domain/usecases/bill-contact/MyBillContactListUseCase';
import {EditBillContactUseCase} from '../domain/usecases/bill-contact/EditBillContactUseCase';
import {DeleteBillContactUseCase} from '../domain/usecases/bill-contact/DeleteBillContactUseCase';
import {SaveBillContactUseCase} from '../domain/usecases/bill-contact/SaveBillContactUseCase';
import {BillContactRepository} from '../data/repositories/BillContactRepository';
import {IBillContactRepository} from '../domain/repositories/IBillContactRepository';
import {BillContactRemoteDataSource} from '../data/datasources/remote/BillContactRemoteDataSource';
import {IBillContactDataSource} from '../data/datasources/IBillContactDataSource';
import {PaymentOrderStatusUseCase} from '../domain/usecases/payment-order/PaymentOrderStatusUseCase';
import {PaymentOrderUseCase} from '../domain/usecases/payment-order/PaymentOrderUseCase';
import {PaymentOrderRepository} from '../data/repositories/PaymentOrderRepository';
import {IPaymentOrderRepository} from '../domain/repositories/IPaymentOrderRepository';
import {PaymentOrderRemoteDataSource} from '../data/datasources/remote/PaymentOrderRemoteDataSource';
import {IPaymentOrderDataSource} from '../data/datasources/IPaymentOrderDataSource';
import {GetBillDetailUseCase} from '../domain/usecases/bill-pay/GetBillDetailUseCase';
import {BillValidateUseCase} from '../domain/usecases/bill-pay/BillValidateUseCase';
import {ValidateUseCase} from '../domain/usecases/payment/ValidateUseCase';
import {PaymentRepository} from '../data/repositories/PaymentRepository';
import {IPaymentRepository} from '../domain/repositories/IPaymentRepository';
import {PaymentRemoteDataSource} from '../data/datasources/remote/PaymentRemoteDataSource';
import {IPaymentDataSource} from '../data/datasources/IPaymentDataSource';
import {MyBillListUseCase} from '../domain/usecases/bill-pay/MyBillListUseCase';
import {ProviderListUseCase} from '../domain/usecases/bill-pay/ProviderListUseCase';
import {SourceAccountListUseCase} from '../domain/usecases/arrangement/SourceAccountListUseCase';
import {ArrangementRepository} from '../data/repositories/ArrangementRepository';
import {IArrangementRepository} from '../domain/repositories/IArrangementRepository';
import {ArrangementRemoteDataSource} from '../data/datasources/remote/ArrangementRemoteDataSource';
import {IArrangementDataSource} from '../data/datasources/IArrangementDataSource';
import {GetProfileUseCase} from '../domain/usecases/customer/GetProfileUseCase';
import {CustomerRepository} from '../data/repositories/CustomerRepository';
import {ICustomerRepository} from '../domain/repositories/ICustomerRepository';
import {CustomerRemoteDataSource} from '../data/datasources/remote/CustomerRemoteDataSource';
import {ICustomerDataSource} from '../data/datasources/ICustomerDataSource';
import {CategoryListUseCase} from '../domain/usecases/bill-pay/CategoryListUseCase';
import {BillPayRepository} from '../data/repositories/BillPayRepository';
import {IBillPayRepository} from '../domain/repositories/IBillPayRepository';
import {BillPayRemoteDataSource} from '../data/datasources/remote/BillPayRemoteDataSource';
import {IBillPayDataSource} from '../data/datasources/IBillPayDataSource';
import {hostSharedModule, IHttpClient} from 'msb-host-shared-module';
import {ValidateCustomerUseCase} from '../domain/usecases/customer/ValidateCustomerUseCase';

export class DIContainer {
  private static instance: DIContainer;

  private httpClient: IHttpClient;

  private isUseMock = false;
  private billContactDataSource!: IBillContactDataSource;
  private billContactRepository!: IBillContactRepository;

  private paymentOrderDataSource!: IPaymentOrderDataSource;
  private paymentOrderRepository!: IPaymentOrderRepository;

  private paymentDataSource!: IPaymentDataSource;
  private paymentRepository!: IPaymentRepository;

  private arrangementDataSource!: IArrangementDataSource;
  private arrangementRepository!: IArrangementRepository;

  private customerDataSource!: ICustomerDataSource;
  private customerRepository!: ICustomerRepository;

  private billPayDataSource!: IBillPayDataSource;
  private billPayRepository!: IBillPayRepository;

  private constructor() {
    this.httpClient = hostSharedModule.d.httpClient!;
  }

  public static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer();
    }
    return DIContainer.instance;
  }

  //DATA SOURCES

  private getBillContactDataSource(): IBillContactDataSource {
    if (!this.billContactDataSource) {
      this.billContactDataSource = new BillContactRemoteDataSource(this.httpClient);
    }
    return this.billContactDataSource;
  }

  private getPaymentOrderDataSource(): IPaymentOrderDataSource {
    if (!this.paymentOrderDataSource) {
      this.paymentOrderDataSource = new PaymentOrderRemoteDataSource(this.httpClient);
    }
    return this.paymentOrderDataSource;
  }

  private getPaymentDataSource(): IPaymentDataSource {
    if (!this.paymentDataSource) {
      this.paymentDataSource = new PaymentRemoteDataSource(this.httpClient);
    }
    return this.paymentDataSource;
  }

  private getArrangementDataSource(): IArrangementDataSource {
    if (!this.arrangementDataSource) {
      this.arrangementDataSource = new ArrangementRemoteDataSource(this.httpClient);
    }
    return this.arrangementDataSource;
  }

  private getCustomerDataSource(): ICustomerDataSource {
    if (!this.customerDataSource) {
      this.customerDataSource = new CustomerRemoteDataSource(this.httpClient);
    }
    return this.customerDataSource;
  }

  private getBillPayDataSource(): IBillPayDataSource {
    if (!this.billPayDataSource) {
      this.billPayDataSource = new BillPayRemoteDataSource(this.httpClient);
    }
    return this.billPayDataSource;
  }

  // REPORITORIES

  public getBillContactRepository(): IBillContactRepository {
    if (!this.billContactRepository) {
      this.billContactRepository = new BillContactRepository(this.getBillContactDataSource());
    }
    return this.billContactRepository;
  }

  public getPaymentOrderRepository(): IPaymentOrderRepository {
    if (!this.paymentOrderRepository) {
      this.paymentOrderRepository = new PaymentOrderRepository(this.getPaymentOrderDataSource());
    }
    return this.paymentOrderRepository;
  }

  public getPaymentRepository(): IPaymentRepository {
    if (!this.paymentRepository) {
      this.paymentRepository = new PaymentRepository(this.getPaymentDataSource());
    }
    return this.paymentRepository;
  }

  public getArrangementRepository(): IArrangementRepository {
    if (!this.arrangementRepository) {
      this.arrangementRepository = new ArrangementRepository(this.getArrangementDataSource());
    }
    return this.arrangementRepository;
  }

  public getCustomerRepository(): ICustomerRepository {
    if (!this.customerRepository) {
      this.customerRepository = new CustomerRepository(this.getCustomerDataSource());
    }
    return this.customerRepository;
  }

  public getBillPayRepository(): IBillPayRepository {
    if (!this.billPayRepository) {
      this.billPayRepository = new BillPayRepository(this.getBillPayDataSource());
    }
    return this.billPayRepository;
  }

  // USE CASES

  public getGetMyBillHistoryListUseCase(): GetMyBillHistoryListUseCase {
    return new GetMyBillHistoryListUseCase(this.getBillContactRepository());
  }

  public getGetMyBillContactRecentListUseCase(): GetMyBillContactRecentListUseCase {
    return new GetMyBillContactRecentListUseCase(this.getBillContactRepository());
  }

  public getMyBillContactListUseCase(): MyBillContactListUseCase {
    return new MyBillContactListUseCase(this.getBillContactRepository());
  }

  public getEditBillContactUseCase(): EditBillContactUseCase {
    return new EditBillContactUseCase(this.getBillContactRepository());
  }

  public getDeleteBillContactUseCase(): DeleteBillContactUseCase {
    return new DeleteBillContactUseCase(this.getBillContactRepository());
  }

  public getSaveBillContactUseCase(): SaveBillContactUseCase {
    return new SaveBillContactUseCase(this.getBillContactRepository());
  }

  public getPaymentOrderStatusUseCase(): PaymentOrderStatusUseCase {
    return new PaymentOrderStatusUseCase(this.getPaymentOrderRepository());
  }

  public getPaymentOrderUseCase(): PaymentOrderUseCase {
    return new PaymentOrderUseCase(this.getPaymentOrderRepository());
  }

  public getGetBillDetailUseCase(): GetBillDetailUseCase {
    return new GetBillDetailUseCase(this.getBillPayRepository());
  }

  public getBillValidateUseCase(): BillValidateUseCase {
    return new BillValidateUseCase(this.getBillPayRepository());
  }

  public getValidateUseCase(): ValidateUseCase {
    return new ValidateUseCase(this.getPaymentRepository());
  }

  public getMyBillListUseCase(): MyBillListUseCase {
    return new MyBillListUseCase(this.getBillPayRepository());
  }

  public getProviderListUseCase(): ProviderListUseCase {
    return new ProviderListUseCase(this.getBillPayRepository());
  }

  public getSourceAccountListUseCase(): SourceAccountListUseCase {
    return new SourceAccountListUseCase(this.getArrangementRepository());
  }

  public getProfileUseCase(): GetProfileUseCase {
    return new GetProfileUseCase(this.getCustomerRepository());
  }

  public getCategoryListUseCase(): CategoryListUseCase {
    return new CategoryListUseCase(this.getBillPayRepository());
  }

  public getValidateCustomerUseCase(): ValidateCustomerUseCase {
    return new ValidateCustomerUseCase(this.getCustomerRepository(), this.getArrangementRepository());
  }
}
