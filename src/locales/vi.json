{"welcome": "Chào mừng bạn đến với Super App tao la home", "greeting": "<PERSON>n ch<PERSON>o, bạn khỏe không?", "vnd": "VND", "code": "Mã: ", "edit": "Chỉnh sửa", "delete": "Xoá", "bottomTab": {"home": "Trang Chủ", "setting": "Cài đặt", "asset": "<PERSON><PERSON><PERSON>"}, "paymentHome": {"title": "Thanh toán & nạp tiền", "topup": "<PERSON><PERSON><PERSON> t<PERSON>", "billing": "<PERSON><PERSON> toán", "selectService": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "automaticRewards": "<PERSON><PERSON> toán tự động", "allCategory": "<PERSON><PERSON><PERSON> c<PERSON> dịch vụ"}, "billingTab": {"saved": "<PERSON><PERSON> l<PERSON>", "recent": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, mã kh<PERSON><PERSON> hàng", "titleSaved": "<PERSON><PERSON> l<PERSON>", "titleRecent": "<PERSON><PERSON><PERSON><PERSON>", "hintSearch": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, mã kh<PERSON><PERSON> hàng", "headerMyAccount": "<PERSON><PERSON><PERSON>n của tôi", "headerFavorite": "<PERSON><PERSON><PERSON>ch", "titleEmpty": "<PERSON><PERSON> s<PERSON>ch hoá đơn trống", "contentEmpty": "<PERSON><PERSON><PERSON> thông tin hóa đơn để thao tác thanh\ntoán nhanh và thuận tiện hơn", "titleEmptyContactSystemError": "<PERSON><PERSON> lỗi tải trang", "contentEmptyContactSystemError": "<PERSON><PERSON> lỗi không mong muốn xảy ra. <PERSON>u<PERSON> khách vui\n lòng thử lại.", "titleEmptyContactFiltered": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "contentEmptyContactFiltered": "<PERSON>ui lòng tìm với từ khoá khác", "titleEmptyRecent": "Chưa có dữ liệu", "contentEmptyRecent": "<PERSON><PERSON><PERSON> có hoá đơn thanh toán gần đây"}, "oops": "<PERSON><PERSON> gián đoạn tạm thời", "errorOccurred": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại sau, xin lỗi vì sự bất tiện này", "close": "Đ<PERSON><PERSON>", "components": {"providerList": {"searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "noResultsTitle": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noResultsMessage": "<PERSON>ui lòng tìm với từ khoá khác", "systemErrorTitle": "<PERSON><PERSON> thống gi<PERSON> đ<PERSON>", "systemErrorMessage": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại. Xin lỗi vì sự bất tiện này."}, "recentTab": {"searchPlaceholder": "<PERSON><PERSON><PERSON>, số tài <PERSON>n"}, "billDetail": {"separator": " • ", "statusUnpaid": "<PERSON><PERSON><PERSON> to<PERSON>", "statusPaid": "<PERSON><PERSON> thanh toán"}}, "screens": {"prepaidMobileInfo": {"insufficientBalance": "Số dư tài kho<PERSON>n không đủ "}, "postPaidMobileInfo": {"insufficientBalance": "Số dư tài kho<PERSON>n không đủ ", "billPeriod": "<PERSON><PERSON><PERSON>", "totalAmount": "<PERSON><PERSON><PERSON> tiền"}, "paymentResult": {"paymentPeriod": "<PERSON><PERSON> thanh toán"}}, "utils": {"formatUtils": {"noAmount": "<PERSON><PERSON><PERSON><PERSON> đồng", "currencyVnd": "<PERSON><PERSON><PERSON>", "currencyUsd": "USD", "amountSuggestion1": "100,000", "amountSuggestion2": "200,000", "amountSuggestion3": "500,000", "done": "<PERSON><PERSON>"}}, "paymentConfirm": {"title": "<PERSON><PERSON><PERSON><PERSON> tiền", "receiver": "<PERSON><PERSON><PERSON><PERSON>n", "sender": "<PERSON><PERSON><PERSON><PERSON>", "transferContent": "<PERSON><PERSON><PERSON> dung chuyển tiền", "endOfTransaction": "<PERSON><PERSON><PERSON> th<PERSON>c giao d<PERSON>ch", "transactionFee": "Phí (bao gồm VAT)", "paymentCode": "Mã giao d<PERSON>ch", "billPeriod": "<PERSON><PERSON> đ<PERSON>n", "paymentType": "<PERSON><PERSON><PERSON>", "endOfTransactionDescription": "<PERSON>u<PERSON> khách có muốn kết thúc giao dịch chuyển tiền?", "close": "Đ<PERSON><PERSON>", "transferSplitDetail": "<PERSON> tiết tách lệnh giao dịch", "transferConfirm": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "confirm": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON>i dung", "transferNotYetExecuted": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> ch<PERSON>a đ<PERSON><PERSON><PERSON> thực hi<PERSON>n", "doItLater": "<PERSON><PERSON><PERSON><PERSON> hiện sau", "updateNow": "<PERSON><PERSON><PERSON> nh<PERSON>t ngay", "haveTemporaryInterruption": "<PERSON><PERSON> gián đoạn tạm thời", "explainHaveTemporaryInterruption": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "common": {"accountNumber": "Số tà<PERSON>/ Số thẻ", "accountNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>/số thẻ", "inputName": "<PERSON><PERSON><PERSON>n", "inputNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "content": "<PERSON><PERSON>i dung", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung chuyển k<PERSON>n", "payment": "<PERSON><PERSON> toán", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "paymentAuto": "<PERSON><PERSON> toán tự động", "historyPayment": "<PERSON><PERSON><PERSON> sử thanh toán", "item": "ITEM", "promoCode": "<PERSON><PERSON> <PERSON><PERSON> đãi (<PERSON><PERSON><PERSON> c<PERSON>)", "inputPromoCode": "<PERSON><PERSON>n hoặc nhập mã ưu đãi", "selectAmount": "<PERSON><PERSON><PERSON> m<PERSON>nh giá", "transfer": "chuyen tien"}, "paymentBill": {"title": "<PERSON><PERSON> toán", "labelSelecboxProvider": "<PERSON><PERSON><PERSON> cung cấp", "labelBank": "<PERSON><PERSON><PERSON> cung cấp", "hintSelecboxProvider": "<PERSON><PERSON><PERSON> nhà cung cấp", "hintSearch": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "labelInputAccNumber": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng", "hintInputAccNumber": "<PERSON><PERSON><PERSON><PERSON> số hợp đồng/mã khách hàng", "hintInputPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "labelInputName": "<PERSON><PERSON><PERSON>n", "contentInputName": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "btnContinue": "<PERSON><PERSON><PERSON><PERSON>", "numberPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "prepaid": "<PERSON><PERSON><PERSON> trước", "postpaid": "Trả sau", "phonePrepaid": "<PERSON> động trả trước", "phonePostpaid": "<PERSON> động trả sau"}, "paymentResult": {"detail_transaction": "<PERSON> tiết giao dịch", "other_transaction": "<PERSON><PERSON><PERSON>", "transaction_success": "<PERSON><PERSON> toán hoá đơn thành công", "transaction_failed": "<PERSON><PERSON> toán hoá đơn thất bại", "paymentType": "<PERSON><PERSON><PERSON>", "receiver": "<PERSON><PERSON><PERSON><PERSON>n", "sender": "<PERSON><PERSON><PERSON><PERSON>", "transactionFee": "<PERSON><PERSON> giao d<PERSON>ch", "transferContent": "<PERSON><PERSON><PERSON> dung chuyển tiền", "transaction_in_progress": "<PERSON><PERSON> to<PERSON> hóa đơn đang xử lý", "transaction_in_progress_explain": "<PERSON><PERSON><PERSON> cầu <PERSON>h toán hóa đơn của quý khách đang được xử lý. <PERSON><PERSON> lòng không thực hiện lại giao dịch.", "transaction_in_progress_error": "<PERSON><PERSON> g<PERSON> đoạn tạm thời, chúng tôi đang khắc phục ngay. <PERSON>g quý khách thông cảm và thử lại.", "topup_error": "<PERSON><PERSON><PERSON> tiền thất bại", "topup_in_progress": "<PERSON><PERSON><PERSON> tiền đang xử lý", "topup_success": "<PERSON><PERSON><PERSON> tiền thành công", "address": "Địa chỉ kh<PERSON>ch hàng (trên hóa đơn)", "paymentRefCode": "<PERSON><PERSON> tham chiếu", "free": "<PERSON><PERSON><PERSON> phí"}, "paymentInfor": {"bill": "<PERSON><PERSON> đ<PERSON>n", "payment": "<PERSON><PERSON> toán", "transaction": "<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> tiền", "sourceAccount": "<PERSON><PERSON><PERSON><PERSON> tiền", "paymentInfoValidate": {"checkIBMB": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện giao dịch", "checkIBMBDescription": "<PERSON><PERSON><PERSON> khách đang sử dụng dịch vụ truy vấn (thuộc gói IBMB), v<PERSON> vậy không thể sử dụng tính năng này", "interruptionCitad": "<PERSON><PERSON><PERSON> thời gian giao dịch chuyển thường", "interruptionCitadDescription": "<PERSON>iao dịch chuyển thường chưa thể thực hiện được do ngoài giờ làm việc. Quý khách có thể đặt lịch xử lý giao dịch này vào ngày làm việc tiếp theo.", "scheduleTransfer": "Đặt lịch giao dịch", "napasInterrupInWorkingTime": "<PERSON><PERSON> hàng người nhận tạm thời gián đo<PERSON>n", "napasInterrupInWorkingTimeDescription": "Giao dịch không thể chuyển khoản nhanh 24/7 do ngân hàng người nhận tạm thời gián đoạn. Qu<PERSON> khách vui lòng thử lại hoặc sử dụng dịch vụ chuyển thường để tiếp tục giao dịch.", "napasInterrupOutWorkingTime": "<PERSON><PERSON> hàng người nhận tạm thời gián đo<PERSON>n", "napasInterrupOutWorkingTimeDescription": "Giao dịch không thể chuyển nhanh 24/7 do ngân hàng người nhận tạm thời không hỗ trợ. Quý khách vui lòng thử lại hoặc sử dụng dịch vụ chuyển thường để thực hiện giao dịch này vào ngày làm việc tiếp theo.", "duplicateSourceAccount": "Tài khoản người nhận trùng tài khoản nguồn", "duplicateSourceAccountDescription": "<PERSON><PERSON><PERSON> dị<PERSON> không thể thực hiện do quý khách đã chọn tài khoản người nhận trùng với tài khoản nguồn. Vui lòng kiểm tra và thử lại.", "minAmountNapas": "<PERSON><PERSON> tiền giao dịch phải lớn hơn hoặc bằng 3,000 VND", "availableBalance": "Số tiền giao dịch v<PERSON><PERSON><PERSON> quá số dư tài khoản nguồn"}, "userNotValid": "<PERSON><PERSON><PERSON><PERSON> nhận không hợp lệ", "userNotValidDescription": "<PERSON><PERSON><PERSON> khoản người nhận không hợp lệ. <PERSON><PERSON><PERSON> khách vui lòng kiểm tra và thực hiện lại", "userNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ng<PERSON><PERSON> nh<PERSON>n", "userNotFoundDescription": "<PERSON><PERSON> thống không tìm thấy thông tin người nhận. <PERSON><PERSON><PERSON> khách vui lòng kiểm tra lại.", "biometricAuthentication": "<PERSON> quy đ<PERSON>nh của <PERSON> hàng <PERSON>ư<PERSON>, qu<PERSON> kh<PERSON>ch cần thu thập thông tin sinh trắc học theo thông tin căn cước công dân mới nhất.", "identification": "G<PERSON><PERSON>y tờ tùy thân của quý khách đã hết hạn. <PERSON><PERSON> lòng cập nhật để tiếp tục giao dịch.", "paymentInfoValidateTitle": "<PERSON><PERSON> tiền giao d<PERSON>ch không hợp lệ", "paymentInfoValidateDescription": "Số tiền quý khách vừa nhập lớn hơn hạn mức giao dịch còn lại trong ngày là {{value}} VND. Vui lòng điều chỉnh lại số tiền hoặc cài đặt lại hạn mức giao dịch."}, "qrPaymentInfo": {"payment": "<PERSON><PERSON> toán", "merchantName": "<PERSON><PERSON><PERSON><PERSON> bán/ dịch vụ", "amountPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tiền", "amount": "<PERSON><PERSON> tiền"}, "editContact": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t ho<PERSON> đơn", "beneficiary_bank": "<PERSON><PERSON><PERSON> cung cấp", "select_bank": "<PERSON><PERSON><PERSON> nhà cung cấp", "selectBankBranch": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "account_number": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng", "enter_account_number": "<PERSON><PERSON><PERSON><PERSON> số hợp đồng/<PERSON><PERSON> khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "name_alias": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> (Không bắt buộc)", "enter_content": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "add_to_favourite": "<PERSON><PERSON><PERSON><PERSON> vào danh sách yêu thích", "lunch": "An trua", "dinner": "An toi", "cafe": "Cafe", "amount": "<PERSON><PERSON> tiền"}, "addContact": {"title": "<PERSON><PERSON><PERSON> đơn", "labelSelecboxBank": "<PERSON><PERSON><PERSON> cung cấp", "hintSelecboxBank": "<PERSON><PERSON><PERSON> nhà cung cấp", "labelInputAccNumber": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng", "hintInputAccNumber": "<PERSON><PERSON><PERSON><PERSON> số hợp đồng/<PERSON><PERSON> khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "contentInputName": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "labelAlias": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> (Không bắt buộc)", "contentAlias": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "enableAutomatic": "<PERSON><PERSON><PERSON> ký thanh toán tự động", "btnContinue": "<PERSON><PERSON><PERSON><PERSON>", "bank": "<PERSON><PERSON> h<PERSON>", "toastSuccess": "<PERSON><PERSON><PERSON><PERSON> hoá đơn thành công", "toastError": "<PERSON>h<PERSON><PERSON> hoá đơn không thành công"}, "error": {"oops": "<PERSON><PERSON> gián đoạn tạm thời", "errorOccurred": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại sau, xin lỗi vì sự bất tiện này", "transactionFailed": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện giao dịch", "endOfTransaction": "<PERSON><PERSON><PERSON> th<PERSON>c giao d<PERSON>ch", "ibmbFailed": "<PERSON><PERSON><PERSON> khách đang sử dụng dịch vụ truy vấn (thuộc gói IBMB), v<PERSON> vậy không thể sử dụng tính năng này", "sourceAccountNotFound": "<PERSON><PERSON><PERSON> kh<PERSON>ch cần có tài khoản thanh toán đang hoạt động để sử dụng tính năng này. Vui lòng kiểm tra lại hoặc mở thêm tài.", "informationNotValid": "Thông tin không hợp lệ", "contractNotValid": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng không hợp lệ. Qu<PERSON> khách vui lòng kiểm tra lại.", "contractNoDebt": "<PERSON><PERSON><PERSON> đơn không nợ cước", "contractNotDebtMessage": "<PERSON><PERSON><PERSON> đơn của quý khách đã đư<PERSON><PERSON> thanh toán. <PERSON>ui lòng kiểm tra lại.", "telcoNotValid": "Thông tin nhà mạng không hợp lệ", "telcoNotValidMessage": "Thông tin nhà mạng của quý khách không hợp lệ. <PERSON>ui lòng kiểm tra lại.", "phoneNotValid": "<PERSON><PERSON><PERSON> bao không tồn tại", "phoneNotValidMessage": "<PERSON><PERSON> điện thoại của quý khách không phải thuê bao trả sau. <PERSON><PERSON> lòng kiểm tra lại", "phoneViettelDebt": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON>", "phoneViettelDebtMessage": "Do quy định của NCCDV Viettel về việc không hiển thị thông tin cước nợ trả sau. Quý khách vui lòng nhấn “Tiế<PERSON> tục” để thanh toán toàn bộ cước nợ.", "paidErrorBilling": "<PERSON><PERSON> g<PERSON> đoạn tạm thời, chúng tôi đang khắc phục ngay. <PERSON>g quý khách thông cảm và thử lại.", "validation": {"invalidData": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "errorPhone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}, "network": {"title": "<PERSON><PERSON> lỗi xảy ra", "content": "<PERSON><PERSON> lòng kiểm tra lại kết nối mạng của bạn"}, "server": {"title": "<PERSON><PERSON> lỗi xảy ra", "content": "<PERSON><PERSON> lòng thử lại sau"}, "timeout": {"title": "<PERSON><PERSON> lỗi xảy ra", "content": "<PERSON>h<PERSON>i gian chờ đã hết. <PERSON><PERSON> lòng thử lại"}, "action": {"retry": "<PERSON><PERSON> lỗi xảy ra", "contact": "<PERSON>h<PERSON>i gian chờ đã hết. <PERSON><PERSON> lòng thử lại", "cancel": "Huỷ bỏ", "close": "Đ<PERSON><PERSON>", "back": "Quay lại", "home": "Trang chủ"}}}