{"welcome": "Chào mừng bạn đến với Super App tao la home", "greeting": "<PERSON>n ch<PERSON>o, bạn khỏe không?", "code": "Code: ", "vnd": "VND", "edit": "Chỉnh sửa", "delete": "Xoá", "bottomTab": {"home": "Trang Chủ", "setting": "Cài đặt", "asset": "<PERSON><PERSON><PERSON>"}, "paymentHome": {"title": "<PERSON><PERSON> toán và nạp tiền", "topup": "<PERSON><PERSON><PERSON> t<PERSON>", "billing": "<PERSON><PERSON> toán", "selectService": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "automaticRewards": "<PERSON><PERSON> toán tự động", "allCategory": "<PERSON><PERSON><PERSON> c<PERSON> dịch vụ"}, "billingTab": {"saved": "<PERSON><PERSON> l<PERSON>", "recent": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, mã kh<PERSON><PERSON> hàng", "titleSaved": "<PERSON><PERSON> l<PERSON>", "titleRecent": "<PERSON><PERSON><PERSON><PERSON>", "hintSearch": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "headerMyAccount": "<PERSON><PERSON><PERSON>n của tôi", "headerFavorite": "<PERSON><PERSON><PERSON>ch", "titleEmpty": "<PERSON><PERSON> s<PERSON>ch hoá đơn trống", "contentEmpty": "<PERSON><PERSON><PERSON> thông tin hóa đơn để thao tác thanh\ntoán nhanh và thuận tiện hơn", "titleEmptyContactSystemError": "<PERSON><PERSON> lỗi tải trang", "contentEmptyContactSystemError": "<PERSON><PERSON> lỗi không mong muốn xảy ra. <PERSON>u<PERSON> khách vui\n lòng thử lại.", "titleEmptyContactFiltered": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "contentEmptyContactFiltered": "<PERSON>ui lòng tìm với từ khoá khác", "titleEmptyRecent": "Chưa có dữ liệu", "contentEmptyRecent": "<PERSON><PERSON><PERSON> có hoá đơn thanh toán gần đây"}, "oops": "<PERSON><PERSON> gián đoạn tạm thời", "errorOccurred": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại sau, xin lỗi vì sự bất tiện này", "close": "Đ<PERSON><PERSON>", "components": {"providerList": {"searchPlaceholder": "Enter content", "noResultsTitle": "No results found", "noResultsMessage": "Please search with different keywords", "systemErrorTitle": "System interrupted", "systemErrorMessage": "Please try again. Sorry for the inconvenience."}, "recentTab": {"searchPlaceholder": "Name, account number"}, "billDetail": {"separator": " • ", "statusUnpaid": "Unpaid", "statusPaid": "Paid"}}, "screens": {"prepaidMobileInfo": {"insufficientBalance": "Insufficient account balance "}, "postPaidMobileInfo": {"insufficientBalance": "Insufficient account balance ", "billPeriod": "Bill", "totalAmount": "Total amount"}, "paymentResult": {"paymentPeriod": "Payment period"}}, "utils": {"formatUtils": {"noAmount": "Zero dong", "currencyVnd": "dong", "currencyUsd": "USD", "amountSuggestion1": "100,000", "amountSuggestion2": "200,000", "amountSuggestion3": "500,000", "done": "Done"}}, "paymentConfirm": {"title": "<PERSON><PERSON><PERSON><PERSON> tiền", "receiver": "<PERSON><PERSON><PERSON><PERSON>n", "sender": "<PERSON><PERSON><PERSON><PERSON>", "transferContent": "<PERSON><PERSON><PERSON> dung chuyển tiền", "endOfTransaction": "<PERSON><PERSON><PERSON> th<PERSON>c giao d<PERSON>ch", "transactionFee": "Phí (bao gồm VAT)", "paymentCode": "Mã giao d<PERSON>ch", "billPeriod": "<PERSON><PERSON> đ<PERSON>n", "paymentType": "<PERSON><PERSON><PERSON>", "endOfTransactionDescription": "<PERSON>u<PERSON> khách có muốn kết thúc giao dịch chuyển tiền?", "close": "Đ<PERSON><PERSON>", "transferSplitDetail": "<PERSON> tiết tách lệnh giao dịch", "transferConfirm": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "confirm": "<PERSON><PERSON><PERSON>"}, "common": {"accountNumber": "Số tà<PERSON>/ Số thẻ", "accountNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>/số thẻ", "inputName": "<PERSON><PERSON><PERSON>n", "inputNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "content": "<PERSON><PERSON>i dung", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung chuyển k<PERSON>n", "payment": "<PERSON><PERSON> toán", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "paymentAuto": "<PERSON><PERSON> toán tự động", "historyPayment": "<PERSON><PERSON><PERSON> sử thanh toán", "item": "ITEM", "promoCode": "<PERSON><PERSON> <PERSON><PERSON> đãi (<PERSON><PERSON><PERSON> c<PERSON>)", "inputPromoCode": "<PERSON><PERSON>n hoặc nhập mã ưu đãi", "selectAmount": "<PERSON><PERSON><PERSON> m<PERSON>nh giá"}, "paymentBill": {"title": "<PERSON><PERSON> toán", "labelSelecboxProvider": "<PERSON><PERSON><PERSON> cung cấp", "labelBank": "<PERSON><PERSON><PERSON> cung cấp", "hintSelecboxProvider": "<PERSON><PERSON><PERSON> nhà cung cấp", "hintSearch": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "labelInputAccNumber": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng", "hintInputAccNumber": "<PERSON><PERSON><PERSON><PERSON> số hợp đồng/mã khách hàng", "hintInputPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "labelInputName": "<PERSON><PERSON><PERSON>n", "contentInputName": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "btnContinue": "<PERSON><PERSON><PERSON><PERSON>", "numberPhone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "prepaid": "<PERSON><PERSON><PERSON> trước", "postpaid": "Trả sau", "phonePrepaid": "<PERSON> động trả trước", "phonePostpaid": "<PERSON> động trả sau"}, "paymentResult": {"detail_transaction": "<PERSON> tiết giao dịch", "other_transaction": "<PERSON><PERSON><PERSON>", "transaction_success": "<PERSON><PERSON> toán hoá đơn thành công", "transaction_failed": "<PERSON><PERSON> toán hoá đơn thất bại", "paymentType": "<PERSON><PERSON><PERSON>", "receiver": "<PERSON><PERSON><PERSON><PERSON>n", "sender": "<PERSON><PERSON><PERSON><PERSON>", "transactionFee": "<PERSON><PERSON> giao d<PERSON>ch", "transferContent": "<PERSON><PERSON><PERSON> dung chuyển tiền", "transaction_in_progress": "<PERSON><PERSON> to<PERSON> hóa đơn đang xử lý", "transaction_in_progress_explain": "<PERSON><PERSON><PERSON> cầu <PERSON>h toán hóa đơn của quý khách đang được xử lý. <PERSON><PERSON> lòng không thực hiện lại giao dịch.", "transaction_in_progress_error": "<PERSON><PERSON> g<PERSON> đoạn tạm thời, chúng tôi đang khắc phục ngay. <PERSON>g quý khách thông cảm và thử lại.", "topup_error": "<PERSON><PERSON><PERSON> tiền thất bại", "topup_in_progress": "<PERSON><PERSON><PERSON> tiền đang xử lý", "topup_success": "<PERSON><PERSON><PERSON> tiền thành công", "address": "Địa chỉ kh<PERSON>ch hàng (trên hóa đơn)", "paymentRefCode": "<PERSON><PERSON> tham chiếu", "free": "<PERSON><PERSON><PERSON> phí"}, "paymentInfor": {"bill": "<PERSON><PERSON> đ<PERSON>n", "payment": "<PERSON><PERSON> toán", "transaction": "<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> tiền", "sourceAccount": "<PERSON><PERSON><PERSON><PERSON> tiền"}, "editContact": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t ho<PERSON> đơn", "beneficiary_bank": "<PERSON><PERSON><PERSON> cung cấp", "select_bank": "<PERSON><PERSON><PERSON> nhà cung cấp", "selectBankBranch": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "account_number": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng", "enter_account_number": "<PERSON><PERSON><PERSON><PERSON> số hợp đồng/<PERSON><PERSON> khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "name_alias": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> (Không bắt buộc)", "enter_content": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "add_to_favourite": "<PERSON><PERSON><PERSON><PERSON> vào danh sách yêu thích", "lunch": "An trua", "dinner": "An toi", "cafe": "Cafe", "amount": "<PERSON><PERSON> tiền"}, "addContact": {"title": "<PERSON><PERSON><PERSON> đơn", "labelSelecboxBank": "<PERSON><PERSON><PERSON> cung cấp", "hintSelecboxBank": "<PERSON><PERSON><PERSON> nhà cung cấp", "labelInputAccNumber": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng", "hintInputAccNumber": "<PERSON><PERSON><PERSON><PERSON> số hợp đồng/<PERSON><PERSON> khách hàng", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "contentInputName": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> nhận", "labelAlias": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> (Không bắt buộc)", "contentAlias": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> dung", "enableAutomatic": "<PERSON><PERSON><PERSON> ký thanh toán tự động", "btnContinue": "<PERSON><PERSON><PERSON><PERSON>", "bank": "<PERSON><PERSON> h<PERSON>", "toastSuccess": "<PERSON><PERSON><PERSON><PERSON> hoá đơn thành công", "toastError": "<PERSON>h<PERSON><PERSON> hoá đơn không thành công"}, "error": {"oops": "<PERSON><PERSON> gián đoạn tạm thời (en)", "errorOccurred": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại sau, xin lỗi vì sự bất tiện này (en)", "transactionFailed": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện giao d<PERSON> (en)", "endOfTransaction": "<PERSON><PERSON><PERSON> th<PERSON>c giao d<PERSON> (en)", "ibmbFailed": "<PERSON><PERSON><PERSON> khách đang sử dụng dịch vụ truy vấn (thuộc gói IBMB), v<PERSON> vậy không thể sử dụng tính năng này (en)", "sourceAccountNotFound": "<PERSON><PERSON><PERSON> kh<PERSON>ch cần có tài khoản thanh toán đang hoạt động để sử dụng tính năng này. Vui lòng kiểm tra lại hoặc mở thêm tài khoản thanh toán. (en)", "informationNotValid": "<PERSON>hông tin không hợp lệ (en)", "contractNotValid": "<PERSON><PERSON> hợp đồng/<PERSON><PERSON> khách hàng không hợp lệ. <PERSON>u<PERSON> khách vui lòng kiểm tra lại. (en)", "contractNoDebt": "<PERSON><PERSON><PERSON> đơn không nợ cước (en)", "contractNotDebtMessage": "<PERSON><PERSON><PERSON> đơn của quý khách đã đư<PERSON><PERSON> thanh toán. <PERSON>ui lòng kiểm tra lại. (en)", "telcoNotValid": "Thông tin nhà mạng không hợp lệ (en)", "telcoNotValidMessage": "Thông tin nhà mạng của quý khách không hợp lệ. <PERSON>ui lòng kiểm tra lại. (en)", "phoneNotValid": "<PERSON><PERSON><PERSON> bao không tồn tại (en)", "phoneNotValidMessage": "<PERSON><PERSON> điện thoại của quý khách không phải thuê bao trả sau. <PERSON><PERSON> lòng kiểm tra lại (en)", "phoneViettelDebt": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON> (en)", "phoneViettelDebtMessage": "Do quy định của NCCDV Viettel về việc không hiển thị thông tin cước nợ trả sau. Quý khách vui lòng nhấn “Tiế<PERSON> tục” để thanh toán toàn bộ cước nợ. (en)", "paidErrorBilling": "<PERSON><PERSON> g<PERSON> đoạn tạm thời, chúng tôi đang khắc phục ngay. <PERSON>g quý khách thông cảm và thử lại.", "validation": {"invalidData": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "errorPhone": "<PERSON><PERSON> điện tho<PERSON><PERSON> không hợp lệ"}, "network": {"title": "<PERSON><PERSON> lỗi xảy ra", "content": "<PERSON><PERSON> lòng kiểm tra lại kết nối mạng của bạn"}, "server": {"title": "<PERSON><PERSON> lỗi xảy ra", "content": "<PERSON><PERSON> lòng thử lại sau"}, "timeout": {"title": "<PERSON><PERSON> lỗi xảy ra", "content": "<PERSON>h<PERSON>i gian chờ đã hết. <PERSON><PERSON> lòng thử lại"}, "action": {"retry": "<PERSON><PERSON> lỗi xảy ra", "contact": "<PERSON>h<PERSON>i gian chờ đã hết. <PERSON><PERSON> lòng thử lại", "cancel": "Huỷ bỏ", "close": "Đ<PERSON><PERSON>", "back": "Quay lại", "home": "Trang chủ"}}}