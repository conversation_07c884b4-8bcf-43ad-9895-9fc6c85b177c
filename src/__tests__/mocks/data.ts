/**
 * Data layer mock data
 * Mock data for repositories, data sources, and API responses
 */

import { BaseResponse } from '@/core/BaseResponse';

// ============================================================================
// API RESPONSE MOCK DATA
// ============================================================================

export const mockApiSuccessResponse = <T>(data: T): BaseResponse<T> => ({
  ...data,
  errors: null,
});

export const mockApiErrorResponse = (errorKey: string, message: string): BaseResponse<any> => ({
  errors: [{ key: errorKey, message }],
});

// ============================================================================
// BILL CONTACT API RESPONSES
// ============================================================================

export const mockSaveBillContactApiResponse = mockApiSuccessResponse({
  contactId: 'CONTACT_123',
  success: true,
  message: 'Contact saved successfully',
});

export const mockDeleteBillContactApiResponse = mockApiSuccessResponse({
  success: true,
  message: 'Contact deleted successfully',
});

export const mockEditBillContactApiResponse = mockApiSuccessResponse({
  contactId: 'CONTACT_123',
  success: true,
  message: 'Contact updated successfully',
});

export const mockMyBillContactListApiResponse = mockApiSuccessResponse({
  contacts: [
    {
      id: 'CONTACT_123',
      name: 'Electric Bill',
      accountNumber: '**********',
      provider: 'EVN HCMC',
      category: 'ELECTRICITY',
      createdAt: '2024-01-15T10:00:00Z',
    },
    {
      id: 'CONTACT_456',
      name: 'Water Bill',
      accountNumber: '**********',
      provider: 'SAWACO',
      category: 'WATER',
      createdAt: '2024-01-14T09:00:00Z',
    },
  ],
  totalCount: 2,
  page: 1,
  limit: 20,
});

export const mockRecentContactListApiResponse = mockApiSuccessResponse({
  contacts: [
    {
      id: 'CONTACT_123',
      name: 'Electric Bill',
      accountNumber: '**********',
      provider: 'EVN HCMC',
      category: 'ELECTRICITY',
      lastUsed: '2024-01-15T14:30:00Z',
    },
  ],
  hasMore: false,
});

export const mockBillHistoryApiResponse = mockApiSuccessResponse({
  transactions: [
    {
      id: 'TXN_001',
      accountNumber: '**********',
      amount: 150000,
      fee: 5000,
      status: 'SUCCESS',
      transactionDate: '2024-01-15T14:30:00Z',
      provider: 'EVN HCMC',
      referenceNumber: 'REF_001',
    },
  ],
  totalCount: 1,
  page: 1,
  limit: 20,
});

// ============================================================================
// CATEGORY API RESPONSES
// ============================================================================

export const mockCategoryListApiResponse = mockApiSuccessResponse({
  categories: [
    {
      id: 'CAT_001',
      name: 'Electricity',
      code: 'ELECTRICITY',
      icon: 'electricity-icon',
      sortOrder: 1,
    },
    {
      id: 'CAT_002',
      name: 'Water',
      code: 'WATER',
      icon: 'water-icon',
      sortOrder: 2,
    },
  ],
});

// ============================================================================
// PAYMENT API RESPONSES
// ============================================================================

export const mockPaymentValidateApiResponse = mockApiSuccessResponse({
  isValid: true,
  transactionId: 'TXN_VALIDATE_001',
  fee: 5000,
  exchangeRate: 1,
  estimatedTime: '2-3 minutes',
});

export const mockPaymentValidateErrorApiResponse = mockApiErrorResponse(
  'A05',
  'Account not found or invalid'
);

// ============================================================================
// DATA SOURCE MOCK IMPLEMENTATIONS
// ============================================================================

export const createMockBillContactDataSource = () => ({
  saveBillContact: jest.fn().mockResolvedValue(mockSaveBillContactApiResponse),
  deleteBillContact: jest.fn().mockResolvedValue(mockDeleteBillContactApiResponse),
  editBillContact: jest.fn().mockResolvedValue(mockEditBillContactApiResponse),
  myBillContactList: jest.fn().mockResolvedValue(mockMyBillContactListApiResponse),
  getMyBillContactRecentList: jest.fn().mockResolvedValue(mockRecentContactListApiResponse),
  getMyBillHistoryList: jest.fn().mockResolvedValue(mockBillHistoryApiResponse),
});

export const createMockBillPayDataSource = () => ({
  categoryList: jest.fn().mockResolvedValue(mockCategoryListApiResponse),
  providerList: jest.fn().mockResolvedValue(mockApiSuccessResponse({ providers: [] })),
  billValidate: jest.fn().mockResolvedValue(mockApiSuccessResponse({ isValid: true })),
  getBillDetail: jest.fn().mockResolvedValue(mockApiSuccessResponse({ billInfo: {} })),
  myBillList: jest.fn().mockResolvedValue(mockApiSuccessResponse({ bills: [] })),
});

export const createMockPaymentDataSource = () => ({
  validate: jest.fn().mockResolvedValue(mockPaymentValidateApiResponse),
});

// ============================================================================
// REPOSITORY MOCK IMPLEMENTATIONS
// ============================================================================

export const createMockBillContactRepositoryWithData = () => ({
  saveBillContact: jest.fn().mockResolvedValue({
    status: 'SUCCESS',
    data: { contactId: 'CONTACT_123', success: true },
  }),
  deleteBillContact: jest.fn().mockResolvedValue({
    status: 'SUCCESS',
    data: { success: true },
  }),
  editBillContact: jest.fn().mockResolvedValue({
    status: 'SUCCESS',
    data: { contactId: 'CONTACT_123', success: true },
  }),
  myBillContactList: jest.fn().mockResolvedValue({
    status: 'SUCCESS',
    data: { contacts: [], totalCount: 0 },
  }),
  getMyBillContactRecentList: jest.fn().mockResolvedValue({
    status: 'SUCCESS',
    data: { contacts: [], hasMore: false },
  }),
  getMyBillHistoryList: jest.fn().mockResolvedValue({
    status: 'SUCCESS',
    data: { transactions: [], totalCount: 0 },
  }),
});

// ============================================================================
// HTTP CLIENT MOCK
// ============================================================================

export const createMockHttpClient = () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
});

// ============================================================================
// MAPPER MOCK DATA
// ============================================================================

export const mockMapperInputData = {
  billContact: {
    id: 'RAW_123',
    contact_name: 'raw_electric_bill',
    account_no: '**********',
    provider_name: 'EVN HCMC',
    bill_category: 'ELECTRICITY',
    created_time: '2024-01-15T10:00:00Z',
  },
  category: {
    category_id: 'RAW_CAT_001',
    category_name: 'Electricity',
    category_code: 'ELECTRICITY',
    icon_url: 'electricity-icon',
    display_order: 1,
  },
  payment: {
    transaction_id: 'RAW_TXN_001',
    account_number: '**********',
    payment_amount: 150000,
    transaction_fee: 5000,
    payment_status: 'SUCCESS',
    created_date: '2024-01-15T14:30:00Z',
  },
};

export const mockMapperOutputData = {
  billContact: {
    id: 'RAW_123',
    name: 'raw_electric_bill',
    accountNumber: '**********',
    provider: 'EVN HCMC',
    category: 'ELECTRICITY',
    createdAt: '2024-01-15T10:00:00Z',
  },
  category: {
    id: 'RAW_CAT_001',
    name: 'Electricity',
    code: 'ELECTRICITY',
    icon: 'electricity-icon',
    sortOrder: 1,
  },
  payment: {
    id: 'RAW_TXN_001',
    accountNumber: '**********',
    amount: 150000,
    fee: 5000,
    status: 'SUCCESS',
    transactionDate: '2024-01-15T14:30:00Z',
  },
};

// ============================================================================
// ERROR SCENARIOS
// ============================================================================

export const mockDataSourceErrors = {
  networkError: mockApiErrorResponse('NET0001', 'Network connection failed'),
  validationError: mockApiErrorResponse('PIS0101', 'Validation failed'),
  systemError: mockApiErrorResponse('SYS0001', 'System error occurred'),
  notFoundError: mockApiErrorResponse('A05', 'Resource not found'),
};
