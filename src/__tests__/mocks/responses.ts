/**
 * Mock responses for testing
 * Centralized mock data for all test files
 */

import { BaseResponse } from '@/core/BaseResponse';
import { MSBError } from '@/core/MSBErrorCode';

// ============================================================================
// CORE MOCK RESPONSES
// ============================================================================

export const mockSuccessResponse = <T>(data: T): BaseResponse<T> => ({
  ...data,
  errors: null,
});

export const mockErrorResponse = (errorKey: string = 'PIS0101'): BaseResponse<any> => ({
  errors: [{ key: errorKey, message: 'Test error message' }],
});

export const mockMSBError: MSBError = {
  key: 'PIS0101',
  message: 'Test error message',
};

// ============================================================================
// BILL CONTACT MOCK DATA
// ============================================================================

export const mockBillContactData = {
  id: '123',
  name: 'Test Contact',
  accountNumber: '**********',
  provider: 'Test Provider',
  category: 'ELECTRICITY',
};

export const mockBillContactListResponse = mockSuccessResponse({
  contacts: [mockBillContactData],
  totalCount: 1,
});

export const mockSaveBillContactResponse = mockSuccessResponse({
  contactId: '123',
  success: true,
});

export const mockDeleteBillContactResponse = mockSuccessResponse({
  success: true,
});

export const mockEditBillContactResponse = mockSuccessResponse({
  contactId: '123',
  success: true,
});

export const mockRecentContactListResponse = mockSuccessResponse({
  contacts: [mockBillContactData],
  hasMore: false,
});

export const mockBillHistoryResponse = mockSuccessResponse({
  transactions: [
    {
      id: 'TXN_001',
      amount: 100000,
      date: '2024-01-15',
      status: 'SUCCESS',
    },
  ],
  totalCount: 1,
});

// ============================================================================
// CATEGORY LIST MOCK DATA
// ============================================================================

export const mockCategoryData = {
  id: 'CAT_001',
  name: 'Electricity',
  code: 'ELECTRICITY',
  icon: 'electricity-icon',
};

export const mockCategoryListResponse = mockSuccessResponse({
  categories: [mockCategoryData],
});

// ============================================================================
// PAYMENT VALIDATION MOCK DATA
// ============================================================================

export const mockValidateRequest = {
  accountNumber: '**********',
  amount: 100000,
  currency: 'VND',
  paymentType: 'TRANSFER' as const,
};

export const mockValidateResponse = mockSuccessResponse({
  isValid: true,
  transactionId: 'TXN_001',
  fee: 0,
});

export const mockValidateErrorResponse = mockErrorResponse('A05');

// ============================================================================
// EXECUTION HANDLER MOCK DATA
// ============================================================================

export const mockExecutionSuccessResult = {
  status: 'SUCCESS' as const,
  data: { success: true },
};

export const mockExecutionErrorResult = {
  status: 'ERROR' as const,
  error: {
    code: 'PIS0101',
    message: 'Test error',
    title: 'Error Title',
    category: 'SYSTEM' as const,
  },
};

// ============================================================================
// HANDLE DATA MOCK RESPONSES
// ============================================================================

export const mockHandleDataSuccessResponse = mockSuccessResponse({
  data: 'test data',
});

export const mockHandleDataErrorResponse = mockErrorResponse('PIS0106');

export const mockHandleDataNullResponse = null;

// ============================================================================
// REPOSITORY MOCK FUNCTIONS
// ============================================================================

export const createMockRepository = () => ({
  // Bill Contact Repository
  saveBillContact: jest.fn(),
  deleteBillContact: jest.fn(),
  editBillContact: jest.fn(),
  myBillContactList: jest.fn(),
  getMyBillContactRecentList: jest.fn(),
  getMyBillHistoryList: jest.fn(),
  
  // Category Repository
  categoryList: jest.fn(),
  
  // Payment Repository
  validate: jest.fn(),
});

// ============================================================================
// DATA SOURCE MOCK FUNCTIONS
// ============================================================================

export const createMockDataSource = () => ({
  // Bill Contact DataSource
  saveBillContact: jest.fn(),
  deleteBillContact: jest.fn(),
  editBillContact: jest.fn(),
  myBillContactList: jest.fn(),
  getMyBillContactRecentList: jest.fn(),
  getMyBillHistoryList: jest.fn(),
  
  // Category DataSource
  categoryList: jest.fn(),
  
  // Payment DataSource
  validate: jest.fn(),
});

// ============================================================================
// EXECUTION HANDLER MOCK
// ============================================================================

export const createMockExecutionHandler = () => jest.fn();

// ============================================================================
// HOST SHARED MODULE MOCK
// ============================================================================

export const mockHostSharedModule = {
  d: {
    domainService: {
      addSpinnerRequest: jest.fn(),
      addSpinnerCompleted: jest.fn(),
      showPopup: jest.fn(),
    },
  },
};
