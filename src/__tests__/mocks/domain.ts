/**
 * Domain layer mock data
 * Mock data for entities, use cases, and domain models
 */

// ============================================================================
// BILL CONTACT DOMAIN MODELS
// ============================================================================

export const mockBillContactEntity = {
  id: '123',
  name: 'Electric Bill Contact',
  accountNumber: '**********',
  provider: 'EVN HCMC',
  category: 'ELECTRICITY',
  isActive: true,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
};

export const mockBillContactList = [
  mockBillContactEntity,
  {
    id: '456',
    name: 'Water Bill Contact',
    accountNumber: '**********',
    provider: 'SAWACO',
    category: 'WATER',
    isActive: true,
    createdAt: '2024-01-14T09:00:00Z',
    updatedAt: '2024-01-14T09:00:00Z',
  },
];

// ============================================================================
// CATEGORY DOMAIN MODELS
// ============================================================================

export const mockCategoryEntity = {
  id: 'CAT_001',
  name: 'Electricity',
  code: 'ELECTRICITY',
  icon: 'electricity-icon',
  description: 'Electric utility bills',
  isActive: true,
  sortOrder: 1,
};

export const mockCategoryList = [
  mockCategoryEntity,
  {
    id: 'CAT_002',
    name: 'Water',
    code: 'WATER',
    icon: 'water-icon',
    description: 'Water utility bills',
    isActive: true,
    sortOrder: 2,
  },
  {
    id: 'CAT_003',
    name: 'Internet',
    code: 'INTERNET',
    icon: 'internet-icon',
    description: 'Internet service bills',
    isActive: true,
    sortOrder: 3,
  },
];

// ============================================================================
// PAYMENT DOMAIN MODELS
// ============================================================================

export const mockPaymentValidationRequest = {
  accountNumber: '**********',
  amount: 100000,
  currency: 'VND',
  paymentType: 'TRANSFER' as const,
  description: 'Electric bill payment',
  sourceAccount: 'ACC_001',
};

export const mockPaymentValidationResult = {
  isValid: true,
  transactionId: 'TXN_001',
  fee: 5000,
  exchangeRate: 1,
  estimatedTime: '2-3 minutes',
  warnings: [],
};

export const mockPaymentValidationError = {
  isValid: false,
  errors: ['Insufficient balance', 'Invalid account number'],
  transactionId: null,
  fee: 0,
};

// ============================================================================
// BILL HISTORY DOMAIN MODELS
// ============================================================================

export const mockBillTransaction = {
  id: 'TXN_001',
  accountNumber: '**********',
  amount: 150000,
  fee: 5000,
  currency: 'VND',
  status: 'SUCCESS',
  transactionDate: '2024-01-15T14:30:00Z',
  description: 'Electric bill payment',
  provider: 'EVN HCMC',
  category: 'ELECTRICITY',
  referenceNumber: 'REF_001',
};

export const mockBillHistoryList = [
  mockBillTransaction,
  {
    id: 'TXN_002',
    accountNumber: '**********',
    amount: 80000,
    fee: 3000,
    currency: 'VND',
    status: 'SUCCESS',
    transactionDate: '2024-01-14T16:45:00Z',
    description: 'Water bill payment',
    provider: 'SAWACO',
    category: 'WATER',
    referenceNumber: 'REF_002',
  },
];

// ============================================================================
// USE CASE REQUEST/RESPONSE MODELS
// ============================================================================

export const mockSaveBillContactRequest = {
  name: 'New Electric Contact',
  accountNumber: '**********',
  provider: 'EVN HCMC',
  category: 'ELECTRICITY',
  description: 'Home electric bill',
};

export const mockEditBillContactRequest = {
  id: '123',
  name: 'Updated Electric Contact',
  accountNumber: '**********',
  provider: 'EVN HCMC',
  category: 'ELECTRICITY',
  description: 'Updated home electric bill',
};

export const mockDeleteBillContactRequest = {
  id: '123',
};

export const mockGetBillHistoryRequest = {
  accountNumber: '**********',
  fromDate: '2024-01-01',
  toDate: '2024-01-31',
  page: 1,
  limit: 20,
};

export const mockGetRecentContactsRequest = {
  limit: 10,
  page: 1,
};

// ============================================================================
// USE CASE RESPONSE MODELS
// ============================================================================

export const mockUseCaseSuccessResponse = <T>(data: T) => ({
  status: 'SUCCESS' as const,
  data,
});

export const mockUseCaseErrorResponse = (errorCode: string, message: string) => ({
  status: 'ERROR' as const,
  error: {
    code: errorCode,
    message,
    title: 'Use Case Error',
    category: 'BUSINESS' as const,
  },
});

// Common use case responses
export const mockCategoryListUseCaseResponse = mockUseCaseSuccessResponse({
  categories: mockCategoryList,
});

export const mockBillContactListUseCaseResponse = mockUseCaseSuccessResponse({
  contacts: mockBillContactList,
  totalCount: mockBillContactList.length,
});

export const mockPaymentValidationUseCaseResponse = mockUseCaseSuccessResponse(
  mockPaymentValidationResult
);

export const mockBillHistoryUseCaseResponse = mockUseCaseSuccessResponse({
  transactions: mockBillHistoryList,
  totalCount: mockBillHistoryList.length,
  hasMore: false,
});

// ============================================================================
// REPOSITORY INTERFACE MOCKS
// ============================================================================

export const createMockBillContactRepository = () => ({
  saveBillContact: jest.fn(),
  deleteBillContact: jest.fn(),
  editBillContact: jest.fn(),
  myBillContactList: jest.fn(),
  getMyBillContactRecentList: jest.fn(),
  getMyBillHistoryList: jest.fn(),
});

export const createMockBillPayRepository = () => ({
  categoryList: jest.fn(),
  providerList: jest.fn(),
  billValidate: jest.fn(),
  getBillDetail: jest.fn(),
  myBillList: jest.fn(),
});

export const createMockPaymentRepository = () => ({
  validate: jest.fn(),
});

// ============================================================================
// USE CASE MOCKS
// ============================================================================

export const createMockCategoryListUseCase = () => ({
  execute: jest.fn(),
});

export const createMockPaymentValidateUseCase = () => ({
  execute: jest.fn(),
});

export const createMockBillContactUseCase = () => ({
  saveBillContact: jest.fn(),
  deleteBillContact: jest.fn(),
  editBillContact: jest.fn(),
  getContactList: jest.fn(),
  getRecentContacts: jest.fn(),
  getBillHistory: jest.fn(),
});
