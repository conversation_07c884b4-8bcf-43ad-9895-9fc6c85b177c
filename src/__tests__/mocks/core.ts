/**
 * Core layer mock data
 * Mock data specific to core domain logic
 */

import { CustomError } from '@/core/MSBCustomError';
import { ResultState } from '@/core/ResultState';

// ============================================================================
// CUSTOM ERROR MOCK DATA
// ============================================================================

export const mockCustomErrorData = {
  code: 'PIS0101',
  message: 'Payment validation failed',
  title: 'Validation Error',
  category: 'VALIDATION' as const,
};

export const mockCustomError = new CustomError(
  mockCustomErrorData.code,
  mockCustomErrorData.message,
  mockCustomErrorData.title,
  mockCustomErrorData.category
);

export const mockSystemError = new CustomError(
  'SYS0001',
  'System error occurred',
  'System Error',
  'SYSTEM'
);

export const mockNetworkError = new CustomError(
  'NET0001',
  'Network connection failed',
  'Network Error',
  'NETWORK'
);

export const mockUnknownError = new CustomError(
  'UNKNOWN',
  'An unknown error occurred',
  'Unknown Error',
  'UNKNOWN'
);

// ============================================================================
// RESULT STATE MOCK DATA
// ============================================================================

export const mockSuccessResult = <T>(data: T): ResultState<T> => ({
  status: 'SUCCESS',
  data,
});

export const mockErrorResult = (error: CustomError): ResultState<any> => ({
  status: 'ERROR',
  error,
});

// Common success results
export const mockStringSuccessResult = mockSuccessResult('test data');
export const mockNumberSuccessResult = mockSuccessResult(42);
export const mockBooleanSuccessResult = mockSuccessResult(true);
export const mockObjectSuccessResult = mockSuccessResult({ id: 1, name: 'test' });
export const mockArraySuccessResult = mockSuccessResult([1, 2, 3]);

// Common error results
export const mockValidationErrorResult = mockErrorResult(mockCustomError);
export const mockSystemErrorResult = mockErrorResult(mockSystemError);
export const mockNetworkErrorResult = mockErrorResult(mockNetworkError);
export const mockUnknownErrorResult = mockErrorResult(mockUnknownError);

// ============================================================================
// ERROR MAPPER MOCK DATA
// ============================================================================

export const mockErrorMappings = {
  'PIS0101': {
    message: 'Payment validation failed',
    title: 'Validation Error',
    category: 'VALIDATION' as const,
  },
  'PIS0106': {
    message: 'Invalid payment amount',
    title: 'Amount Error',
    category: 'VALIDATION' as const,
  },
  'A05': {
    message: 'Account not found',
    title: 'Account Error',
    category: 'BUSINESS' as const,
  },
  'SYS0001': {
    message: 'System error occurred',
    title: 'System Error',
    category: 'SYSTEM' as const,
  },
  'NET0001': {
    message: 'Network connection failed',
    title: 'Network Error',
    category: 'NETWORK' as const,
  },
};

// ============================================================================
// MSB ERROR MOCK DATA
// ============================================================================

export const mockMSBErrors = {
  validation: { key: 'PIS0101', message: 'Payment validation failed' },
  amount: { key: 'PIS0106', message: 'Invalid payment amount' },
  account: { key: 'A05', message: 'Account not found' },
  system: { key: 'SYS0001', message: 'System error occurred' },
  network: { key: 'NET0001', message: 'Network connection failed' },
  unknown: { key: 'UNKNOWN', message: 'An unknown error occurred' },
};

// ============================================================================
// BASE RESPONSE MOCK DATA
// ============================================================================

export const mockBaseResponses = {
  success: { errors: null },
  validationError: { errors: [mockMSBErrors.validation] },
  amountError: { errors: [mockMSBErrors.amount] },
  accountError: { errors: [mockMSBErrors.account] },
  systemError: { errors: [mockMSBErrors.system] },
  networkError: { errors: [mockMSBErrors.network] },
  multipleErrors: { 
    errors: [mockMSBErrors.validation, mockMSBErrors.amount] 
  },
};
