/**
 * Utils layer mock data
 * Mock data for utilities, handlers, and cross-cutting concerns
 */

import { ResultState } from '@/core/ResultState';
import { CustomError } from '@/core/MSBCustomError';

// ============================================================================
// EXECUTION HANDLER MOCK DATA
// ============================================================================

export const mockExecutionHandlerSuccess = <T>(data: T): ResultState<T> => ({
  status: 'SUCCESS',
  data,
});

export const mockExecutionHandlerError = (
  code: string = 'PIS0101',
  message: string = 'Execution failed'
): ResultState<any> => ({
  status: 'ERROR',
  error: new CustomError(code, message, 'Execution Error', 'SYSTEM'),
});

// Common execution results
export const mockExecutionResults = {
  stringSuccess: mockExecutionHandlerSuccess('test result'),
  numberSuccess: mockExecutionHandlerSuccess(42),
  booleanSuccess: mockExecutionHandlerSuccess(true),
  objectSuccess: mockExecutionHandlerSuccess({ id: 1, name: 'test' }),
  arraySuccess: mockExecutionHandlerSuccess([1, 2, 3]),
  
  validationError: mockExecutionHandlerError('PIS0101', 'Validation failed'),
  systemError: mockExecutionHandlerError('SYS0001', 'System error'),
  networkError: mockExecutionHandlerError('NET0001', 'Network error'),
  unknownError: mockExecutionHandlerError('UNKNOWN', 'Unknown error'),
};

// ============================================================================
// HANDLE DATA MOCK DATA
// ============================================================================

export const mockHandleDataInputs = {
  validResponse: {
    data: 'test data',
    errors: null,
  },
  errorResponse: {
    errors: [{ key: 'PIS0106', message: 'Handle data error' }],
  },
  nullResponse: null,
  undefinedResponse: undefined,
};

export const mockHandleDataResults = {
  success: {
    status: 'SUCCESS' as const,
    data: 'mapped test data',
  },
  error: {
    status: 'ERROR' as const,
    error: new CustomError('PIS0106', 'Handle data error', 'Data Error', 'UNKNOWN'),
  },
  nullError: {
    status: 'ERROR' as const,
    error: new CustomError('UNKNOWN', 'No data received', 'Data Error', 'UNKNOWN'),
  },
};

// ============================================================================
// MAPPER MOCK FUNCTIONS
// ============================================================================

export const createMockMapper = <TInput, TOutput>(outputData: TOutput) => ({
  map: jest.fn().mockReturnValue(outputData),
});

export const createMockMapperWithError = (errorMessage: string = 'Mapping failed') => ({
  map: jest.fn().mockImplementation(() => {
    throw new Error(errorMessage);
  }),
});

// Common mappers
export const mockStringMapper = createMockMapper('mapped string');
export const mockObjectMapper = createMockMapper({ id: 1, name: 'mapped' });
export const mockArrayMapper = createMockMapper([1, 2, 3]);
export const mockErrorMapper = createMockMapperWithError('Mapping error');

// ============================================================================
// HOST SHARED MODULE MOCK
// ============================================================================

export const createMockHostSharedModule = () => ({
  d: {
    domainService: {
      addSpinnerRequest: jest.fn(),
      addSpinnerCompleted: jest.fn(),
      showPopup: jest.fn(),
    },
  },
});

export const mockHostSharedModuleWithCallbacks = () => {
  const mockModule = createMockHostSharedModule();
  
  // Add implementation for spinner methods
  mockModule.d.domainService.addSpinnerRequest.mockImplementation(() => {
    console.log('Spinner started');
  });
  
  mockModule.d.domainService.addSpinnerCompleted.mockImplementation(() => {
    console.log('Spinner completed');
  });
  
  mockModule.d.domainService.showPopup.mockImplementation((config: any) => {
    console.log('Popup shown:', config);
    return Promise.resolve();
  });
  
  return mockModule;
};

// ============================================================================
// EXECUTION HANDLER MOCK FUNCTIONS
// ============================================================================

export const createMockExecutionHandler = () => jest.fn();

export const createMockExecutionHandlerWithResults = () => {
  const mockHandler = jest.fn();
  
  // Default to success
  mockHandler.mockResolvedValue(mockExecutionResults.objectSuccess);
  
  return mockHandler;
};

export const createMockExecutionHandlerWithError = (errorCode: string = 'PIS0101') => {
  const mockHandler = jest.fn();
  
  mockHandler.mockResolvedValue(
    mockExecutionHandlerError(errorCode, 'Execution failed')
  );
  
  return mockHandler;
};

// ============================================================================
// ASYNC FUNCTION MOCKS
// ============================================================================

export const createMockAsyncFunction = <T>(returnValue: T, delay: number = 0) => {
  return jest.fn().mockImplementation(async () => {
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    return returnValue;
  });
};

export const createMockAsyncFunctionWithError = (
  errorMessage: string = 'Async function failed',
  delay: number = 0
) => {
  return jest.fn().mockImplementation(async () => {
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    throw new Error(errorMessage);
  });
};

// ============================================================================
// LOADING STATE MOCK DATA
// ============================================================================

export const mockLoadingStates = {
  initial: { isLoading: false, hasStarted: false },
  loading: { isLoading: true, hasStarted: true },
  completed: { isLoading: false, hasStarted: true },
  error: { isLoading: false, hasStarted: true, hasError: true },
};

// ============================================================================
// CONSOLE MOCK UTILITIES
// ============================================================================

export const createMockConsole = () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
});

export const mockConsoleWithImplementation = () => {
  const mockConsole = createMockConsole();
  
  // Add implementations that actually log for debugging
  mockConsole.log.mockImplementation((...args) => {
    // Uncomment for debugging: console.log('[MOCK LOG]', ...args);
  });
  
  mockConsole.error.mockImplementation((...args) => {
    // Uncomment for debugging: console.error('[MOCK ERROR]', ...args);
  });
  
  return mockConsole;
};

// ============================================================================
// TIMER MOCK UTILITIES
// ============================================================================

export const createMockTimer = () => ({
  setTimeout: jest.fn(),
  clearTimeout: jest.fn(),
  setInterval: jest.fn(),
  clearInterval: jest.fn(),
});

// ============================================================================
// PROMISE UTILITIES
// ============================================================================

export const createMockPromise = <T>(value: T, shouldReject: boolean = false) => {
  if (shouldReject) {
    return Promise.reject(new Error(String(value)));
  }
  return Promise.resolve(value);
};

export const createDelayedMockPromise = <T>(
  value: T,
  delay: number,
  shouldReject: boolean = false
) => {
  return new Promise<T>((resolve, reject) => {
    setTimeout(() => {
      if (shouldReject) {
        reject(new Error(String(value)));
      } else {
        resolve(value);
      }
    }, delay);
  });
};
