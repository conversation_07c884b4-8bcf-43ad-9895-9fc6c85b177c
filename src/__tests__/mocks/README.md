# Mock Data Documentation

Th<PERSON> mục này chứa tất cả mock data đư<PERSON><PERSON> tổ chức theo từng layer của Clean Architecture để sử dụng trong unit tests.

## C<PERSON>u trúc thư mục

```
src/__tests__/mocks/
├── README.md           # Tài liệu hướng dẫn
├── index.ts           # Export tất cả mock data
├── core.ts            # Mock data cho Core layer
├── domain.ts          # Mock data cho Domain layer  
├── data.ts            # Mock data cho Data layer
├── utils.ts           # Mock data cho Utils layer
└── responses.ts       # Mock responses (legacy support)
```

## Cách sử dụng

### Import từ index file (Khuyến nghị)

```typescript
import {
  mockCustomError,
  mockBillContactEntity,
  mockApiSuccessResponse,
  createMockExecutionHandler,
} from '../../__tests__/mocks';
```

### Import từ file cụ thể

```typescript
import { mockCustomError } from '../../__tests__/mocks/core';
import { mockBillContactEntity } from '../../__tests__/mocks/domain';
```

## Chi tiết từng file

### 1. core.ts - Core Layer Mocks

**Mock data cho:**
- CustomError instances
- ResultState patterns
- Error mappings
- MSB Error codes
- Base response structures

**Ví dụ sử dụng:**
```typescript
import { mockCustomError, mockSuccessResult } from '../../__tests__/mocks/core';

// Sử dụng trong test
expect(result.error).toEqual(mockCustomError);
expect(result).toEqual(mockSuccessResult('test data'));
```

### 2. domain.ts - Domain Layer Mocks

**Mock data cho:**
- Domain entities (BillContact, Category, Payment)
- Use case requests/responses
- Business models
- Repository interfaces

**Ví dụ sử dụng:**
```typescript
import { 
  mockBillContactEntity,
  createMockBillContactRepository 
} from '../../__tests__/mocks/domain';

const mockRepository = createMockBillContactRepository();
mockRepository.saveBillContact.mockResolvedValue(mockBillContactEntity);
```

### 3. data.ts - Data Layer Mocks

**Mock data cho:**
- API responses
- Data source implementations
- HTTP client mocks
- Mapper input/output data

**Ví dụ sử dụng:**
```typescript
import { 
  mockApiSuccessResponse,
  createMockBillContactDataSource 
} from '../../__tests__/mocks/data';

const mockDataSource = createMockBillContactDataSource();
expect(response).toEqual(mockApiSuccessResponse(data));
```

### 4. utils.ts - Utils Layer Mocks

**Mock data cho:**
- ExecutionHandler results
- HandleData utilities
- Host shared module
- Async function mocks

**Ví dụ sử dụng:**
```typescript
import { 
  mockExecutionHandlerSuccess,
  createMockHostSharedModule 
} from '../../__tests__/mocks/utils';

const mockHost = createMockHostSharedModule();
expect(result).toEqual(mockExecutionHandlerSuccess('data'));
```

## Quick Setup Functions

File `index.ts` cung cấp các hàm setup nhanh cho các scenario phổ biến:

```typescript
import { setupMocks } from '../../__tests__/mocks';

// Setup cho core functionality
const coreMocks = setupMocks.core();

// Setup cho repository layer
const repoMocks = setupMocks.repository();

// Setup cho use case layer  
const useCaseMocks = setupMocks.useCase();

// Setup cho utils layer
const utilsMocks = setupMocks.utils();
```

## Best Practices

### 1. Tổ chức Mock Data
- Sử dụng mock data từ file tương ứng với layer đang test
- Import từ `index.ts` để có consistency
- Tạo mock functions thay vì hardcode data trong test

### 2. Naming Convention
- Prefix `mock` cho data objects: `mockBillContactEntity`
- Prefix `create` cho factory functions: `createMockRepository`
- Suffix theo type: `ApiResponse`, `Entity`, `Request`

### 3. Reusability
- Tạo factory functions cho complex mocks
- Sử dụng parameters để customize mock data
- Avoid duplication giữa các test files

### 4. Maintenance
- Update mock data khi domain models thay đổi
- Keep mock data simple và focused
- Document complex mock scenarios

## Ví dụ Test File

```typescript
import { handleData } from '../HandleData';
import {
  mockHandleDataInputs,
  createMockMapper,
  mockExecutionHandlerSuccess,
} from '../../__tests__/mocks';

describe('HandleData', () => {
  const mockMapper = createMockMapper('mapped data');

  it('should handle successful response', async () => {
    const result = await handleData(
      Promise.resolve(mockHandleDataInputs.validResponse),
      mockMapper.map
    );

    expect(result).toBe('mapped data');
    expect(mockMapper.map).toHaveBeenCalledWith(
      mockHandleDataInputs.validResponse
    );
  });
});
```

## Migration từ Inline Mocks

Nếu bạn có inline mock data trong test files, có thể migrate như sau:

**Trước:**
```typescript
const mockResponse = {
  data: 'test data',
  errors: null,
};
```

**Sau:**
```typescript
import { mockHandleDataInputs } from '../../__tests__/mocks';

// Sử dụng
mockHandleDataInputs.validResponse
```

## Troubleshooting

### Import Errors
- Đảm bảo đường dẫn relative path đúng
- Check file `index.ts` có export mock cần thiết không

### Type Errors  
- Verify mock data structure match với interface
- Update mock data khi domain models change

### Test Failures
- Check mock functions được setup đúng
- Verify mock return values match expected results
