/**
 * Mock data index
 * Central export point for all mock data
 */

// Core layer mocks
export * from './core';

// Domain layer mocks
export * from './domain';

// Data layer mocks
export * from './data';

// Utils layer mocks
export * from './utils';

// Response mocks (legacy - for backward compatibility)
export * from './responses';

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

// Re-export commonly used mocks for easy access
export {
  mockCustomError,
  mockSuccessResult,
  mockErrorResult,
  mockMSBErrors,
} from './core';

export {
  mockBillContactEntity,
  mockCategoryEntity,
  mockPaymentValidationRequest,
  createMockBillContactRepository,
} from './domain';

export {
  mockApiSuccessResponse,
  mockApiErrorResponse,
  createMockBillContactDataSource,
  createMockHttpClient,
} from './data';

export {
  mockExecutionHandlerSuccess,
  mockExecutionHandlerError,
  createMockHostSharedModule,
  createMockExecutionHandler,
} from './utils';

// ============================================================================
// QUICK SETUP FUNCTIONS
// ============================================================================

/**
 * Quick setup for common test scenarios
 */
export const setupMocks = {
  /**
   * Setup basic mocks for core functionality
   */
  core: () => ({
    customError: mockCustomError,
    successResult: mockSuccessResult('test'),
    errorResult: mockErrorResult(mockCustomError),
  }),

  /**
   * Setup mocks for repository layer testing
   */
  repository: () => ({
    dataSource: createMockBillContactDataSource(),
    httpClient: createMockHttpClient(),
  }),

  /**
   * Setup mocks for use case layer testing
   */
  useCase: () => ({
    repository: createMockBillContactRepository(),
    executionHandler: createMockExecutionHandler(),
  }),

  /**
   * Setup mocks for utils testing
   */
  utils: () => ({
    hostModule: createMockHostSharedModule(),
    executionHandler: createMockExecutionHandler(),
  }),
};
