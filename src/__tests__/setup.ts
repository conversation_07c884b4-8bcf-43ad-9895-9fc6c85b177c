/**
 * Jest Test Setup
 * Global mocks and configurations for React Native testing
 */

// Mock React Native modules
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Platform: {
      OS: 'ios',
      select: jest.fn((obj) => obj.ios),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812 })),
    },
    Alert: {
      alert: jest.fn(),
    },
    Linking: {
      openURL: jest.fn(),
    },
  };
});

// Mock MSB Host Shared Module
jest.mock('msb-host-shared-module', () => ({
  hostSharedModule: {
    d: {
      domainService: {
        addSpinnerRequest: jest.fn(),
        addSpinnerCompleted: jest.fn(),
        showPopup: jest.fn(),
        onTransfer: jest.fn(),
      },
    },
  },
  IHttpClient: jest.fn(),
}));

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.mockResponse = <T>(data: T, errors?: any[]) => ({
  ...data,
  errors: errors || undefined,
});

global.mockError = (key: string, message?: string) => ({
  key,
  message: message || 'Test error message',
  context: {
    vi: message || 'Test error message',
    en: message || 'Test error message',
  },
});

// Setup fake timers
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.clearAllTimers();
});
