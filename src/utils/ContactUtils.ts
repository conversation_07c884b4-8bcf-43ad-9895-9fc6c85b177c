import {selectContactPhone} from 'react-native-select-contact';
import {PermissionsAndroid} from 'react-native/Libraries/PermissionsAndroid/PermissionsAndroid';

const requestContactPermission = async (): Promise<boolean> => {
  try {
    const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_CONTACTS, {
      title: 'Allow Access Contact?',
      message: 'allow this app to read contact information',
      buttonNegative: 'Cancel',
      buttonPositive: 'OK',
    });
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      console.log('granted');
      return true;
    } else {
      console.log('denied');
      return false;
    }
  } catch (err) {
    console.warn(err);
    return false;
  }
};

const getContact = async (): Promise<string | undefined | null> => {
  return selectContactPhone()
    .then(select => {
      if (!select) {
        return null;
      }
      const {contact, selectedPhone} = select;
      const phoneNum = selectedPhone?.number;
      const phoneStr = phoneNum?.split(' ')?.join('');
      console.log(`Selected ${selectedPhone.type} phone number ${selectedPhone.number} from ${contact.name}`);

      return phoneStr;
    })
    .catch(e => {
      console.log('====================================');
      console.log(e);
      console.log('====================================');
      return undefined;
    });
};

export {requestContactPermission, getContact};
