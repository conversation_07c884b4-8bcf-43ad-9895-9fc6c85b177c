import {createError, CustomError} from '../core/MSBCustomError';
import {MSBErrorCode} from '../core/MSBErrorCode';

export const handleResponse = async (response: Response): Promise<any> => {
  try {
    console.log('✅✅✅✅✅✅✅✅✅✅✅DataSource Response Successs', response);
    if (response.status === 204) {
      return {};
    } else if (response.ok === false) {
      const data = await response.json();
      if (data.errors) {
        return data;
      } else {
        console.log('ADDED UNKNOWN ERROR TO HANDLE DATA');
        return {
          errors: [
            {
              key: MSBErrorCode.UNKNOWN_ERROR,
            },
          ],
          ...data,
        };
      }
    }
    return await response.json();
  } catch (error) {
    console.log('❌❌❌❌❌❌❌❌❌❌ DataSource HTTP or Json mapper Error', response);
    if (error instanceof CustomError) {
      throw error;
    }
    throw createError();
  }
};
