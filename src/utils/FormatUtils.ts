import moment from 'moment';

const formatPrice = (amount: number | string | null | undefined) => {
  if (amount == null || amount == undefined) {
    return '0';
  }
  const num = typeof amount === 'string' ? Number(amount) : amount;
  if (isNaN(num)) {
    return '0';
  }
  return num.toLocaleString('vi-VN').replace(/\./g, ',');
};
const formattedNumber = (str: string): number => {
  return Number(str.replace(/,/g, ''));
};

const formatDateHHMM = (isoString: string): string => {
  return moment(isoString).format('HH:mm');
};

const formatDateDDMMYYYY = (isoString: string): string => {
  return moment(isoString).format('DD/MM/YYYY');
};

const numberToWordsVi = (number: number, currency = 'VND') => {
  const units = ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
  const teens = [
    'mười',
    'mười một',
    'mười hai',
    'mười ba',
    'mười bốn',
    'mười lăm',
    'mười sáu',
    'mười bảy',
    'mười tám',
    'mười chín',
  ];
  const tens = ['', '', 'hai mươi', 'ba mươi', 'bốn mươi', 'năm mươi', 'sáu mươi', 'bảy mươi', 'tám mươi', 'chín mươi'];
  const scales = ['', 'nghìn', 'triệu', 'tỷ', 'nghìn tỷ', 'nghìn nghìn tỷ'];

  function convertToWords(n: any, scaleIndex: number) {
    if (n === 0) {
      return '';
    }

    const scale = scales[scaleIndex];
    const hundred = Math.floor(n / 100);
    const remainder = n % 100;

    let result = '';

    if (hundred > 0) {
      result += units[hundred] + ' trăm ';
    }

    if (remainder > 0) {
      if (remainder < 10) {
        result += units[remainder] + ' ';
      } else if (remainder < 20) {
        result += teens[remainder - 10] + ' ';
      } else {
        const ten = Math.floor(remainder / 10);
        const one = remainder % 10;
        result += tens[ten] + ' ' + units[one] + ' ';
      }
    }

    return result + scale + ' ';
  }

  if (number === 0) {
    return 'Không đồng';
  }

  let result = '';
  let scaleIndex = 0;
  let currencyWord = '';

  while (number > 0) {
    const chunk = number % 1000;
    if (chunk > 0) {
      result = convertToWords(chunk, scaleIndex) + result;
    }
    number = Math.floor(number / 1000);
    scaleIndex++;
  }

  if (currency === 'VND') {
    currencyWord = 'đồng';
  } else if (currency === 'USD') {
    currencyWord = 'USD;';
  }

  return result.trim()[0]?.toUpperCase() + result.trim().slice(1) + ' ' + currencyWord;
};

const splitTransactions = (amount: number, maxLimit247: number, splitAmount: number) => {
  const transactions: number[] = [];

  let splitTrans = Math.floor(amount / splitAmount);
  let finalTrans = amount % splitAmount;

  // Nếu finalTrans + splitAmount <= maxLimit247, gộp final vào giao dịch cuối
  if (finalTrans !== 0 && finalTrans + splitAmount <= maxLimit247) {
    splitTrans--;
    finalTrans += splitAmount;
  }

  if (splitTrans > 0) {
    transactions.push(...Array(splitTrans).fill(splitAmount));
  }

  // Thêm giao dịch cuối nếu có
  if (finalTrans > 0) {
    transactions.push(finalTrans);
  }

  const transactionItemsMap = new Map<number, number>();

  for (const trans of transactions) {
    transactionItemsMap.set(trans, (transactionItemsMap.get(trans) || 0) + 1);
  }

  const transactionItems = Array.from(transactionItemsMap, ([amount, count]) => ({amount, count}));

  return {
    totalTransaction: transactions.length,
    transactions,
    transactionItems,
  };
};

// chuyển tiếng việt có dấu sang tiếng việt không dấu
const removeVietnameseTones = (str: string): string => {
  if (!str) {
    return '';
  }
  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/đ/g, 'd')
    .replace(/Đ/g, 'D')
    .trim();
};

// chỉ cho phép nhập các tự đặc biệt /().+_,
// không cho nhập các kí tự đặc biệt còn lại và icon
// dùng cho nhập nội dung chuyển khoản
const formatRemittanceInformation = (content?: string | null) => {
  if (!content) {
    return '';
  }
  return content.replace(/[^a-zA-Z0-9À-ỹ\-\\/().+_, ]/g, '')?.replace(/\s+/g, ' ');
};

const removeSpecialCharsAndEmoji = (content?: string | null) => {
  if (!content) {
    return '';
  }
  return content.replace(/[^\p{L} ]+/gu, '');
};

const formatMoney = (text: string): string => {
  console.log('text', text);
  const cleanedText = text.replace(/[^0-9.]/g, '');
  console.log('cleanedText', cleanedText);
  const withoutLeadingZeros = cleanedText.replace(/^0+/, '') || '0';
  return withoutLeadingZeros.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

const generateAmountSuggestions = (input: string): string[] => {
  if (!input || input.length === 0) {
    return ['100,000', '200,000', '500,000', 'Xong'];
  }

  const num = parseInt(input, 10);
  console.log('num', num);
  if (isNaN(num) || num === 0) {
    return ['100,000', '200,000', '500,000', 'Xong'];
  }

  const length = input.length;
  const suggestions: string[] = [];

  if (length === 1) {
    suggestions.push(formatMoney((num * 10000).toString()));
    suggestions.push(formatMoney((num * 100000).toString()));
    suggestions.push(formatMoney((num * 1000000).toString()));
  } else if (length === 2) {
    suggestions.push(formatMoney((num * 1000).toString()));
    suggestions.push(formatMoney((num * 10000).toString()));
    suggestions.push(formatMoney((num * 100000).toString()));
  } else if (length === 3 || length === 4) {
    suggestions.push(formatMoney((num * 100).toString()));
    suggestions.push(formatMoney((num * 1000).toString()));
    suggestions.push(formatMoney((num * 100000).toString()));
  } else if (length === 5 || length === 6) {
    suggestions.push(formatMoney((num * 100).toString()));
    suggestions.push(formatMoney((num * 1000).toString()));
    suggestions.push(formatMoney((num * 10000).toString()));
  } else {
    suggestions.push(formatMoney((num * 10).toString()));
    suggestions.push(formatMoney((num * 100).toString()));
  }

  suggestions.push('Xong');
  return suggestions;
};

export default {
  formatPrice,
  formatMoney,
  formattedNumber,
  numberToWordsVi,
  splitTransactions,
  formatDateDDMMYYYY,
  formatDateHHMM,
  formatRemittanceInformation,
  generateAmountSuggestions,
  removeVietnameseTones,
  removeSpecialCharsAndEmoji,
};
