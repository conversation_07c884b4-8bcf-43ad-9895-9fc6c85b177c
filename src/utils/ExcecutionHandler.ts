import {ResultState} from '../core/ResultState';
import {BaseResponse} from '../core/BaseResponse';
import {hostSharedModule} from 'msb-host-shared-module';
import {createError, CustomError} from '../core/MSBCustomError';

export class ExecutionHandler {
  static async execute<T>(
    fetchFunction: () => Promise<BaseResponse<T>>,
    isUseLoading?: boolean | null | undefined,
  ): Promise<ResultState<T>> {
    try {
      if (isUseLoading) {
        hostSharedModule.d.domainService.addSpinnerRequest();
      }
      const response = await fetchFunction();
      console.log('✅ ExecutionHandler with response=', response);
      if (isUseLoading) {
        hostSharedModule.d.domainService.addSpinnerCompleted();
      }
      if (!response || response?.errors) {
        console.error('❌ ExecutionHandler Null data Error');
        return {
          status: 'ERROR',
          error: createError(response?.errors?.[0].key ?? ''),
        };
      }
      return {status: 'SUCCESS', data: response};
    } catch (error) {
      console.error('❌ ExecutionHandler UseCase Error:', error);

      if (error instanceof CustomError) {
        return {
          status: 'ERROR',
          error: error,
        };
      }
      return {
        status: 'ERROR',
        error: createError((error as any)?.code),
      };
    } finally {
      if (isUseLoading) {
        hostSharedModule.d.domainService.addSpinnerCompleted();
      }
    }
  }
}
