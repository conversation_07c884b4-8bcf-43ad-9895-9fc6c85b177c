const baseUrl = process.env.API_URL || '';

export const PathResolver = {
  billContact: {
    saveBillContact: () => `${baseUrl}/contact-manager-extension/client-api/v1/contacts`,
    deleteBillContact: (id: string) => `${baseUrl}/contact-manager/client-api/v2/contacts/${id}`,
    editBillContact: (id: string) => `${baseUrl}/contact-manager/client-api/v2/contacts/${id}`,
    myBillContactList: (category: string) => `${baseUrl}/contact-manager/client-api/v2/contacts?category=${category}`,
    getMyBillContactRecentList: () => `${baseUrl}/transaction-manager/client-api/v2/transactions`,
    getMyBillHistoryList: () => `${baseUrl}/transaction-manager/client-api/v2/transactions`,
  },

  getMyBillHistoryList: {
    getMyBillHistoryList: () => `${baseUrl}/transaction-manager/client-api/v2/transactions`,
  },

  paymentOrder: {
    paymentOrder: () => `${baseUrl}/payment-order-service/client-api/v3/payment-orders`,
    paymentOrderStatus: (id: string) => `${baseUrl}/payment-order-service/client-api/v3/payment-orders/${id}`,
  },

  payment: {
    validate: () => `${baseUrl}/payment/validate`,
  },

  arrangement: {
    sourceAccountList: () => `${baseUrl}/arrangement-manager-extension/client-api/v1/arrangements/search`,
  },

  customer: {
    getProfile: () => `${baseUrl}/digital-customer/client-api/v1/customers/me`,
  },
  billPay: {
    categoryList: () => `${baseUrl}/product-setup/client-api/v1/product/search?code=BLP.CT&status=ACTIVE`,
    providerList: (code: string) => `${baseUrl}/contact-manager-extension/client-api/v1/bill-pay/${code}/providers`,
    myBillList: () => `${baseUrl}/bill-pay/my-bill-list`,
    getBillDetail: () => `${baseUrl}/billpay-extension/client-api/v1/billpays/bills/query`,
    billValidate: () => `${baseUrl}/payment-order-service/client-api/v3/payment-orders/validate`,
  },
};
