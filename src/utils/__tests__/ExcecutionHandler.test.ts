/**
 * Unit Tests for ExecutionHandler
 * Testing cross-cutting concerns: loading, error handling, logging
 */

import { ExecutionHandler } from '../ExcecutionHandler';
import { ResultState } from '../../core/ResultState';
import { BaseResponse } from '../../core/BaseResponse';
import { CustomError, ErrorCategory, createError } from '../../core/MSBCustomError';
import { MSBErrorCode } from '../../core/MSBErrorCode';
import { hostSharedModule } from 'msb-host-shared-module';

// Mock hostSharedModule
jest.mock('msb-host-shared-module');

describe('ExecutionHandler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset console mocks
    (console.log as jest.Mock).mockClear();
    (console.error as jest.Mock).mockClear();
  });

  describe('Successful Execution', () => {
    it('should execute function successfully without loading', async () => {
      const mockData = { id: 1, name: 'Test User' };
      const mockResponse: BaseResponse<typeof mockData> = mockData;
      
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      const result = await ExecutionHandler.execute(fetchFunction, false);

      expect(result.status).toBe('SUCCESS');
      expect(result.data).toEqual(mockResponse);
      expect(fetchFunction).toHaveBeenCalledTimes(1);
      expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
    });

    it('should execute function successfully with loading', async () => {
      const mockData = { id: 1, name: 'Test User' };
      const mockResponse: BaseResponse<typeof mockData> = mockData;
      
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      const result = await ExecutionHandler.execute(fetchFunction, true);

      expect(result.status).toBe('SUCCESS');
      expect(result.data).toEqual(mockResponse);
      expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
    });

    it('should log successful execution', async () => {
      const mockResponse: BaseResponse<any> = { data: 'test' };
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      await ExecutionHandler.execute(fetchFunction);

      expect(console.log).toHaveBeenCalledWith('✅ ExecutionHandler with response=', mockResponse);
    });
  });

  describe('Error Handling - Response Errors', () => {
    it('should handle response with errors array', async () => {
      const mockResponse: BaseResponse<any> = {
        data: null,
        errors: [{ key: MSBErrorCode.PIS0101, message: 'Business error' }],
      };
      
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
      expect(result.error.code).toBe(MSBErrorCode.PIS0101);
      expect(console.error).toHaveBeenCalledWith('❌ ExecutionHandler Null data Error');
    });

    it('should handle null response', async () => {
      const fetchFunction = jest.fn().mockResolvedValue(null);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
      expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
    });

    it('should handle undefined response', async () => {
      const fetchFunction = jest.fn().mockResolvedValue(undefined);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
      expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
    });

    it('should use first error key when multiple errors exist', async () => {
      const mockResponse: BaseResponse<any> = {
        data: null,
        errors: [
          { key: MSBErrorCode.PIS0106, message: 'Network error' },
          { key: MSBErrorCode.A05, message: 'System error' },
        ],
      };
      
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error.code).toBe(MSBErrorCode.PIS0106);
    });

    it('should handle error without key', async () => {
      const mockResponse: BaseResponse<any> = {
        data: null,
        errors: [{ message: 'Error without key' }],
      };
      
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
    });
  });

  describe('Error Handling - Exceptions', () => {
    it('should handle CustomError exceptions', async () => {
      const customError = new CustomError(
        'CUSTOM_ERROR',
        ErrorCategory.BUSINESS,
        'Custom Error',
        'This is a custom error',
        true,
        [{ type: 'RETRY', label: 'Retry', primary: true }]
      );

      const fetchFunction = jest.fn().mockRejectedValue(customError);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBe(customError);
      expect(result.error.code).toBe('CUSTOM_ERROR');
      expect(console.error).toHaveBeenCalledWith('❌ ExecutionHandler UseCase Error:', customError);
    });

    it('should handle generic exceptions with error code', async () => {
      const genericError = { code: MSBErrorCode.A05, message: 'Generic error' };
      const fetchFunction = jest.fn().mockRejectedValue(genericError);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
      expect(result.error.code).toBe(MSBErrorCode.A05);
    });

    it('should handle generic exceptions without error code', async () => {
      const genericError = new Error('Generic error message');
      const fetchFunction = jest.fn().mockRejectedValue(genericError);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
      expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
    });

    it('should handle string exceptions', async () => {
      const fetchFunction = jest.fn().mockRejectedValue('String error');

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
      expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
    });
  });

  describe('Loading Management', () => {
    it('should manage loading state correctly on success', async () => {
      const mockResponse: BaseResponse<any> = { data: 'test' };
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      await ExecutionHandler.execute(fetchFunction, true);

      expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
    });

    it('should manage loading state correctly on error', async () => {
      const fetchFunction = jest.fn().mockRejectedValue(new Error('Test error'));

      await ExecutionHandler.execute(fetchFunction, true);

      expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
    });

    it('should complete loading even if function throws', async () => {
      const fetchFunction = jest.fn().mockImplementation(() => {
        throw new Error('Synchronous error');
      });

      await ExecutionHandler.execute(fetchFunction, true);

      expect(hostSharedModule.d.domainService.addSpinnerRequest).toHaveBeenCalledTimes(1);
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).toHaveBeenCalledTimes(1);
    });

    it('should not manage loading when isUseLoading is false', async () => {
      const mockResponse: BaseResponse<any> = { data: 'test' };
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      await ExecutionHandler.execute(fetchFunction, false);

      expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
    });

    it('should not manage loading when isUseLoading is null', async () => {
      const mockResponse: BaseResponse<any> = { data: 'test' };
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      await ExecutionHandler.execute(fetchFunction, null);

      expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
    });

    it('should not manage loading when isUseLoading is undefined', async () => {
      const mockResponse: BaseResponse<any> = { data: 'test' };
      const fetchFunction = jest.fn().mockResolvedValue(mockResponse);

      await ExecutionHandler.execute(fetchFunction);

      expect(hostSharedModule.d.domainService.addSpinnerRequest).not.toHaveBeenCalled();
      expect(hostSharedModule.d.domainService.addSpinnerCompleted).not.toHaveBeenCalled();
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for success results', async () => {
      interface UserData {
        id: number;
        name: string;
        email: string;
      }

      const mockUserData: UserData = {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
      };

      const fetchFunction = jest.fn().mockResolvedValue(mockUserData);

      const result: ResultState<UserData> = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.id).toBe(1);
        expect(result.data?.name).toBe('John Doe');
        expect(result.data?.email).toBe('<EMAIL>');
      }
    });

    it('should work with generic types', async () => {
      async function executeWithType<T>(
        fetchFn: () => Promise<BaseResponse<T>>
      ): Promise<ResultState<T>> {
        return ExecutionHandler.execute(fetchFn);
      }

      const stringResult = await executeWithType(() => Promise.resolve('test string'));
      const numberResult = await executeWithType(() => Promise.resolve(42));
      const arrayResult = await executeWithType(() => Promise.resolve([1, 2, 3]));

      expect(stringResult.status).toBe('SUCCESS');
      expect(numberResult.status).toBe('SUCCESS');
      expect(arrayResult.status).toBe('SUCCESS');

      if (stringResult.status === 'SUCCESS') {
        expect(typeof stringResult.data).toBe('string');
      }
      if (numberResult.status === 'SUCCESS') {
        expect(typeof numberResult.data).toBe('number');
      }
      if (arrayResult.status === 'SUCCESS') {
        expect(Array.isArray(arrayResult.data)).toBe(true);
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle function that returns promise resolving to null', async () => {
      const fetchFunction = jest.fn().mockResolvedValue(null);

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
    });

    it('should handle function that throws immediately', async () => {
      const fetchFunction = jest.fn().mockImplementation(() => {
        throw new Error('Immediate error');
      });

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
    });

    it('should handle async function that rejects', async () => {
      const fetchFunction = jest.fn().mockRejectedValue(new Error('Async rejection'));

      const result = await ExecutionHandler.execute(fetchFunction);

      expect(result.status).toBe('ERROR');
      expect(result.error).toBeInstanceOf(CustomError);
    });
  });
});
