/**
 * Unit Tests for HandleData
 * Testing data transformation and error handling in Repository layer
 */

import {handleData} from '../HandleData';
import {CustomError, ErrorCategory, createError} from '../../core/MSBCustomError';
import {MSBErrorCode} from '../../core/MSBErrorCode';
import {mockHandleDataInputs, createMockMapper, createMockMapperWithError} from '../../__tests__/mocks';

describe('HandleData', () => {
  // Mock mapper functions
  const mockMapper = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (console.log as jest.Mock).mockClear();
  });

  describe('Successful Data Handling', () => {
    it('should handle successful response and map data', async () => {
      const mockResponse = {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
      };

      const mappedData = {
        userId: 1,
        fullName: '<PERSON>',
        emailAddress: '<EMAIL>',
      };

      mockMapper.mockReturnValue(mappedData);
      const mockRequest = Promise.resolve(mockResponse);

      const result = await handleData(mockRequest, mockMapper);

      expect(result).toEqual(mappedData);
      expect(mockMapper).toHaveBeenCalledWith(mockResponse);
      expect(mockMapper).toHaveBeenCalledTimes(1);
      expect(console.log).toHaveBeenCalledWith('DATA:', mockResponse);
      expect(console.log).toHaveBeenCalledWith('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', mockResponse);
    });

    it('should handle complex data structures', async () => {
      const mockResponse = {
        users: [
          {id: 1, name: 'User 1'},
          {id: 2, name: 'User 2'},
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
        },
      };

      const mappedData = {
        userList: mockResponse.users,
        totalCount: mockResponse.pagination.total,
        currentPage: mockResponse.pagination.page,
      };

      mockMapper.mockReturnValue(mappedData);
      const mockRequest = Promise.resolve(mockResponse);

      const result = await handleData(mockRequest, mockMapper);

      expect(result).toEqual(mappedData);
      expect(result.userList).toHaveLength(2);
      expect(result.totalCount).toBe(2);
    });

    it('should handle empty but valid responses', async () => {
      const mockResponse = {
        data: [],
        total: 0,
      };

      const mappedData = {
        items: [],
        count: 0,
      };

      mockMapper.mockReturnValue(mappedData);
      const mockRequest = Promise.resolve(mockResponse);

      const result = await handleData(mockRequest, mockMapper);

      expect(result).toEqual(mappedData);
      expect(result.items).toEqual([]);
      expect(result.count).toBe(0);
    });
  });

  describe('Error Handling - Response Errors', () => {
    it('should throw CustomError when response has errors array', async () => {
      const mockResponse = {
        data: null,
        errors: [
          {
            key: MSBErrorCode.PIS0101,
            message: 'Business logic error',
          },
        ],
      };

      const mockRequest = Promise.resolve(mockResponse);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(CustomError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.PIS0101);
      }

      expect(mockMapper).not.toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response',
        mockResponse,
      );
    });

    it('should throw CustomError when response is null', async () => {
      const mockRequest = Promise.resolve(null);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(CustomError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.UNKNOWN_ERROR);
      }

      expect(mockMapper).not.toHaveBeenCalled();
    });

    it('should throw CustomError when response is undefined', async () => {
      const mockRequest = Promise.resolve(undefined);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(CustomError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.UNKNOWN_ERROR);
      }
    });

    it('should use specific error code from response', async () => {
      const mockResponse = {
        data: null,
        errors: [
          {
            key: MSBErrorCode.PIS0106,
            message: 'Network error',
          },
        ],
      };

      const mockRequest = Promise.resolve(mockResponse);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.PIS0106);
        expect((error as CustomError).category).toBe(ErrorCategory.NETWORK);
      }
    });

    it('should handle multiple errors and use first one', async () => {
      const mockResponse = {
        data: null,
        errors: [
          {
            key: MSBErrorCode.A05,
            message: 'System error',
          },
          {
            key: MSBErrorCode.PIS0103,
            message: 'Validation error',
          },
        ],
      };

      const mockRequest = Promise.resolve(mockResponse);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.A05);
      }
    });

    it('should handle error without key', async () => {
      const mockResponse = {
        data: null,
        errors: [
          {
            message: 'Error without key',
          },
        ],
      };

      const mockRequest = Promise.resolve(mockResponse);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.UNKNOWN_ERROR);
      }
    });
  });

  describe('Error Handling - Mapper Errors', () => {
    it('should handle mapper throwing CustomError', async () => {
      const mockResponse = {data: 'valid response'};
      const customError = new CustomError(
        'MAPPER_ERROR',
        ErrorCategory.SYSTEM,
        'Mapper Error',
        'Mapper failed to process data',
        false,
        [],
      );

      mockMapper.mockImplementation(() => {
        throw customError;
      });

      const mockRequest = Promise.resolve(mockResponse);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(customError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBe(customError);
        expect((error as CustomError).code).toBe('MAPPER_ERROR');
      }

      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error',
        customError,
      );
    });

    it('should handle mapper throwing generic error', async () => {
      const mockResponse = {data: 'valid response'};
      const genericError = new Error('Generic mapper error');

      mockMapper.mockImplementation(() => {
        throw genericError;
      });

      const mockRequest = Promise.resolve(mockResponse);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(CustomError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.NOT_VALID_DATA_FORMAT);
        expect((error as CustomError).category).toBe(ErrorCategory.UNKNOWN);
      }

      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error',
        genericError,
      );
    });

    it('should handle mapper returning null/undefined', async () => {
      const mockResponse = {data: 'valid response'};

      mockMapper.mockReturnValue(null);
      const mockRequest = Promise.resolve(mockResponse);

      const result = await handleData(mockRequest, mockMapper);
      expect(result).toBeNull();

      mockMapper.mockReturnValue(undefined);
      const mockRequest2 = Promise.resolve(mockResponse);

      const result2 = await handleData(mockRequest2, mockMapper);
      expect(result2).toBeUndefined();
    });
  });

  describe('Error Handling - Request Errors', () => {
    it('should handle request promise rejection with CustomError', async () => {
      const customError = new CustomError(
        'REQUEST_ERROR',
        ErrorCategory.NETWORK,
        'Request Error',
        'Request failed',
        true,
        [],
      );

      const mockRequest = Promise.reject(customError);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(customError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBe(customError);
      }

      expect(mockMapper).not.toHaveBeenCalled();
    });

    it('should handle request promise rejection with generic error', async () => {
      const genericError = new Error('Network request failed');
      const mockRequest = Promise.reject(genericError);

      await expect(handleData(mockRequest, mockMapper)).rejects.toThrow(CustomError);

      try {
        await handleData(mockRequest, mockMapper);
      } catch (error) {
        expect(error).toBeInstanceOf(CustomError);
        expect((error as CustomError).code).toBe(MSBErrorCode.NOT_VALID_DATA_FORMAT);
      }

      expect(console.log).toHaveBeenCalledWith(
        '❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error',
        genericError,
      );
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for mapped data', async () => {
      interface ResponseData {
        id: number;
        name: string;
      }

      interface MappedData {
        userId: number;
        fullName: string;
      }

      const mockResponse: ResponseData = {
        id: 1,
        name: 'John Doe',
      };

      const mappedData: MappedData = {
        userId: 1,
        fullName: 'John Doe',
      };

      const typedMapper = (response: ResponseData): MappedData => ({
        userId: response.id,
        fullName: response.name,
      });

      const mockRequest = Promise.resolve(mockResponse);

      const result: MappedData = await handleData(mockRequest, typedMapper);

      expect(result.userId).toBe(1);
      expect(result.fullName).toBe('John Doe');
    });

    it('should work with generic types', async () => {
      async function handleTypedData<TResponse, TMapped>(
        request: Promise<TResponse>,
        mapper: (response: TResponse) => TMapped,
      ): Promise<TMapped> {
        return handleData(request, mapper);
      }

      const stringResponse = 'test string';
      const numberMapper = (str: string) => str.length;

      const result = await handleTypedData(Promise.resolve(stringResponse), numberMapper);

      expect(result).toBe(11); // 'test string'.length
      expect(typeof result).toBe('number');
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle API response with nested data', async () => {
      const apiResponse = {
        status: 'success',
        data: {
          user: {
            id: 1,
            profile: {
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
            },
            preferences: {
              theme: 'dark',
              language: 'en',
            },
          },
        },
      };

      const mapper = (response: typeof apiResponse) => ({
        id: response.data.user.id,
        name: `${response.data.user.profile.firstName} ${response.data.user.profile.lastName}`,
        email: response.data.user.profile.email,
        settings: response.data.user.preferences,
      });

      const mockRequest = Promise.resolve(apiResponse);
      const result = await handleData(mockRequest, mapper);

      expect(result.id).toBe(1);
      expect(result.name).toBe('John Doe');
      expect(result.email).toBe('<EMAIL>');
      expect(result.settings.theme).toBe('dark');
    });

    it('should handle pagination response', async () => {
      const paginationResponse = {
        items: [
          {id: 1, title: 'Item 1'},
          {id: 2, title: 'Item 2'},
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 25,
          hasNext: true,
        },
      };

      const mapper = (response: typeof paginationResponse) => ({
        data: response.items,
        currentPage: response.pagination.page,
        totalItems: response.pagination.total,
        hasMorePages: response.pagination.hasNext,
      });

      const mockRequest = Promise.resolve(paginationResponse);
      const result = await handleData(mockRequest, mapper);

      expect(result.data).toHaveLength(2);
      expect(result.currentPage).toBe(1);
      expect(result.totalItems).toBe(25);
      expect(result.hasMorePages).toBe(true);
    });
  });
});
