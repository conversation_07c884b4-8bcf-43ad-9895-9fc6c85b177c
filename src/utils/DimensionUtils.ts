// import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Dimensions, Platform, StatusBar} from 'react-native';

const {height, width} = Dimensions.get('screen'); // Use 'screen' for better accuracy

const iPhoneXModels = [
  {width: 320, height: 480, models: ['iPhone 1', 'iPhone 3G', 'iPhone 3GS']},
  {width: 320, height: 568, models: ['iPhone 5', 'iPhone 5S', 'iPhone 5C', 'iPhone SE (2016)']},
  {
    width: 375,
    height: 667,
    models: ['iPhone 6', 'iPhone 6S', 'iPhone 7', 'iPhone 8', 'iPhone SE (2020)', 'iPhone SE (2022)'],
  },
  {width: 414, height: 736, models: ['iPhone 6 Plus', 'iPhone 6S Plus', 'iPhone 7 Plus', 'iPhone 8 Plus']},
  {width: 375, height: 812, models: ['iPhone X', 'iPhone XS', 'iPhone 11 Pro']},
  {width: 414, height: 896, models: ['iPhone XR', 'iPhone XS Max', 'iPhone 11', 'iPhone 11 Pro Max']},
  {width: 390, height: 844, models: ['iPhone 12', 'iPhone 12 Pro', 'iPhone 13', 'iPhone 13 Pro']},
  {width: 428, height: 926, models: ['iPhone 12 Pro Max', 'iPhone 13 Pro Max']},
  {width: 430, height: 932, models: ['iPhone 14 Pro Max', 'iPhone 15 Pro Max']},
  {width: 393, height: 852, models: ['iPhone 14 Pro', 'iPhone 15 Pro']},
  {width: 390, height: 844, models: ['iPhone 14', 'iPhone 15']},
  {width: 430, height: 932, models: ['iPhone 16 Pro Max']},
  {width: 402, height: 874, models: ['iPhone 16', 'iPhone 16 Pro']},
  {width: 430, height: 960, models: ['iPhone 16 Plus']},
];

const isIPhoneX = () =>
  Platform.OS === 'ios' &&
  !Platform.isPad &&
  !Platform.isTV &&
  iPhoneXModels.some(model => model.width === width && model.height === height);

const paddingTop = Platform.select({
  ios: isIPhoneX() ? 44 : 20,
  android: StatusBar.currentHeight || 24,
  default: 0,
});

const paddingBottom = Platform.select({
  ios: isIPhoneX() ? 34 : 20,
  android: 20,
  default: 0,
});

const getPaddingTopByDevice = () => {
  // return useSafeAreaInsets().top;
  return paddingTop;
};

const getPaddingBottomByDevice = () => {
  // return useSafeAreaInsets().bottom;
  return paddingBottom;
};
const getWindowWidth = () => {
  return Dimensions.get('window').width;
};

const getWindowHeight = () => {
  return Dimensions.get('window').height;
};

export default {
  getPaddingTopByDevice,
  getPaddingBottomByDevice,
  getWindowHeight,
  getWindowWidth,
};
