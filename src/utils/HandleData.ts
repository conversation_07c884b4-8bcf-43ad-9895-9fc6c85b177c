import {createError, CustomError} from '../core/MSBCustomError';
import {MSBErrorCode} from '../core/MSBErrorCode';

export const handleData = async <T>(request: Promise<any>, mapper: (response: any) => T): Promise<T> => {
  try {
    const response = await request;
    console.log('DATA:', response);
    if (!response || response?.errors) {
      console.log('❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response', response);
      throw createError(response?.errors?.[0]?.key);
    }
    console.log('✅✅✅✅✅✅✅✅✅✅✅Repository handleData Success', response);
    return mapper(response);
  } catch (error) {
    console.log('❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error', error);
    if (error instanceof CustomError) {
      throw error;
    }
    throw createError(MSBErrorCode.NOT_VALID_DATA_FORMAT);
  }
};
