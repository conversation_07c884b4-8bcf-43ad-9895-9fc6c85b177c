import {CustomError, ErrorAction} from '../core/MSBCustomError';
import {MSBErrorCode} from '../core/MSBErrorCode';
import {hostSharedModule} from 'msb-host-shared-module';
import {I18nKeys, translate} from '../locales/i18n';

/**
 * Action handler type - screens implement business logic
 */
export type ActionHandler = () => void | Promise<void>;

/**
 * Action handlers mapping - flexible cho screens
 */
export interface ActionHandlers {
  [actionType: string]: ActionHandler | undefined;
}

/**
 * Popup action definition - limited to 2 actions max
 */
export interface PopupAction {
  label: string;
  handler: () => void | Promise<void>;
  primary?: boolean;
}

/**
 * Enhanced popup options for hostSharedModule constraint
 */
export interface PopupOptions {
  title: string;
  content: string;
  iconType: 'ERROR' | 'WARNING';
  errorCode?: string;
  confirmAction?: PopupAction;
  cancelAction?: PopupAction;
}

/**
 * Show popup với hostSharedModule constraint (max 2 actions)
 */
export const showPopup = (options: PopupOptions): void => {
  const {title, content, iconType, errorCode, confirmAction, cancelAction} = options;

  console.log('showPopup==========', {title, content, iconType});

  // Build popup config for hostSharedModule
  const popupConfig: any = {
    title: translate(title as I18nKeys),
    content: translate(content as I18nKeys),
    iconType,
    ...(errorCode && {errorCode}),
  };

  // Add confirm action (primary action)
  if (confirmAction) {
    popupConfig.confirmBtnText =  translate(confirmAction.label as I18nKeys);
    popupConfig.onConfirm = confirmAction.handler;
  } else {
    // Default confirm action
    popupConfig.confirmBtnText = translate('close');
  }

  // Add cancel action (secondary action) if provided
  if (cancelAction) {
    popupConfig.cancelBtnText = translate(cancelAction.label as I18nKeys);
    popupConfig.onCancel = cancelAction.handler;
  }

  hostSharedModule.d.domainService?.showPopup(popupConfig);
};

/**
 * Map action to handler
 */
const mapActionToHandler = (
  action: ErrorAction,
  handlers?: ActionHandlers,
): {label: string; handler: ActionHandler; primary?: boolean} | null => {
  if (!handlers || !handlers[action.type]) {
    return null;
  }

  const handler = handlers[action.type];
  if (!handler) {
    return null;
  }

  return {
    label: action.label,
    handler,
    primary: action.primary,
  };
};

/**
 * Map error category to icon type
 */
const mapCategoryToIconType = (category: string): 'ERROR' | 'WARNING' => {
  switch (category) {
    case 'NETWORK':
    case 'API':
    case 'SYSTEM':
      return 'ERROR';
    case 'BUSINESS':
    case 'VALIDATION':
      return 'WARNING';
    default:
      return 'WARNING';
  }
};

/**
 * Show error popup với CustomError và ActionHandlers
 * Support null/undefined actions với default close action
 */
export const showErrorPopup = (error: CustomError, handlers?: ActionHandlers | null | undefined): void => {
  console.log('🔔 Showing error popup:', {
    code: error.code,
    title: error.title,
    category: error.category,
    retryable: error.retryable,
    actionsCount: error.actions?.length || 0,
  });

  // Handle null/undefined actions - default to close only
  if (!error.actions || error.actions.length === 0 || !handlers) {
    showPopup({
      title: error.title,
      content: error.userMessage,
      iconType: mapCategoryToIconType(error.category),
      confirmAction: {
        label: 'close',
        handler: () => {},
      },
    });
    return;
  }

  // Get primary and secondary actions (max 2)
  const primaryAction = error.getPrimaryAction();
  const secondaryAction = error.getSecondaryAction();

  // Map actions to handlers
  const mappedPrimary = primaryAction ? mapActionToHandler(primaryAction, handlers) : null;
  const mappedSecondary = secondaryAction ? mapActionToHandler(secondaryAction, handlers) : null;

  // Show popup based on available actions
  if (!mappedPrimary && !mappedSecondary) {
    // No handlers - show default close
    showPopup({
      title: error.title,
      content: error.userMessage,
      iconType: mapCategoryToIconType(error.category),
      confirmAction: {
        label: 'close',
        handler: () => {},
      },
    });
  } else if (mappedPrimary && !mappedSecondary) {
    // Single action
    showPopup({
      title: error.title,
      content: error.userMessage,
      iconType: mapCategoryToIconType(error.category),
      confirmAction: mappedPrimary,
    });
  } else {
    // Two actions (primary + secondary)
    showPopup({
      title: error.title,
      content: error.userMessage,
      iconType: mapCategoryToIconType(error.category),
      confirmAction: mappedPrimary || {
        label: 'close',
        handler: () => {},
      },
      cancelAction: mappedSecondary || undefined,
    });
  }
};

/**
 * Legacy showErrorPopup for backward compatibility
 */
export const showLegacyErrorPopup = (
  error?: CustomError | null | undefined,
  confirmAction?: PopupAction,
  cancelAction?: PopupAction,
): void => {
  showPopup({
    title: translate((error?.title as I18nKeys) || 'error.oops'),
    content: translate((error?.userMessage as I18nKeys) || 'error.errorOccurred'),
    iconType: 'ERROR',
    errorCode: translate('code') + (error?.code ?? MSBErrorCode.UNKNOWN_ERROR),
    confirmAction,
    cancelAction,
  });
};

/**
 * Show confirmation popup
 */
export const showConfirmationPopup = (
  title: string,
  content: string,
  onConfirm: () => void | Promise<void>,
  onCancel?: () => void | Promise<void>,
  iconType: 'ERROR' | 'WARNING' = 'WARNING',
): void => {
  showPopup({
    title,
    content,
    iconType,
    confirmAction: {
      label: 'Xác nhận',
      handler: onConfirm,
      primary: true,
    },
    cancelAction: onCancel
      ? {
          label: 'Hủy',
          handler: onCancel,
        }
      : undefined,
  });
};

/**
 * Show simple info popup với chỉ 1 action
 */
export const showInfoPopup = (
  title: string,
  content: string,
  buttonText: string = translate('close'),
  onConfirm?: () => void | Promise<void>,
  iconType: 'ERROR' | 'WARNING' = 'WARNING',
): void => {
  showPopup({
    title,
    content,
    iconType,
    confirmAction: {
      label: buttonText,
      handler: onConfirm || (() => {}),
    },
  });
};

/**
 * Show success popup
 */
export const showSuccessPopup = (
  title: string,
  content: string,
  buttonText: string = translate('close'),
  onConfirm?: () => void | Promise<void>,
): void => {
  showPopup({
    title,
    content,
    iconType: 'WARNING',
    confirmAction: {
      label: buttonText,
      handler: onConfirm || (() => {}),
    },
  });
};

/**
 * Show warning popup
 */
export const showWarningPopup = (
  title: string,
  content: string,
  buttonText: string = translate('close'),
  onConfirm?: () => void | Promise<void>,
): void => {
  showPopup({
    title,
    content,
    iconType: 'WARNING',
    confirmAction: {
      label: buttonText,
      handler: onConfirm || (() => {}),
    },
  });
};

/**
 * Helper functions for creating action handlers
 */
export const createActionHandlers = (handlers: Record<string, ActionHandler>): ActionHandlers => {
  return handlers;
};

export const combineActionHandlers = (...handlers: ActionHandlers[]): ActionHandlers => {
  return Object.assign({}, ...handlers);
};

// Backward compatibility
export const showCommonPopup = (error?: CustomError | null | undefined, onConfirm?: (() => void) | undefined) => {
  if (!error) {
    return;
  }

  showLegacyErrorPopup(error, onConfirm ? {label: translate('close'), handler: onConfirm} : undefined);
};
