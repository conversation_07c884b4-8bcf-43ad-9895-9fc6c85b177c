import {CommonActions, NavigationProp, ParamListBase, StackActions} from '@react-navigation/native';
import {debounce} from 'lodash';
import React from 'react';

export const navigationRef = React.createRef<NavigationProp<ParamListBase>>();
const DELAY_TIMER = 200;

const goBack = debounce(
  () => {
    navigationRef.current?.goBack();
  },
  DELAY_TIMER,
  {leading: true, trailing: false},
);

const pushScreen = debounce(
  (destination: string, data?: any) => {
    navigationRef.current?.dispatch(StackActions.push(destination, data));
  },
  DELAY_TIMER,
  {leading: true, trailing: false},
);

const replaceScreen = debounce(
  (destination: string, data?: any) => {
    navigationRef.current?.dispatch(StackActions.replace(destination, data));
  },
  DELAY_TIMER,
  {leading: true, trailing: false},
);

const resetScreen = debounce(
  (name: string) => {
    navigationRef.current?.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name}],
      }),
    );
  },
  DELAY_TIMER,
  {leading: true, trailing: false},
);

const navigateScreen = debounce(
  (destination: string, data?: any) => {
    navigationRef.current?.navigate(destination, data);
  },
  DELAY_TIMER,
  {leading: true, trailing: false},
);

export default {
  goBack,
  pushScreen,
  navigateScreen,
  replaceScreen,
  resetScreen,
};
