import {PaymentOrderRequest} from '../data/models/payment-order/PaymentOrderRequest';
import {GetBillDetailModel} from '../domain/entities/get-bill-detail/GetBillDetailModel';
import {ProviderModel} from '../domain/entities/provider-list/ProviderListModel';

export interface PaymentInfoModel {
  title: string;
  categoryName: string;
  billInfo?: GetBillDetailModel;
  paymentValidate?: PaymentOrderRequest;
  originatorAccount?: {
    identification?: string;
    name?: string;
    accountNo?: string;
    bankName?: string;
    bankCode?: string;
  };
  paymentResultType?: string;
  contractName?: string;
  provider?: ProviderModel | null;
  additionalInfo?: string;
}
