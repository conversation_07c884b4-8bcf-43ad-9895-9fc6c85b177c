import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {useLocale} from 'msb-communication-lib/dist/locales/LocaleContext';
import ScreenNames from '../commons/ScreenNames';
import PaymentHomePage from '../presentation/payment-home';
import PaymentBillScreen from '../presentation/payment-bill';
import PaymentConfirmScreen from '../presentation/payment-confirm';
import PaymentInfoScreen from '../presentation/payment-info';
import PaymentResultScreen from '../presentation/payment-result';
import {PaymentInfoModel} from './types';
import PaymentResultDetailScreen from '../presentation/payment-result-detail';
import PaymentPhoneScreen from '../presentation/payment-phone';
import SaveBillContactScreen from '../presentation/savecontact';
import {CategoryModel} from '../domain/entities/category-list/CategoryListModel';
import EditBillContactScreen from '../presentation/editcontact';
import BillDetailScreen from '../presentation/bill-detail';
import {AccountModel} from '../domain/entities/my-bill-contact-list/MyBillContactListModel';
import {IBillContact} from '../domain/entities/IBillContact';
const Stack = createNativeStackNavigator<PaymentStackParamList>();

export type MobilePaymentStackParamList = {
  [ScreenNames.PostpaidMobileScreen]: undefined;
  [ScreenNames.PostpaidMobileInfoScreen]: {
    phoneNumber: string;
    provider: string;
    amount: number;
  };
  [ScreenNames.PrepaidMobileScreen]: undefined;
};
export type PaymentStackParamList = {
  [ScreenNames.PaymentHomePage]: undefined;
  [ScreenNames.BillDetailScreen]: {
    account: AccountModel;
    contact: IBillContact;
  };
  [ScreenNames.PaymentBillScreen]: any;
  [ScreenNames.SaveBillContactScreen]: any;
  [ScreenNames.EditBillContactScreen]: any;

  [ScreenNames.PaymentConfirmScreen]: {
    paymentInfo: PaymentInfoModel;
    hasPeriod?: boolean;
  };
  [ScreenNames.PaymentInfoScreen]: {
    paymentInfo: PaymentInfoModel;
  };
  [ScreenNames.PaymentResultScreen]: {
    paymentInfo: PaymentInfoModel;
  };
  [ScreenNames.PaymentResultDetailScreen]: {
    paymentInfo: PaymentInfoModel;
  };
  SegmentStack: undefined;
  PaymentStack: undefined;
  BottomTabs: undefined;
  PaymentPhoneScreen: {
    category: CategoryModel;
  };
};

const PaymentStack = () => {
  const {locale} = useLocale();

  return (
    <Stack.Navigator screenOptions={{animation: 'slide_from_right'}} key={locale}>
      <Stack.Screen component={PaymentHomePage} name={ScreenNames.PaymentHomePage} options={{headerShown: false}} />
      <Stack.Screen component={BillDetailScreen} name={ScreenNames.BillDetailScreen} options={{headerShown: false}} />
      <Stack.Screen component={PaymentBillScreen} name={ScreenNames.PaymentBillScreen} options={{headerShown: false}} />
      <Stack.Screen component={PaymentInfoScreen} name={ScreenNames.PaymentInfoScreen} options={{headerShown: false}} />
      <Stack.Screen
        component={PaymentConfirmScreen}
        name={ScreenNames.PaymentConfirmScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        component={PaymentResultScreen}
        name={ScreenNames.PaymentResultScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        component={PaymentResultDetailScreen}
        name={ScreenNames.PaymentResultDetailScreen}
        options={{
          headerShown: false,
          animation: 'slide_from_bottom',
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        component={PaymentPhoneScreen}
        name={ScreenNames.PaymentPhoneScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        component={SaveBillContactScreen}
        name={ScreenNames.SaveBillContactScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        component={EditBillContactScreen}
        name={ScreenNames.EditBillContactScreen}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};

export default PaymentStack;
