import {PaymentOrderStatusModel} from '../entities/payment-order-status/PaymentOrderStatusModel';
import {PaymentOrderModel} from '../entities/payment-order/PaymentOrderModel';
import {BaseResponse} from '../../core/BaseResponse';
import {PaymentOrderRequest} from '../../data/models/payment-order/PaymentOrderRequest';
import {PaymentOrderStatusRequest} from '../../data/models/payment-order-status/PaymentOrderStatusRequest';

export interface IPaymentOrderRepository {
  paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderModel>>;
  paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusModel>>;
}
