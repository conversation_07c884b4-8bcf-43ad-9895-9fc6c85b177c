import {GetBillDetailModel} from '../entities/get-bill-detail/GetBillDetailModel';
import {GetBillDetailRequest} from '../../data/models/get-bill-detail/GetBillDetailRequest';
import {BillValidateModel} from '../entities/bill-validate/BillValidateModel';
import {MyBillListRequest} from '../../data/models/my-bill-list/MyBillListRequest';
import {ProviderListModel} from '../entities/provider-list/ProviderListModel';
import {ProviderListRequest} from '../../data/models/provider-list/ProviderListRequest';
import {CategoryListModel} from '../entities/category-list/CategoryListModel';
import {BaseResponse} from '../../core/BaseResponse';
import {BillValidateRequest} from '../../data/models/bill-validate/BillValidateRequest';
import {MyBillContactListModel} from '../entities/my-bill-contact-list/MyBillContactListModel';

export interface IBillPayRepository {
  categoryList(): Promise<BaseResponse<CategoryListModel>>;
  providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListModel>>;
  myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillContactListModel>>;
  getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailModel>>;
  billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateModel>>;
}
