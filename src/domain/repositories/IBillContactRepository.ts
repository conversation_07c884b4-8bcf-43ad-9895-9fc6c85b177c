import {BillHistoryModel} from '../entities/get-my-bill-history-list/GetMyBillHistoryListModel';
import {GetMyBillHistoryListRequest} from '../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';
import {GetMyBillContactRecentListModel} from '../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';
import {GetMyBillContactRecentListRequest} from '../../data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';
import {MyBillContactListModel} from '../entities/my-bill-contact-list/MyBillContactListModel';
import {EditBillContactModel} from '../entities/edit-bill-contact/EditBillContactModel';
import {EditBillContactRequest} from '../../data/models/edit-bill-contact/EditBillContactRequest';
import {DeleteBillContactModel} from '../entities/delete-bill-contact/DeleteBillContactModel';
import {DeleteBillContactRequest} from '../../data/models/delete-bill-contact/DeleteBillContactRequest';
import {SaveBillContactModel} from '../entities/save-bill-contact/SaveBillContactModel';
import {SaveBillContactRequest} from '../../data/models/save-bill-contact/SaveBillContactRequest';
import {BaseResponse} from '../../core/BaseResponse';

export interface IBillContactRepository {
  saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactModel>>;
  deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactModel>>;
  editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactModel>>;
  myBillContactList(): Promise<BaseResponse<MyBillContactListModel>>;
  getMyBillContactRecentList(
    request: GetMyBillContactRecentListRequest,
  ): Promise<BaseResponse<GetMyBillContactRecentListModel>>;
  getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<BillHistoryModel>>;
}
