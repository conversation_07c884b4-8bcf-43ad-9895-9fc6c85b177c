import {IBillContact} from '../IBillContact';

export class GetBillDetailModel implements IBillContact {
  constructor(
    public billCode?: string,
    public service?: BillDetailServiceModel,
    public queryRef?: string,
    public customerInfo?: BillDetailCustomerInfoModel,
    public billList?: BillDetailBillModel[],
    public partnerRespCode?: string,
    public tranSeqCount?: number,
    public partnerRespDesc?: string,
    public partnerTraceSeq?: string,
    public result?: string,
    public extendData?: any,
    public paymentRule?: number,
    public payableAmount?: string | null,
    public favoriteStatus?: 'ACTIVE' | 'INACTIVE',
    public reminderStatus?: 'ACTIVE' | 'INACTIVE',
  ) {}
  getServiceCode(): string {
    return '';
  }

  getPayableAmount(): string {
    return (
      this.payableAmount ??
      this.billList
        ?.map(e => e.amount)
        .reduce((a, b) => (a ?? 0) + (b ?? 0), 0)
        ?.toString() ??
      '0'
    );
  }
  getFavoriteStatus(): 'ACTIVE' | 'INACTIVE' {
    return this.favoriteStatus ?? 'INACTIVE';
  }
  getReminderStatus(): 'ACTIVE' | 'INACTIVE' {
    return this.reminderStatus ?? ((this.billList?.length ?? 0) > 0 ? 'ACTIVE' : 'INACTIVE') ?? 'INACTIVE';
  }

  getPartnerCode(): string {
    return '';
  }

  setBillList(billList?: any[]) {
    this.billList = billList?.map(e => e);
  }
  getId(): string {
    return this.billCode || '';
  }

  getCustomerName(): string {
    return this.billList?.[0].custName || '';
  }
  getSubtitle(): string {
    return this.service?.code || '';
  }
  getIcon(): string {
    return '';
  }
  isPair(): boolean {
    return false;
  }
  getType(): string {
    return '';
  }
  getSearchContent(): string {
    return this.billCode || '';
  }
  isEditable(): boolean {
    return false;
  }
  getBillCode?(): string {
    return '';
  }

  getCategoryCode?(): string {
    return '';
  }

  getExternalId(): string {
    return '';
  }

  //TODO: enhance this method to return the correct value after the backend is ready
  isTopup(): boolean {
    return true;
  }
}

export class BillDetailServiceModel {
  constructor(public code?: string) {}
}

export class BillDetailCustomerInfoModel {
  constructor(public cif?: string, public phone?: any, public acct?: any, public name?: string, public address?: any) {}
}

export class BillDetailBillModel {
  constructor(
    public id?: string,
    public no?: any,
    public amount?: number,
    public code?: string,
    public custCode?: any,
    public custName?: string,
    public period?: any,
    public fee?: any,
    public custAddress?: any,
  ) {}
}
