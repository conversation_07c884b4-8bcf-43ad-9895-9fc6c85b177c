// import Images from '../../../assets/images/Images';

export type ProviderListModel = ProviderModel[];

export class ProviderModel {
  constructor(
    public id?: number,
    public serviceCode?: string,
    public categoryCode?: string,
    public subGroupId?: number,
    public subgroupNameVn?: string,
    public subgroupNameEn?: string,
    public partnerCode?: string,
    public partnerName?: string,
    public autoBillSupport?: number,
    public voucherSupport?: number,
    public phoneRequired?: number,
    public isRecommend?: number,
    public partnerType?: number,
    public payFee?: number,
    public type?: number,
    public paymentSupport?: number,
    public description?: string,
  ) {}

  getName(): string {
    return this.subgroupNameVn || this.description || '';
  }

  getIconName(): string {
    return this.subGroupId?.toString() ?? '';
  }

  isTopup(): boolean {
    return this.type === 2;
  }

  setPartnerName(name: string): void {
    this.partnerName = name;
  }

  isViettelBill(): boolean {
    return this.subgroupNameVn?.toLocaleLowerCase().includes('viettel') || false;
  }
}
