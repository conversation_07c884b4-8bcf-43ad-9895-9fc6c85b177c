export interface BillHistoryData {
  id: string;
  billCode: string;
  category: string;
  subGroupId?: string;
  customerName?: string;
  totalAmount: number;
  period?: string;
  paymentDate: string;
  accountNumber: string;
  coreRef?: string;
  content?: string;
  serviceCode: string;
  arrangementId: string;
  paymentOrderId: string;
  cifNo: string;
  creditDebitIndicator?: 'CRDT' | 'DBIT';
  transactionAmountCurrency?: {
    amount: string;
    currencyCode: string;
  };
  creationTime: string;
  counterPartyName?: string;
  description?: string;
}

export interface BillData {
  id: string;
  transName?: string;
  content?: string;
  amount: string;
  transDate: string;
  creationDate: string;
}

export interface BillHistoryDTO {
  title: string;
  data: BillData[];
}

export interface BillHistoryModel {
  billHistoryDTO: BillHistoryDTO[];
  billHistory: BillHistoryData[];
}

export class GetMyBillHistoryListModel {
  constructor(
    public id: string,
    public billCode: string,
    public category: string,
    public subGroupId?: string,
    public customerName?: string,
    public totalAmount?: number,
    public period?: string,
    public paymentDate?: string,
    public accountNumber?: string,
    public coreRef?: string,
    public serviceCode?: string,
    public arrangementId?: string,
    public paymentOrderId?: string,
    public cifNo?: string,
  ) {}
}
