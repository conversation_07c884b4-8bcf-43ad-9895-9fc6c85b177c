import {IBillContact} from '../IBillContact';

export type MyBillContactListModel = MyBillContactModel[];

export class MyBillContactModel implements IBillContact {
  constructor(
    public id?: string | null,
    public name?: string | null,
    public alias?: string | null,
    public category?: string | null,
    public activeStatus?: string | null,
    public accessContextScope?: string | null,
    public accounts: AccountModel[] = [],
    public payableAmount?: string | null,
    public favoriteStatus?: 'ACTIVE' | 'INACTIVE',
    public reminderStatus?: 'ACTIVE' | 'INACTIVE',
    public serviceCode?: string | null,
  ) {}
  getServiceCode(): string {
    return this.serviceCode ?? '';
  }

  getPartnerCode(): string {
    return '';
  }

  getCustomerName(): string {
    return this.alias ?? this.name ?? '';
  }

  getId(): string {
    return this.id || '';
  }

  getTitle(): string {
    return this?.name || '';
  }
  getSubtitle(): string {
    return this.accounts?.[0].bankName ?? '';
  }
  getIcon(): string {
    return this.accounts?.[0].bankCode ?? '';
  }
  getExternalId(): string {
    return this.accounts?.[0].externalId ?? '';
  }
  isPair(): boolean {
    return false;
  }
  getType(): string {
    return '';
  }
  getSearchContent(): string {
    return (
      (this.alias?.toLowerCase() ?? '') +
      (this.accounts?.[0]?.bankName?.toLowerCase() ?? '') +
      (this.accounts?.[0]?.accountNumber?.toLowerCase() ?? '') +
      (this.accounts?.[0]?.bankCode?.toLowerCase() ?? '')
    );
  }
  isEditable(): boolean {
    return false;
  }
  getBillCode?(): string {
    return this.accounts?.[0].accountNumber ?? '';
  }

  getCategoryCode?(): string {
    return this.accounts?.[0].accountType ?? '';
  }

  getPayableAmount(): string {
    return this.payableAmount ?? '';
  }
  getFavoriteStatus(): 'ACTIVE' | 'INACTIVE' {
    return this.favoriteStatus ?? 'INACTIVE';
  }
  getReminderStatus(): 'ACTIVE' | 'INACTIVE' {
    return this.reminderStatus ?? 'INACTIVE';
  }
}

export class AccountModel {
  constructor(
    public bankName?: string | null,
    public accountNumber?: string | null,
    public bankCode?: string | null,
    public accountType?: string | null,
    public externalId?: string | null,
    public bankPostCode?: string | null,
  ) {}
}
