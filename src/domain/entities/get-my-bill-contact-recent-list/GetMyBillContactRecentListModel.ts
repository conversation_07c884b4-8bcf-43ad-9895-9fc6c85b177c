import {IBillContact} from '../IBillContact';

export type GetMyBillContactRecentListModel = GetMyBillContactRecentModel[];

export class GetMyBillContactRecentModel implements IBillContact {
  constructor(
    public id?: string,
    public billCode?: string,
    public category?: string,
    public subGroupId?: string,
    public customerName?: string,
    public totalAmount?: number,
    public period?: string,
    public paymentDate?: string,
    public accountNumber?: string,
    public coreRef?: string,
    public serviceCode?: string,
    public arrangementId?: string,
    public paymentOrderId?: string,
    public cifNo?: string,
  ) {}

  //TODO: enhance this method to return the correct value after the backend is ready
  isTopup(): boolean {
    return true;
  }

  getServiceCode(): string {
    return this.serviceCode ?? '';
  }

  getPayableAmount(): string {
    return this.totalAmount?.toString() ?? '0';
  }
  getFavoriteStatus(): 'ACTIVE' | 'INACTIVE' {
    return 'INACTIVE';
  }
  getReminderStatus(): 'ACTIVE' | 'INACTIVE' {
    return 'INACTIVE';
  }

  getPartnerCode(): string {
    return '';
  }

  setBillList(billList?: any[]) {}
  getId(): string {
    return this.id ?? '';
  }

  getCustomerName(): string {
    return this.customerName ?? '';
  }
  getSubtitle(): string {
    return this.category ?? '';
  }

  getExternalId(): string {
    return this.subGroupId ?? '';
  }
  getIcon(): string {
    return '';
  }
  isPair(): boolean {
    return false;
  }
  getType(): string {
    return '';
  }
  getSearchContent(): string {
    return '';
  }
  isEditable(): boolean {
    return false;
  }
  getBillCode?(): string {
    return this.billCode ?? '';
  }

  getCategoryCode?(): string {
    return '';
  }
}
