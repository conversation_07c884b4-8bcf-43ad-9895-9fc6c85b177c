export interface IBillContact {
  getId: () => string;
  getExternalId: () => string;
  getPartnerCode: () => string;
  getCustomerName: () => string;
  getSubtitle: () => string;
  getIcon: () => string;
  isPair: () => boolean;
  getType: () => string;

  getSearchContent: () => string;
  isEditable: () => boolean;
  getBillCode?: () => string;
  getCategoryCode?: () => string;
  getPayableAmount: () => string;
  getFavoriteStatus: () => 'ACTIVE' | 'INACTIVE';
  getReminderStatus: () => 'ACTIVE' | 'INACTIVE';
  getServiceCode: () => string;
}
