export interface BillValidateModel {
  id: string;
  originatorAccount: OriginatorAccountModel;
  instructionPriority: string;
  requestedExecutionDate: string;
  paymentMode: string;
  paymentType: string;
  transferTransactionInformation: TransferTransactionInformationModel;
  originator: OriginatorModel;
  totalAmount: CurrencyAmountModel;
  isIntraLegalEntityPaymentOrder: boolean;
  canApprove: boolean;
  finalApprover: boolean;
}

export interface OriginatorAccountModel {
  arrangementId: string;
  externalArrangementId: string;
  identification: AccountIdentificationModel;
}

export interface AccountIdentificationModel {
  identification: string;
  schemeName: string;
}

export interface TransferTransactionInformationModel {
  counterparty: CounterpartyModel;
  counterpartyAccount: AccountIdentificationModel;
  instructedAmount: CurrencyAmountModel;
  additions: TransferAdditionsModel;
}

export interface CounterpartyModel {
  name: string;
  role: string;
}

export interface CurrencyAmountModel {
  amount: string;
  currencyCode: string;
}

export interface TransferAdditionsModel {
  bpQueryRef: string;
  bpBillList: string; // Hoặc: BillItemModel[] nếu parse JSON
  bpSummary: string; // Hoặc: BpSummaryModel nếu parse JSON
  bpServiceCode: string;
  cifNo: string;
  bpCategory: string;
  bpAccountingNumber: string;
}

export interface OriginatorModel {
  name: string;
  role: string;
  postalAddress: Record<string, any>;
}
