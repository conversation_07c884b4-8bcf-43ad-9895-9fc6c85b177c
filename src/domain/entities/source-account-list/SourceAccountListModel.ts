export interface SourceAccountListModel {
  totalCount: number;
  data: SourceAccountModel[];
}

export interface SourceAccountUserPreferencesModel {
  arrangementId?: string;
  alias?: string;
  visible?: boolean | null;
  favorite?: boolean;
  additions?: any | null;
}

export interface SourceAccountProductKindModel {
  externalKindId: string;
  kindName: string;
  kindUri: string;
  expectsChildren: boolean;
  additions: any | null;
}

export interface SourceAccountProductModel {
  id: string;
  translations: any[];
  additions: any | null;
  externalId: string;
  externalTypeId: string | null;
  typeName: string;
  productKind: SourceAccountProductKindModel;
}

export interface SourceAccountModel {
  id: string;
  productKindName: string;
  legalEntityIds: string[];
  productId: string;
  productTypeName: string;
  externalProductId: string;
  externalArrangementId: string;
  userPreferences?: SourceAccountUserPreferencesModel;
  product: SourceAccountProductModel;
  state?: any | null;
  parentId?: string | null;
  subscriptions?: any | null;
  isDefault: string;
  cifNo: string;
  virtualAccountInfos?: any[];
  additions?: any | null;
  name: string;
  bookedBalance?: number | null;
  availableBalance?: number | null;
  creditLimit?: number | null;
  currency: string;
  externalTransferAllowed?: boolean | null;
  urgentTransferAllowed?: boolean | null;
  accountOpeningDate?: string | null;
  accountHolderNames?: string | null;
  bankAlias?: string;
  BBAN?: string;
  IBAN?: string | null;
  BIC?: string | null;
  amountQR?: string | null;
  contentQR?: string | null;
}
