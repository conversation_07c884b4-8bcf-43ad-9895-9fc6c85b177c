/**
 * Unit Tests for ValidateUseCase
 * Testing UseCase with request parameters and business validation logic
 */

import {ValidateUseCase} from '../ValidateUseCase';
import {IPaymentRepository} from '../../../repositories/IPaymentRepository';
import {ValidateModel} from '../../../entities/validate/ValidateModel';
import {ValidateRequest} from '../../../../data/models/validate/ValidateRequest';
import {ResultState} from '../../../../core/ResultState';
import {CustomError, ErrorCategory} from '../../../../core/MSBCustomError';
import {MSBErrorCode} from '../../../../core/MSBErrorCode';
import {ExecutionHandler} from '../../../../utils/ExcecutionHandler';
import {
  mockPaymentValidationRequest,
  mockPaymentValidationResult,
  mockPaymentValidationUseCaseResponse,
  createMockPaymentRepository,
  mockExecutionHandlerSuccess,
  mockExecutionHandlerError,
} from '../../../../__tests__/mocks';

// Mock ExecutionHandler
jest.mock('../../../../utils/ExcecutionHandler', () => ({
  ExecutionHandler: {
    execute: jest.fn(),
  },
}));

describe('ValidateUseCase', () => {
  let useCase: ValidateUseCase;
  let mockRepository: jest.Mocked<IPaymentRepository>;
  let mockExecutionHandler: jest.MockedFunction<typeof ExecutionHandler.execute>;

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      validate: jest.fn(),
    };

    // Create use case instance
    useCase = new ValidateUseCase(mockRepository);

    // Setup ExecutionHandler mock
    mockExecutionHandler = ExecutionHandler.execute as jest.MockedFunction<typeof ExecutionHandler.execute>;

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should create use case with repository dependency', () => {
      expect(useCase).toBeInstanceOf(ValidateUseCase);
      expect(useCase['repository']).toBe(mockRepository);
    });

    it('should implement dependency injection pattern', () => {
      const anotherMockRepository = {
        validate: jest.fn(),
      } as jest.Mocked<IPaymentRepository>;

      const anotherUseCase = new ValidateUseCase(anotherMockRepository);
      expect(anotherUseCase['repository']).toBe(anotherMockRepository);
      expect(anotherUseCase['repository']).not.toBe(mockRepository);
    });
  });

  describe('execute', () => {
    it('should call ExecutionHandler.execute with repository function and request', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
        paymentType: 'TRANSFER',
        beneficiaryName: 'John Doe',
      };

      const mockValidateData: ValidateModel = {
        isValid: true,
        transactionId: 'TXN_123456',
        fee: 5000,
        exchangeRate: 1,
        estimatedTime: '2-3 minutes',
        validationDetails: {
          accountStatus: 'ACTIVE',
          availableBalance: 500000,
          dailyLimit: 1000000,
          remainingLimit: 900000,
        },
      };

      const mockResult: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockValidateData,
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute(mockRequest);

      expect(mockExecutionHandler).toHaveBeenCalledTimes(1);
      expect(mockExecutionHandler).toHaveBeenCalledWith(expect.any(Function));
      expect(result).toBe(mockResult);
      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.isValid).toBe(true);
        expect(result.data?.transactionId).toBe('TXN_123456');
        expect(result.data?.fee).toBe(5000);
      }
    });

    it('should pass repository.validate function with request to ExecutionHandler', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 250000,
        currency: 'VND',
        paymentType: 'BILL_PAYMENT',
        billCode: 'ELEC_001',
      };

      const mockResult: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: {isValid: true, transactionId: 'TXN_789', fee: 0},
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      await useCase.execute(mockRequest);

      // Verify that the function passed to ExecutionHandler calls repository.validate with correct request
      const passedFunction = mockExecutionHandler.mock.calls[0][0];

      // Execute the passed function to verify it calls repository.validate
      await passedFunction();
      expect(mockRepository.validate).toHaveBeenCalledTimes(1);
      expect(mockRepository.validate).toHaveBeenCalledWith(mockRequest);
    });

    it('should handle successful validation with complex response', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '****************',
        amount: 1500000,
        currency: 'VND',
        paymentType: 'INTERNATIONAL_TRANSFER',
        beneficiaryName: 'Jane Smith',
        beneficiaryBank: 'SWIFT_BANK_001',
        purpose: 'Business payment',
      };

      const mockComplexData: ValidateModel = {
        isValid: true,
        transactionId: 'INTL_TXN_456789',
        fee: 50000,
        exchangeRate: 24500,
        estimatedTime: '1-2 business days',
        validationDetails: {
          accountStatus: 'ACTIVE',
          availableBalance: ********,
          dailyLimit: 5000000,
          remainingLimit: 3500000,
          internationalTransferEnabled: true,
          complianceChecks: {
            amlStatus: 'PASSED',
            sanctionCheck: 'CLEAR',
            riskScore: 'LOW',
          },
        },
        warnings: [
          'High amount transaction requires additional verification',
          'International transfer may take longer during weekends',
        ],
        requiredDocuments: ['Invoice or contract', 'Purpose of payment declaration'],
      };

      const mockResult: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockComplexData,
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute(mockRequest);

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.isValid).toBe(true);
        expect(result.data?.validationDetails?.complianceChecks?.amlStatus).toBe('PASSED');
        expect(result.data?.warnings).toHaveLength(2);
        expect(result.data?.requiredDocuments).toHaveLength(2);
      }
    });

    it('should handle validation failure', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: ********, // Exceeds limit
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const mockFailedData: ValidateModel = {
        isValid: false,
        transactionId: null,
        fee: 0,
        exchangeRate: 1,
        estimatedTime: null,
        validationDetails: {
          accountStatus: 'INACTIVE',
          availableBalance: 0,
          dailyLimit: 1000000,
          remainingLimit: 0,
        },
        errors: ['Account is inactive', 'Amount exceeds daily limit', 'Insufficient balance'],
      };

      const mockResult: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: mockFailedData,
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute(mockRequest);

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.isValid).toBe(false);
        expect(result.data?.errors).toHaveLength(3);
        expect(result.data?.transactionId).toBeNull();
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle validation business errors', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: 'INVALID_ACCOUNT',
        amount: 100000,
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const validationError = new CustomError(
        MSBErrorCode.PIS0103,
        ErrorCategory.VALIDATION,
        'Validation Error',
        'Invalid account number format',
        false,
        [
          {type: 'EDIT_INPUT', label: 'Edit Account Number', primary: true},
          {type: 'CLEAR_FORM', label: 'Clear Form'},
        ],
      );

      const mockErrorResult: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: validationError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(validationError);
        expect(result.error.code).toBe(MSBErrorCode.PIS0103);
        expect(result.error.category).toBe(ErrorCategory.VALIDATION);
        expect(result.error.retryable).toBe(false);
        expect(result.error.actions).toHaveLength(2);
      }
    });

    it('should handle network errors during validation', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const networkError = new CustomError(
        MSBErrorCode.PIS0106,
        ErrorCategory.NETWORK,
        'Network Error',
        'Unable to connect to validation service',
        true,
        [
          {type: 'RETRY', label: 'Retry Validation', primary: true},
          {type: 'CHECK_CONNECTION', label: 'Check Connection'},
        ],
      );

      const mockErrorResult: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: networkError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error.code).toBe(MSBErrorCode.PIS0106);
        expect(result.error.category).toBe(ErrorCategory.NETWORK);
        expect(result.error.retryable).toBe(true);
      }
    });

    it('should handle business logic errors', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: *********, // Very high amount
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const businessError = new CustomError(
        MSBErrorCode.PIS0101,
        ErrorCategory.BUSINESS,
        'Business Rule Violation',
        'Transaction amount exceeds maximum allowed limit',
        false,
        [
          {type: 'REDUCE_AMOUNT', label: 'Reduce Amount', primary: true},
          {type: 'CONTACT_SUPPORT', label: 'Contact Support'},
        ],
      );

      const mockErrorResult: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: businessError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error.code).toBe(MSBErrorCode.PIS0101);
        expect(result.error.category).toBe(ErrorCategory.BUSINESS);
        expect(result.error.retryable).toBe(false);
      }
    });

    it('should handle system errors', async () => {
      const mockRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const systemError = new CustomError(
        MSBErrorCode.A05,
        ErrorCategory.SYSTEM,
        'System Error',
        'Validation service temporarily unavailable',
        true,
        [
          {type: 'RETRY', label: 'Try Again', primary: true},
          {type: 'CONTACT_SUPPORT', label: 'Contact Support'},
        ],
      );

      const mockErrorResult: ResultState<ValidateModel> = {
        status: 'ERROR',
        error: systemError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute(mockRequest);

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error.code).toBe(MSBErrorCode.A05);
        expect(result.error.category).toBe(ErrorCategory.SYSTEM);
        expect(result.error.retryable).toBe(true);
      }
    });
  });

  describe('Request Parameter Validation', () => {
    it('should handle different payment types', async () => {
      const transferRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const billPaymentRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 50000,
        currency: 'VND',
        paymentType: 'BILL_PAYMENT',
        billCode: 'ELEC_001',
      };

      const internationalRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 1000000,
        currency: 'USD',
        paymentType: 'INTERNATIONAL_TRANSFER',
        beneficiaryBank: 'SWIFT_BANK',
      };

      mockExecutionHandler.mockResolvedValue({
        status: 'SUCCESS',
        data: {isValid: true, transactionId: 'TXN_001', fee: 0},
      });

      await useCase.execute(transferRequest);
      await useCase.execute(billPaymentRequest);
      await useCase.execute(internationalRequest);

      expect(mockExecutionHandler).toHaveBeenCalledTimes(3);

      // Verify each request was passed correctly
      const calls = mockExecutionHandler.mock.calls;
      for (let i = 0; i < calls.length; i++) {
        const passedFunction = calls[i][0];
        await passedFunction(); // This should call repository.validate
      }

      expect(mockRepository.validate).toHaveBeenCalledTimes(3);
      expect(mockRepository.validate).toHaveBeenNthCalledWith(1, transferRequest);
      expect(mockRepository.validate).toHaveBeenNthCalledWith(2, billPaymentRequest);
      expect(mockRepository.validate).toHaveBeenNthCalledWith(3, internationalRequest);
    });

    it('should handle different currencies', async () => {
      const vndRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 1000000,
        currency: 'VND',
        paymentType: 'TRANSFER',
      };

      const usdRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 100,
        currency: 'USD',
        paymentType: 'INTERNATIONAL_TRANSFER',
      };

      mockExecutionHandler.mockResolvedValue({
        status: 'SUCCESS',
        data: {isValid: true, transactionId: 'TXN_001', fee: 0},
      });

      await useCase.execute(vndRequest);
      await useCase.execute(usdRequest);

      // Verify each request was passed correctly by executing the passed functions
      const calls = mockExecutionHandler.mock.calls;
      for (let i = 0; i < calls.length; i++) {
        const passedFunction = calls[i][0];
        await passedFunction(); // This should call repository.validate
      }

      expect(mockRepository.validate).toHaveBeenCalledTimes(2);
      expect(mockRepository.validate).toHaveBeenNthCalledWith(1, vndRequest);
      expect(mockRepository.validate).toHaveBeenNthCalledWith(2, usdRequest);
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for ValidateRequest and ValidateModel', async () => {
      const typedRequest: ValidateRequest = {
        accountNumber: '**********',
        amount: 100000,
        currency: 'VND',
        paymentType: 'TRANSFER',
        beneficiaryName: 'Test User',
      };

      const typedResult: ResultState<ValidateModel> = {
        status: 'SUCCESS',
        data: {
          isValid: true,
          transactionId: 'TXN_123',
          fee: 5000,
          exchangeRate: 1,
          estimatedTime: '2-3 minutes',
        },
      };

      mockExecutionHandler.mockResolvedValue(typedResult);

      const result: ResultState<ValidateModel> = await useCase.execute(typedRequest);

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        // TypeScript should enforce ValidateModel structure
        expect(typeof result.data?.isValid).toBe('boolean');
        expect(typeof result.data?.fee).toBe('number');
        expect(typeof result.data?.transactionId).toBe('string');
      }
    });

    it('should work with generic ResultState type', async () => {
      // This test ensures proper TypeScript compilation
      const useCase: ValidateUseCase = new ValidateUseCase(mockRepository);
      const executeMethod: (request: ValidateRequest) => Promise<ResultState<ValidateModel>> =
        useCase.execute.bind(useCase);

      expect(typeof executeMethod).toBe('function');
    });
  });

  describe('Clean Architecture Compliance', () => {
    it('should depend only on repository interface, not implementation', () => {
      // This test ensures dependency inversion principle
      expect(useCase['repository']).toBe(mockRepository);

      // UseCase should work with any implementation of IPaymentRepository
      const anotherRepository: IPaymentRepository = {
        validate: jest.fn(),
      };

      const anotherUseCase = new ValidateUseCase(anotherRepository);
      expect(anotherUseCase['repository']).toBe(anotherRepository);
    });

    it('should encapsulate business logic in execute method', () => {
      // UseCase should have single responsibility: execute business logic
      const publicMethods = Object.getOwnPropertyNames(ValidateUseCase.prototype).filter(
        method => method !== 'constructor' && !method.startsWith('_'),
      );

      expect(publicMethods).toEqual(['execute']);
    });

    it('should not have direct dependencies on external frameworks', () => {
      // UseCase should not import React Native, UI components, or external libraries
      expect(useCase['repository']).toBeDefined();

      // Should only depend on domain interfaces and core utilities
      const dependencies = Object.keys(useCase);
      expect(dependencies).toEqual(['repository']);
    });
  });
});
