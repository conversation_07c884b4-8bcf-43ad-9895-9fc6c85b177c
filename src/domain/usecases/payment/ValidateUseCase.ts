import {IPaymentRepository} from '../../repositories/IPaymentRepository';
import {ValidateModel} from '../../entities/validate/ValidateModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {ValidateRequest} from '../../../data/models/validate/ValidateRequest';
export class ValidateUseCase {
  private repository: IPaymentRepository;

  constructor(repository: IPaymentRepository) {
    this.repository = repository;
  }

  public async execute(request: ValidateRequest): Promise<ResultState<ValidateModel>> {
    // call this.repository.validate(...)
    return ExecutionHandler.execute(() => this.repository.validate(request));
  }
}
