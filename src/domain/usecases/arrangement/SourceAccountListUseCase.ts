import {IArrangementRepository} from '../../repositories/IArrangementRepository';
import {SourceAccountListModel} from '../../entities/source-account-list/SourceAccountListModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {SourceAccountListRequest} from '../../../data/models/source-account-list/SourceAccountListRequest';
export class SourceAccountListUseCase {
  private repository: IArrangementRepository;

  constructor(repository: IArrangementRepository) {
    this.repository = repository;
  }

  public async execute(request?: SourceAccountListRequest): Promise<ResultState<SourceAccountListModel>> {
    const params: SourceAccountListRequest = {
      externalStateIds: ['ACTIVE'],
      externalProductKindIds: ['kind1', 'kind10'],
      currency: 'VND',
    };
    return ExecutionHandler.execute(() => this.repository.sourceAccountList(request ?? params));
  }
}
