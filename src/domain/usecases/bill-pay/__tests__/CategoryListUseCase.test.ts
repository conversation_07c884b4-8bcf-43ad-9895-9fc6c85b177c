/**
 * Unit Tests for CategoryListUseCase
 * Testing UseCase layer implementation with Clean Architecture
 */

import {CategoryListUseCase} from '../CategoryListUseCase';
import {IBillPayRepository} from '../../../repositories/IBillPayRepository';
import {CategoryListModel} from '../../../entities/category-list/CategoryListModel';
import {ResultState} from '../../../../core/ResultState';
import {CustomError, ErrorCategory} from '../../../../core/MSBCustomError';
import {MSBErrorCode} from '../../../../core/MSBErrorCode';
import {ExecutionHandler} from '../../../../utils/ExcecutionHandler';
import {
  mockCategoryEntity,
  mockCategoryList,
  mockCategoryListUseCaseResponse,
  createMockBillPayRepository,
  mockExecutionHandlerSuccess,
  mockExecutionHandlerError,
} from '../../../../__tests__/mocks';

// Mock ExecutionHandler
jest.mock('../../../../utils/ExcecutionHandler', () => ({
  ExecutionHandler: {
    execute: jest.fn(),
  },
}));

describe('CategoryListUseCase', () => {
  let useCase: CategoryListUseCase;
  let mockRepository: jest.Mocked<IBillPayRepository>;
  let mockExecutionHandler: jest.MockedFunction<typeof ExecutionHandler.execute>;

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      categoryList: jest.fn(),
      providerList: jest.fn(),
      myBillList: jest.fn(),
      getBillDetail: jest.fn(),
      billValidate: jest.fn(),
    };

    // Create use case instance
    useCase = new CategoryListUseCase(mockRepository);

    // Setup ExecutionHandler mock
    mockExecutionHandler = ExecutionHandler.execute as jest.MockedFunction<typeof ExecutionHandler.execute>;

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should create use case with repository dependency', () => {
      expect(useCase).toBeInstanceOf(CategoryListUseCase);
      expect(useCase['repository']).toBe(mockRepository);
    });

    it('should implement dependency injection pattern', () => {
      const anotherMockRepository = {
        categoryList: jest.fn(),
        providerList: jest.fn(),
        myBillList: jest.fn(),
        getBillDetail: jest.fn(),
        billValidate: jest.fn(),
      } as jest.Mocked<IBillPayRepository>;

      const anotherUseCase = new CategoryListUseCase(anotherMockRepository);
      expect(anotherUseCase['repository']).toBe(anotherMockRepository);
      expect(anotherUseCase['repository']).not.toBe(mockRepository);
    });
  });

  describe('execute', () => {
    it('should call ExecutionHandler.execute with repository function', async () => {
      const mockCategoryData: CategoryListModel = {
        categories: [
          {
            id: '1',
            name: 'Electricity',
            icon: 'electricity-icon',
            description: 'Electric bill payments',
          },
          {
            id: '2',
            name: 'Water',
            icon: 'water-icon',
            description: 'Water bill payments',
          },
        ],
      };

      const mockResult: ResultState<CategoryListModel> = {
        status: 'SUCCESS',
        data: mockCategoryData,
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute();

      expect(mockExecutionHandler).toHaveBeenCalledTimes(1);
      expect(mockExecutionHandler).toHaveBeenCalledWith(expect.any(Function));
      expect(result).toBe(mockResult);
      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.categories).toHaveLength(2);
        expect(result.data?.categories[0].name).toBe('Electricity');
      }
    });

    it('should pass repository.categoryList function to ExecutionHandler', async () => {
      const mockResult: ResultState<CategoryListModel> = {
        status: 'SUCCESS',
        data: {categories: []},
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      await useCase.execute();

      // Verify that the function passed to ExecutionHandler calls repository.categoryList
      const passedFunction = mockExecutionHandler.mock.calls[0][0];

      // Execute the passed function to verify it calls repository.categoryList
      await passedFunction();
      expect(mockRepository.categoryList).toHaveBeenCalledTimes(1);
      expect(mockRepository.categoryList).toHaveBeenCalledWith();
    });

    it('should handle successful execution with empty categories', async () => {
      const mockEmptyData: CategoryListModel = {
        categories: [],
      };

      const mockResult: ResultState<CategoryListModel> = {
        status: 'SUCCESS',
        data: mockEmptyData,
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute();

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.categories).toEqual([]);
      }
    });

    it('should handle successful execution with complex category data', async () => {
      const mockComplexData: CategoryListModel = {
        categories: [
          {
            id: 'ELEC_001',
            name: 'Electricity',
            icon: 'https://example.com/electricity.png',
            description: 'Pay your electricity bills',
            subcategories: [
              {id: 'EVN_HCM', name: 'EVN Ho Chi Minh'},
              {id: 'EVN_HN', name: 'EVN Ha Noi'},
            ],
            isActive: true,
            sortOrder: 1,
          },
          {
            id: 'WATER_001',
            name: 'Water',
            icon: 'https://example.com/water.png',
            description: 'Pay your water bills',
            subcategories: [
              {id: 'SAWACO', name: 'SAWACO'},
              {id: 'HAWACO', name: 'HAWACO'},
            ],
            isActive: true,
            sortOrder: 2,
          },
        ],
        metadata: {
          total: 2,
          lastUpdated: '2024-06-26T10:00:00Z',
        },
      };

      const mockResult: ResultState<CategoryListModel> = {
        status: 'SUCCESS',
        data: mockComplexData,
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute();

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        expect(result.data?.categories).toHaveLength(2);
        expect(result.data?.categories[0].subcategories).toHaveLength(2);
        expect(result.data?.metadata?.total).toBe(2);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle business logic errors', async () => {
      const businessError = new CustomError(
        MSBErrorCode.PIS0101,
        ErrorCategory.BUSINESS,
        'Business Error',
        'Category service unavailable',
        false,
        [
          {type: 'RETRY', label: 'Try Again', primary: true},
          {type: 'CONTACT_SUPPORT', label: 'Contact Support'},
        ],
      );

      const mockErrorResult: ResultState<CategoryListModel> = {
        status: 'ERROR',
        error: businessError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error).toBe(businessError);
        expect(result.error.code).toBe(MSBErrorCode.PIS0101);
        expect(result.error.category).toBe(ErrorCategory.BUSINESS);
        expect(result.error.retryable).toBe(false);
        expect(result.error.actions).toHaveLength(2);
      }
    });

    it('should handle network errors', async () => {
      const networkError = new CustomError(
        MSBErrorCode.PIS0106,
        ErrorCategory.NETWORK,
        'Network Error',
        'Unable to connect to category service',
        true,
        [
          {type: 'RETRY', label: 'Retry', primary: true},
          {type: 'CHECK_CONNECTION', label: 'Check Connection'},
        ],
      );

      const mockErrorResult: ResultState<CategoryListModel> = {
        status: 'ERROR',
        error: networkError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error.code).toBe(MSBErrorCode.PIS0106);
        expect(result.error.category).toBe(ErrorCategory.NETWORK);
        expect(result.error.retryable).toBe(true);
      }
    });

    it('should handle system errors', async () => {
      const systemError = new CustomError(
        MSBErrorCode.A05,
        ErrorCategory.SYSTEM,
        'System Error',
        'Internal system error occurred',
        true,
        [
          {type: 'RETRY', label: 'Retry', primary: true},
          {type: 'CONTACT_SUPPORT', label: 'Contact Support'},
        ],
      );

      const mockErrorResult: ResultState<CategoryListModel> = {
        status: 'ERROR',
        error: systemError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error.code).toBe(MSBErrorCode.A05);
        expect(result.error.category).toBe(ErrorCategory.SYSTEM);
      }
    });

    it('should handle unknown errors', async () => {
      const unknownError = new CustomError(
        MSBErrorCode.UNKNOWN_ERROR,
        ErrorCategory.UNKNOWN,
        'Unknown Error',
        'An unexpected error occurred',
        true,
        [
          {type: 'RETRY', label: 'Retry', primary: true},
          {type: 'CONTACT_SUPPORT', label: 'Contact Support'},
        ],
      );

      const mockErrorResult: ResultState<CategoryListModel> = {
        status: 'ERROR',
        error: unknownError,
      };

      mockExecutionHandler.mockResolvedValue(mockErrorResult);

      const result = await useCase.execute();

      expect(result.status).toBe('ERROR');
      if (result.status === 'ERROR') {
        expect(result.error.code).toBe(MSBErrorCode.UNKNOWN_ERROR);
        expect(result.error.category).toBe(ErrorCategory.UNKNOWN);
      }
    });
  });

  describe('Integration with ExecutionHandler', () => {
    it('should delegate all execution logic to ExecutionHandler', async () => {
      const mockResult: ResultState<CategoryListModel> = {
        status: 'SUCCESS',
        data: {categories: []},
      };

      mockExecutionHandler.mockResolvedValue(mockResult);

      const result = await useCase.execute();

      expect(mockExecutionHandler).toHaveBeenCalledTimes(1);
      expect(result).toBe(mockResult);

      // Verify that useCase doesn't implement its own error handling
      // All error handling should be delegated to ExecutionHandler
    });

    it('should pass correct function signature to ExecutionHandler', async () => {
      // Mock repository to return a Promise
      mockRepository.categoryList.mockResolvedValue({categories: []});

      mockExecutionHandler.mockResolvedValue({
        status: 'SUCCESS',
        data: {categories: []},
      });

      await useCase.execute();

      const passedFunction = mockExecutionHandler.mock.calls[0][0];
      expect(typeof passedFunction).toBe('function');

      // The function should return a Promise
      const functionResult = passedFunction();
      expect(functionResult).toBeInstanceOf(Promise);

      // Clean up the promise
      await functionResult;
    });

    it('should not pass loading parameter to ExecutionHandler by default', async () => {
      mockExecutionHandler.mockResolvedValue({
        status: 'SUCCESS',
        data: {categories: []},
      });

      await useCase.execute();

      expect(mockExecutionHandler).toHaveBeenCalledWith(expect.any(Function));
      expect(mockExecutionHandler.mock.calls[0]).toHaveLength(1); // Only function parameter
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for CategoryListModel', async () => {
      const typedResult: ResultState<CategoryListModel> = {
        status: 'SUCCESS',
        data: {
          categories: [
            {
              id: '1',
              name: 'Test Category',
              icon: 'test-icon',
              description: 'Test description',
            },
          ],
        },
      };

      mockExecutionHandler.mockResolvedValue(typedResult);

      const result: ResultState<CategoryListModel> = await useCase.execute();

      expect(result.status).toBe('SUCCESS');
      if (result.status === 'SUCCESS') {
        // TypeScript should enforce CategoryListModel structure
        expect(result.data?.categories).toBeDefined();
        expect(Array.isArray(result.data?.categories)).toBe(true);
      }
    });

    it('should work with generic ResultState type', async () => {
      // This test ensures proper TypeScript compilation
      const useCase: CategoryListUseCase = new CategoryListUseCase(mockRepository);
      const executeMethod: () => Promise<ResultState<CategoryListModel>> = useCase.execute.bind(useCase);

      expect(typeof executeMethod).toBe('function');
    });
  });

  describe('Clean Architecture Compliance', () => {
    it('should depend only on repository interface, not implementation', () => {
      // This test ensures dependency inversion principle
      expect(useCase['repository']).toBe(mockRepository);

      // UseCase should work with any implementation of IBillPayRepository
      const anotherRepository: IBillPayRepository = {
        categoryList: jest.fn(),
        providerList: jest.fn(),
        myBillList: jest.fn(),
        getBillDetail: jest.fn(),
        billValidate: jest.fn(),
      };

      const anotherUseCase = new CategoryListUseCase(anotherRepository);
      expect(anotherUseCase['repository']).toBe(anotherRepository);
    });

    it('should encapsulate business logic in execute method', () => {
      // UseCase should have single responsibility: execute business logic
      const publicMethods = Object.getOwnPropertyNames(CategoryListUseCase.prototype).filter(
        method => method !== 'constructor' && !method.startsWith('_'),
      );

      expect(publicMethods).toEqual(['execute']);
    });

    it('should not have direct dependencies on external frameworks', () => {
      // UseCase should not import React Native, UI components, or external libraries
      // This is enforced by the import statements and constructor dependencies
      expect(useCase['repository']).toBeDefined();

      // Should only depend on domain interfaces and core utilities
      const dependencies = Object.keys(useCase);
      expect(dependencies).toEqual(['repository']);
    });
  });
});
