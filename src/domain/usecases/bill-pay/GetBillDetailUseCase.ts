import {IBillPayRepository} from '../../repositories/IBillPayRepository';
import {GetBillDetailModel} from '../../entities/get-bill-detail/GetBillDetailModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {GetBillDetailRequest} from '../../../data/models/get-bill-detail/GetBillDetailRequest';
export class GetBillDetailUseCase {
  private repository: IBillPayRepository;

  constructor(repository: IBillPayRepository) {
    this.repository = repository;
  }

  public async execute(request: GetBillDetailRequest): Promise<ResultState<GetBillDetailModel>> {
    return ExecutionHandler.execute(() => this.repository.getBillDetail(request), true);
  }
}
