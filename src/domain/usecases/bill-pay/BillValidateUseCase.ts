import {IBillPayRepository} from '../../repositories/IBillPayRepository';
import {BillValidateModel} from '../../entities/bill-validate/BillValidateModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {BillValidateRequest} from '../../../data/models/bill-validate/BillValidateRequest';
export class BillValidateUseCase {
  private repository: IBillPayRepository;

  constructor(repository: IBillPayRepository) {
    this.repository = repository;
  }

  public async execute(request: BillValidateRequest): Promise<ResultState<BillValidateModel>> {
    // call this.repository.billValidate(...)
    return ExecutionHandler.execute(() => this.repository.billValidate(request));
  }
}
