import {IBillPayRepository} from '../../repositories/IBillPayRepository';
import {ProviderListModel} from '../../entities/provider-list/ProviderListModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {ProviderListRequest} from '../../../data/models/provider-list/ProviderListRequest';
export class ProviderListUseCase {
  private repository: IBillPayRepository;

  constructor(repository: IBillPayRepository) {
    this.repository = repository;
  }

  public async execute(request: ProviderListRequest): Promise<ResultState<ProviderListModel>> {
    // call this.repository.providerList(...)
    return ExecutionHandler.execute(() => this.repository.providerList(request));
  }
}
