import {IBillPayRepository} from '../../repositories/IBillPayRepository';
import {CategoryListModel} from '../../entities/category-list/CategoryListModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
export class CategoryListUseCase {
  private repository: IBillPayRepository;

  constructor(repository: IBillPayRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<CategoryListModel>> {
    // call this.repository.categoryList(...)
    return ExecutionHandler.execute(() => this.repository.categoryList());
  }
}
