import {IBillPayRepository} from '../../repositories/IBillPayRepository';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {MyBillListRequest} from '../../../data/models/my-bill-list/MyBillListRequest';
import {MyBillContactListModel} from '../../entities/my-bill-contact-list/MyBillContactListModel';
export class MyBillListUseCase {
  private repository: IBillPayRepository;

  constructor(repository: IBillPayRepository) {
    this.repository = repository;
  }

  public async execute(request: MyBillListRequest): Promise<ResultState<MyBillContactListModel>> {
    // call this.repository.myBillList(...)
    return ExecutionHandler.execute(() => this.repository.myBillList(request));
  }
}
