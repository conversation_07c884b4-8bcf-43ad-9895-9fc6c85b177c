import {Exec<PERSON>Hand<PERSON>} from '../../../utils/ExcecutionHandler';
import {GetMyBillContactRecentListRequest} from '../../../data/models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';
import {IBillContactRepository} from '../../repositories/IBillContactRepository';
import {GetMyBillContactRecentListModel} from '../../entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';
import {ResultState} from '../../../core/ResultState';
export class GetMyBillContactRecentListUseCase {
  private repository: IBillContactRepository;

  constructor(repository: IBillContactRepository) {
    this.repository = repository;
  }

  public async execute(
    request: GetMyBillContactRecentListRequest,
  ): Promise<ResultState<GetMyBillContactRecentListModel>> {
    //TODO: implement state
    return ExecutionHandler.execute(() => this.repository.getMyBillContactRecentList(request));
  }
}
