import {IBillContactRepository} from '../../repositories/IBillContactRepository';
import {SaveBillContactModel} from '../../entities/save-bill-contact/SaveBillContactModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {SaveBillContactRequest} from '../../../data/models/save-bill-contact/SaveBillContactRequest';
export class SaveBillContactUseCase {
  private repository: IBillContactRepository;

  constructor(repository: IBillContactRepository) {
    this.repository = repository;
  }

  public async execute(request: SaveBillContactRequest): Promise<ResultState<SaveBillContactModel>> {
    // call this.repository.saveBillContact(...)
    return ExecutionHandler.execute(() => this.repository.saveBillContact(request));
  }
}
