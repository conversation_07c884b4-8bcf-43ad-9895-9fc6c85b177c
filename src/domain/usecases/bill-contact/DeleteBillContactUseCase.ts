import {IBillContactRepository} from '../../repositories/IBillContactRepository';
import {DeleteBillContactModel} from '../../entities/delete-bill-contact/DeleteBillContactModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {DeleteBillContactRequest} from '../../../data/models/delete-bill-contact/DeleteBillContactRequest';
export class DeleteBillContactUseCase {
  private repository: IBillContactRepository;

  constructor(repository: IBillContactRepository) {
    this.repository = repository;
  }

  public async execute(request: DeleteBillContactRequest): Promise<ResultState<DeleteBillContactModel>> {
    // call this.repository.deleteBillContact(...)
    return ExecutionHandler.execute(() => this.repository.deleteBillContact(request));
  }
}
