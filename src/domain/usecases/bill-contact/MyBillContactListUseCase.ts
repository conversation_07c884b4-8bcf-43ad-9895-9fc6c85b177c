import {IBillContactRepository} from '../../repositories/IBillContactRepository';
import {MyBillContactListModel} from '../../entities/my-bill-contact-list/MyBillContactListModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
export class MyBillContactListUseCase {
  private repository: IBillContactRepository;

  constructor(repository: IBillContactRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<MyBillContactListModel>> {
    // call this.repository.myBillContactList(...)
    return ExecutionHandler.execute(() => this.repository.myBillContactList());
  }
}
