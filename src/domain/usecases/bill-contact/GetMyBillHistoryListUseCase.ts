import {IBillContactRepository} from '../../repositories/IBillContactRepository';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {GetMyBillHistoryListRequest} from '../../../data/models/get-my-bill-history-list/GetMyBillHistoryListRequest';
import {ResultState} from '../../../core/ResultState';
import {BillHistoryModel} from '../../entities/get-my-bill-history-list/GetMyBillHistoryListModel';
export class GetMyBillHistoryListUseCase {
  private repository: IBillContactRepository;

  constructor(repository: IBillContactRepository) {
    this.repository = repository;
  }

  public async execute(request: GetMyBillHistoryListRequest): Promise<ResultState<BillHistoryModel>> {
    // call this.repository.getMyBillHistoryList(...)
    //TODO: implement state
    return ExecutionHandler.execute(() => this.repository.getMyBillHistoryList(request));
  }
}
