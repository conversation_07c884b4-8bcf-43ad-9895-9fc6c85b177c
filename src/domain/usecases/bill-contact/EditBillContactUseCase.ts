import {IBillContactRepository} from '../../repositories/IBillContactRepository';
import {EditBillContactModel} from '../../entities/edit-bill-contact/EditBillContactModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {EditBillContactRequest} from '../../../data/models/edit-bill-contact/EditBillContactRequest';
export class EditBillContactUseCase {
  private repository: IBillContactRepository;

  constructor(repository: IBillContactRepository) {
    this.repository = repository;
  }

  public async execute(request: EditBillContactRequest): Promise<ResultState<EditBillContactModel>> {
    // call this.repository.editBillContact(...)
    return ExecutionHandler.execute(() => this.repository.editBillContact(request));
  }
}
