import {ICustomerRepository} from '../../repositories/ICustomerRepository';
import {GetProfileModel} from '../../entities/get-profile/GetProfileModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
export class GetProfileUseCase {
  private repository: ICustomerRepository;

  constructor(repository: ICustomerRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<GetProfileModel>> {
    // call this.repository.getProfile(...)
    return ExecutionHandler.execute(() => this.repository.getProfile());
  }
}
