import {ICustomerRepository} from '../../repositories/ICustomerRepository';
import {ResultState} from '../../../core/ResultState';
import {IArrangementRepository} from '../../repositories/IArrangementRepository';
import {SourceAccountListRequest} from '../../../data/models/source-account-list/SourceAccountListRequest';
import {createError, CustomError} from '../../../core/MSBCustomError';
export class ValidateCustomerUseCase {
  private repository: ICustomerRepository;
  private arrangementRepository: IArrangementRepository;

  constructor(repository: ICustomerRepository, arrangementRepository: IArrangementRepository) {
    this.arrangementRepository = arrangementRepository;
    this.repository = repository;
  }

  public async execute(request?: SourceAccountListRequest): Promise<ResultState<boolean>> {
    // call this.repository.getProfile(...)
    const params: SourceAccountListRequest = {
      externalStateIds: ['ACTIVE'],
      externalProductKindIds: ['kind1', 'kind10'],
      currency: 'VND',
    };
    try {
      const response = await Promise.all([
        this.repository.getProfile(),
        this.arrangementRepository.sourceAccountList(request ?? params),
      ]);
      console.log('✅ ExecutionHandler with response=', response);
      if (!response || !response[0] || !response[1] || response[0].errors || response[1].errors) {
        return {
          status: 'ERROR',
          error: createError(response[0]?.errors?.[0]?.key),
        };
      }
      if (response[0].isBlocked() === true) {
        return {
          status: 'ERROR',
          error: createError(), //TODO: block user
        };
      }
      if (response[1].data?.length === 0) {
        return {
          status: 'ERROR',
          error: createError(), //TODO: khong co tai khoan nguon phu hop
        };
      }
      return {
        status: 'SUCCESS',
        data: true,
      };
    } catch (error) {
      console.error('❌ ExecutionHandler UseCase catch Error:', error);
      return {
        status: 'ERROR',
        error: error instanceof CustomError ? error : createError(),
      };
    }
  }
}
