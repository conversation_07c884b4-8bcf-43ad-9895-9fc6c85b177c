import {IPaymentOrderRepository} from '../../repositories/IPaymentOrderRepository';
import {PaymentOrderModel} from '../../entities/payment-order/PaymentOrderModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {PaymentOrderRequest} from '../../../data/models/payment-order/PaymentOrderRequest';
export class PaymentOrderUseCase {
  private repository: IPaymentOrderRepository;

  constructor(repository: IPaymentOrderRepository) {
    this.repository = repository;
  }

  public async execute(request: PaymentOrderRequest): Promise<ResultState<PaymentOrderModel>> {
    // call this.repository.paymentOrder(...)
    return ExecutionHandler.execute(() => this.repository.paymentOrder(request));
  }
}
