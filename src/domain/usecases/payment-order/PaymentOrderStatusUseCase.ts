import {IPaymentOrderRepository} from '../../repositories/IPaymentOrderRepository';
import {PaymentOrderStatusModel} from '../../entities/payment-order-status/PaymentOrderStatusModel';
import {ResultState} from '../../../core/ResultState';
import {ExecutionHandler} from '../../../utils/ExcecutionHandler';
import {PaymentOrderStatusRequest} from '../../../data/models/payment-order-status/PaymentOrderStatusRequest';
export class PaymentOrderStatusUseCase {
  private repository: IPaymentOrderRepository;

  constructor(repository: IPaymentOrderRepository) {
    this.repository = repository;
  }

  public async execute(request: PaymentOrderStatusRequest): Promise<ResultState<PaymentOrderStatusModel>> {
    // call this.repository.paymentOrderStatus(...)
    return ExecutionHandler.execute(() => this.repository.paymentOrderStatus(request));
  }
}
