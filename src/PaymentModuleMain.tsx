import {LocaleProvider} from 'msb-communication-lib/dist/locales/LocaleContext';
import {useHostInjection} from 'msb-host-shared-module';
import React from 'react';

import {translations} from './locales/i18n.ts';
import PaymentStack from './navigation/PaymentStack.tsx';

const PaymentModuleMain = () => {
  const {locale} = useHostInjection();

  return (
    <LocaleProvider translations={translations} defaultLocale={locale}>
      <PaymentStack />
    </LocaleProvider>
  );
};

export default PaymentModuleMain;
