import {mapGetMyBillHistoryListResponseToModel} from '../mappers/get-my-bill-history-list/GetMyBillHistoryListMapper';
import {BillHistoryModel} from '../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel';
import {GetMyBillHistoryListRequest} from '../models/get-my-bill-history-list/GetMyBillHistoryListRequest';
import {mapGetMyBillContactRecentListResponseToModel} from '../mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper';
import {GetMyBillContactRecentListModel} from '../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';
import {GetMyBillContactRecentListRequest} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';
import {mapMyBillContactListResponseToModel} from '../mappers/my-bill-contact-list/MyBillContactListMapper';
import {MyBillContactListModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';
import {mapEditBillContactResponseToModel} from '../mappers/edit-bill-contact/EditBillContactMapper';
import {EditBillContactModel} from '../../domain/entities/edit-bill-contact/EditBillContactModel';
import {EditBillContactRequest} from '../models/edit-bill-contact/EditBillContactRequest';
import {mapDeleteBillContactResponseToModel} from '../mappers/delete-bill-contact/DeleteBillContactMapper';
import {DeleteBillContactModel} from '../../domain/entities/delete-bill-contact/DeleteBillContactModel';
import {DeleteBillContactRequest} from '../models/delete-bill-contact/DeleteBillContactRequest';
import {mapSaveBillContactResponseToModel} from '../mappers/save-bill-contact/SaveBillContactMapper';
import {handleData} from '../../utils/HandleData';
import {SaveBillContactModel} from '../../domain/entities/save-bill-contact/SaveBillContactModel';
import {SaveBillContactRequest} from '../models/save-bill-contact/SaveBillContactRequest';
import {BaseResponse} from '../../core/BaseResponse';
import {IBillContactRepository} from '../../domain/repositories/IBillContactRepository';
import {IBillContactDataSource} from '../datasources/IBillContactDataSource';

export class BillContactRepository implements IBillContactRepository {
  private remoteDataSource: IBillContactDataSource;

  constructor(remoteDataSource: IBillContactDataSource) {
    this.remoteDataSource = remoteDataSource;
  }

  async saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactModel>> {
    return handleData<SaveBillContactModel>(
      this.remoteDataSource.saveBillContact(request),
      mapSaveBillContactResponseToModel,
    );
  }

  async deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactModel>> {
    return handleData<DeleteBillContactModel>(
      this.remoteDataSource.deleteBillContact(request),
      mapDeleteBillContactResponseToModel,
    );
  }

  async editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactModel>> {
    return handleData<EditBillContactModel>(
      this.remoteDataSource.editBillContact(request),
      mapEditBillContactResponseToModel,
    );
  }

  async myBillContactList(): Promise<BaseResponse<MyBillContactListModel>> {
    return handleData<MyBillContactListModel>(
      this.remoteDataSource.myBillContactList(),
      mapMyBillContactListResponseToModel,
    );
  }

  async getMyBillContactRecentList(
    request: GetMyBillContactRecentListRequest,
  ): Promise<BaseResponse<GetMyBillContactRecentListModel>> {
    return handleData<GetMyBillContactRecentListModel>(
      this.remoteDataSource.getMyBillContactRecentList(request),
      mapGetMyBillContactRecentListResponseToModel,
    );
  }

  async getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<BillHistoryModel>> {
    return handleData<BillHistoryModel>(
      this.remoteDataSource.getMyBillHistoryList(request),
      mapGetMyBillHistoryListResponseToModel,
    );
  }
}
