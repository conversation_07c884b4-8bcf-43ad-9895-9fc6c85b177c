/**
 * Unit Tests for BillContactRepository
 * Testing Repository layer implementation with Clean Architecture
 */

import { BillContactRepository } from '../BillContactRepository';
import { IBillContactDataSource } from '../../datasources/IBillContactDataSource';
import { CustomError, ErrorCategory } from '../../../core/MSBCustomError';
import { MSBErrorCode } from '../../../core/MSBErrorCode';
import { BaseResponse } from '../../../core/BaseResponse';

// Mock the handleData utility
jest.mock('../../../utils/HandleData', () => ({
  handleData: jest.fn(),
}));

// Mock all mapper functions
jest.mock('../../mappers/save-bill-contact/SaveBillContactMapper', () => ({
  mapSaveBillContactResponseToModel: jest.fn(),
}));

jest.mock('../../mappers/delete-bill-contact/DeleteBillContactMapper', () => ({
  mapDeleteBillContactResponseToModel: jest.fn(),
}));

jest.mock('../../mappers/edit-bill-contact/EditBillContactMapper', () => ({
  mapEditBillContactResponseToModel: jest.fn(),
}));

jest.mock('../../mappers/my-bill-contact-list/MyBillContactListMapper', () => ({
  mapMyBillContactListResponseToModel: jest.fn(),
}));

jest.mock('../../mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper', () => ({
  mapGetMyBillContactRecentListResponseToModel: jest.fn(),
}));

jest.mock('../../mappers/get-my-bill-history-list/GetMyBillHistoryListMapper', () => ({
  mapGetMyBillHistoryListResponseToModel: jest.fn(),
}));

import { handleData } from '../../../utils/HandleData';
import {
  mapSaveBillContactResponseToModel,
} from '../../mappers/save-bill-contact/SaveBillContactMapper';
import {
  mapDeleteBillContactResponseToModel,
} from '../../mappers/delete-bill-contact/DeleteBillContactMapper';
import {
  mapEditBillContactResponseToModel,
} from '../../mappers/edit-bill-contact/EditBillContactMapper';
import {
  mapMyBillContactListResponseToModel,
} from '../../mappers/my-bill-contact-list/MyBillContactListMapper';
import {
  mapGetMyBillContactRecentListResponseToModel,
} from '../../mappers/get-my-bill-contact-recent-list/GetMyBillContactRecentListMapper';
import {
  mapGetMyBillHistoryListResponseToModel,
} from '../../mappers/get-my-bill-history-list/GetMyBillHistoryListMapper';

describe('BillContactRepository', () => {
  let repository: BillContactRepository;
  let mockDataSource: jest.Mocked<IBillContactDataSource>;
  let mockHandleData: jest.MockedFunction<typeof handleData>;

  beforeEach(() => {
    // Create mock data source
    mockDataSource = {
      saveBillContact: jest.fn(),
      deleteBillContact: jest.fn(),
      editBillContact: jest.fn(),
      myBillContactList: jest.fn(),
      getMyBillContactRecentList: jest.fn(),
      getMyBillHistoryList: jest.fn(),
    };

    // Create repository instance
    repository = new BillContactRepository(mockDataSource);

    // Setup handleData mock
    mockHandleData = handleData as jest.MockedFunction<typeof handleData>;

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should create repository with data source dependency', () => {
      expect(repository).toBeInstanceOf(BillContactRepository);
      expect(repository['remoteDataSource']).toBe(mockDataSource);
    });
  });

  describe('saveBillContact', () => {
    it('should call handleData with correct parameters', async () => {
      const mockRequest = {
        contactName: 'Test Contact',
        billCode: 'BILL001',
        providerId: 'PROVIDER001',
      };

      const mockResponse = { success: true };
      const mockModel = { id: 1, name: 'Test Contact' };

      mockDataSource.saveBillContact.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.saveBillContact(mockRequest);

      expect(mockDataSource.saveBillContact).toHaveBeenCalledWith(mockRequest);
      expect(mockHandleData).toHaveBeenCalledWith(
        mockDataSource.saveBillContact(mockRequest),
        mapSaveBillContactResponseToModel
      );
      expect(result).toBe(mockModel);
    });

    it('should propagate errors from handleData', async () => {
      const mockRequest = {
        contactName: 'Test Contact',
        billCode: 'BILL001',
        providerId: 'PROVIDER001',
      };

      const customError = new CustomError(
        MSBErrorCode.PIS0101,
        ErrorCategory.BUSINESS,
        'Save Error',
        'Failed to save contact',
        false,
        []
      );

      mockHandleData.mockRejectedValue(customError);

      await expect(repository.saveBillContact(mockRequest)).rejects.toThrow(customError);
      expect(mockDataSource.saveBillContact).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('deleteBillContact', () => {
    it('should call handleData with correct parameters', async () => {
      const mockRequest = {
        contactId: '123',
      };

      const mockResponse = { success: true };
      const mockModel = { deleted: true };

      mockDataSource.deleteBillContact.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.deleteBillContact(mockRequest);

      expect(mockDataSource.deleteBillContact).toHaveBeenCalledWith(mockRequest);
      expect(mockHandleData).toHaveBeenCalledWith(
        mockDataSource.deleteBillContact(mockRequest),
        mapDeleteBillContactResponseToModel
      );
      expect(result).toBe(mockModel);
    });

    it('should handle deletion errors', async () => {
      const mockRequest = { contactId: '123' };
      const networkError = new CustomError(
        MSBErrorCode.PIS0106,
        ErrorCategory.NETWORK,
        'Network Error',
        'Connection failed',
        true,
        []
      );

      mockHandleData.mockRejectedValue(networkError);

      await expect(repository.deleteBillContact(mockRequest)).rejects.toThrow(networkError);
    });
  });

  describe('editBillContact', () => {
    it('should call handleData with correct parameters', async () => {
      const mockRequest = {
        contactId: '123',
        contactName: 'Updated Contact',
        billCode: 'BILL002',
      };

      const mockResponse = { success: true };
      const mockModel = { id: 123, name: 'Updated Contact' };

      mockDataSource.editBillContact.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.editBillContact(mockRequest);

      expect(mockDataSource.editBillContact).toHaveBeenCalledWith(mockRequest);
      expect(mockHandleData).toHaveBeenCalledWith(
        mockDataSource.editBillContact(mockRequest),
        mapEditBillContactResponseToModel
      );
      expect(result).toBe(mockModel);
    });

    it('should handle validation errors', async () => {
      const mockRequest = {
        contactId: '123',
        contactName: '',
        billCode: 'INVALID',
      };

      const validationError = new CustomError(
        MSBErrorCode.PIS0103,
        ErrorCategory.VALIDATION,
        'Validation Error',
        'Invalid contact data',
        false,
        []
      );

      mockHandleData.mockRejectedValue(validationError);

      await expect(repository.editBillContact(mockRequest)).rejects.toThrow(validationError);
    });
  });

  describe('myBillContactList', () => {
    it('should call handleData with correct parameters', async () => {
      const mockResponse = {
        contacts: [
          { id: 1, name: 'Contact 1' },
          { id: 2, name: 'Contact 2' },
        ],
      };

      const mockModel = {
        contactList: [
          { id: 1, name: 'Contact 1' },
          { id: 2, name: 'Contact 2' },
        ],
      };

      mockDataSource.myBillContactList.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.myBillContactList();

      expect(mockDataSource.myBillContactList).toHaveBeenCalledWith();
      expect(mockHandleData).toHaveBeenCalledWith(
        mockDataSource.myBillContactList(),
        mapMyBillContactListResponseToModel
      );
      expect(result).toBe(mockModel);
    });

    it('should handle empty contact list', async () => {
      const mockResponse = { contacts: [] };
      const mockModel = { contactList: [] };

      mockDataSource.myBillContactList.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.myBillContactList();

      expect(result).toBe(mockModel);
      expect(mockDataSource.myBillContactList).toHaveBeenCalledTimes(1);
    });
  });

  describe('getMyBillContactRecentList', () => {
    it('should call handleData with correct parameters', async () => {
      const mockRequest = {
        limit: 10,
        offset: 0,
      };

      const mockResponse = {
        recentContacts: [
          { id: 1, name: 'Recent Contact 1', lastUsed: '2024-01-01' },
        ],
      };

      const mockModel = {
        recentContactList: [
          { id: 1, name: 'Recent Contact 1', lastUsed: '2024-01-01' },
        ],
      };

      mockDataSource.getMyBillContactRecentList.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.getMyBillContactRecentList(mockRequest);

      expect(mockDataSource.getMyBillContactRecentList).toHaveBeenCalledWith(mockRequest);
      expect(mockHandleData).toHaveBeenCalledWith(
        mockDataSource.getMyBillContactRecentList(mockRequest),
        mapGetMyBillContactRecentListResponseToModel
      );
      expect(result).toBe(mockModel);
    });

    it('should handle pagination parameters', async () => {
      const mockRequest = {
        limit: 5,
        offset: 10,
      };

      mockDataSource.getMyBillContactRecentList.mockResolvedValue({ recentContacts: [] });
      mockHandleData.mockResolvedValue({ recentContactList: [] });

      await repository.getMyBillContactRecentList(mockRequest);

      expect(mockDataSource.getMyBillContactRecentList).toHaveBeenCalledWith({
        limit: 5,
        offset: 10,
      });
    });
  });

  describe('getMyBillHistoryList', () => {
    it('should call handleData with correct parameters', async () => {
      const mockRequest = {
        fromDate: '2024-01-01',
        toDate: '2024-01-31',
        page: 1,
        size: 20,
      };

      const mockResponse = {
        history: [
          { id: 1, billCode: 'BILL001', amount: 100000, date: '2024-01-15' },
          { id: 2, billCode: 'BILL002', amount: 200000, date: '2024-01-20' },
        ],
        pagination: {
          total: 2,
          page: 1,
          size: 20,
        },
      };

      const mockModel = {
        historyList: [
          { id: 1, billCode: 'BILL001', amount: 100000, date: '2024-01-15' },
          { id: 2, billCode: 'BILL002', amount: 200000, date: '2024-01-20' },
        ],
        totalCount: 2,
        currentPage: 1,
      };

      mockDataSource.getMyBillHistoryList.mockResolvedValue(mockResponse);
      mockHandleData.mockResolvedValue(mockModel);

      const result = await repository.getMyBillHistoryList(mockRequest);

      expect(mockDataSource.getMyBillHistoryList).toHaveBeenCalledWith(mockRequest);
      expect(mockHandleData).toHaveBeenCalledWith(
        mockDataSource.getMyBillHistoryList(mockRequest),
        mapGetMyBillHistoryListResponseToModel
      );
      expect(result).toBe(mockModel);
    });

    it('should handle date range filtering', async () => {
      const mockRequest = {
        fromDate: '2024-06-01',
        toDate: '2024-06-30',
        page: 1,
        size: 10,
      };

      mockDataSource.getMyBillHistoryList.mockResolvedValue({ history: [] });
      mockHandleData.mockResolvedValue({ historyList: [], totalCount: 0 });

      await repository.getMyBillHistoryList(mockRequest);

      expect(mockDataSource.getMyBillHistoryList).toHaveBeenCalledWith({
        fromDate: '2024-06-01',
        toDate: '2024-06-30',
        page: 1,
        size: 10,
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle data source errors consistently across all methods', async () => {
      const systemError = new CustomError(
        MSBErrorCode.A05,
        ErrorCategory.SYSTEM,
        'System Error',
        'System unavailable',
        true,
        []
      );

      mockHandleData.mockRejectedValue(systemError);

      // Test all methods handle errors consistently
      await expect(repository.saveBillContact({ contactName: 'Test' })).rejects.toThrow(systemError);
      await expect(repository.deleteBillContact({ contactId: '1' })).rejects.toThrow(systemError);
      await expect(repository.editBillContact({ contactId: '1', contactName: 'Test' })).rejects.toThrow(systemError);
      await expect(repository.myBillContactList()).rejects.toThrow(systemError);
      await expect(repository.getMyBillContactRecentList({ limit: 10 })).rejects.toThrow(systemError);
      await expect(repository.getMyBillHistoryList({ fromDate: '2024-01-01', toDate: '2024-01-31' })).rejects.toThrow(systemError);
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for all method signatures', async () => {
      // This test ensures TypeScript compilation and proper interface implementation
      const repository: BillContactRepository = new BillContactRepository(mockDataSource);

      // All these should compile without TypeScript errors
      expect(typeof repository.saveBillContact).toBe('function');
      expect(typeof repository.deleteBillContact).toBe('function');
      expect(typeof repository.editBillContact).toBe('function');
      expect(typeof repository.myBillContactList).toBe('function');
      expect(typeof repository.getMyBillContactRecentList).toBe('function');
      expect(typeof repository.getMyBillHistoryList).toBe('function');
    });
  });
});
