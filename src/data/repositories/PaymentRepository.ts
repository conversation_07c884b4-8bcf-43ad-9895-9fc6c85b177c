import {mapValidateResponseToModel} from '../mappers/validate/ValidateMapper';
import {handleData} from '../../utils/HandleData';
import {ValidateModel} from '../../domain/entities/validate/ValidateModel';
import {ValidateRequest} from '../models/validate/ValidateRequest';
import {IPaymentDataSource} from '../datasources/IPaymentDataSource';
import {BaseResponse} from '../../core/BaseResponse';
import {IPaymentRepository} from '../../domain/repositories/IPaymentRepository';

export class PaymentRepository implements IPaymentRepository {
  private remoteDataSource: IPaymentDataSource;

  constructor(remoteDataSource: IPaymentDataSource) {
    this.remoteDataSource = remoteDataSource;
  }

  async validate(request: ValidateRequest): Promise<BaseResponse<ValidateModel>> {
    return handleData<ValidateModel>(this.remoteDataSource.validate(request), mapValidateResponseToModel);
  }
}
