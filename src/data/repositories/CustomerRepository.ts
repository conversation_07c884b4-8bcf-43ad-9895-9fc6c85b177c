import {mapGetProfileResponseToModel} from '../mappers/get-profile/GetProfileMapper';
import {handleData} from '../../utils/HandleData';
import {GetProfileModel} from '../../domain/entities/get-profile/GetProfileModel';
import {ICustomerDataSource} from '../datasources/ICustomerDataSource';
import {BaseResponse} from '../../core/BaseResponse';
import {ICustomerRepository} from '../../domain/repositories/ICustomerRepository';

export class CustomerRepository implements ICustomerRepository {
  private remoteDataSource: ICustomerDataSource;

  constructor(remoteDataSource: ICustomerDataSource) {
    this.remoteDataSource = remoteDataSource;
  }

  async getProfile(): Promise<BaseResponse<GetProfileModel>> {
    return handleData<GetProfileModel>(this.remoteDataSource.getProfile(), mapGetProfileResponseToModel);
  }
}
