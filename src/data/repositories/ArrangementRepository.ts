import {mapSourceAccountListResponseToModel} from '../mappers/source-account-list/SourceAccountListMapper';
import {handleData} from '../../utils/HandleData';
import {SourceAccountListModel} from '../../domain/entities/source-account-list/SourceAccountListModel';
import {SourceAccountListRequest} from '../models/source-account-list/SourceAccountListRequest';
import {IArrangementDataSource} from '../datasources/IArrangementDataSource';
import {BaseResponse} from '../../core/BaseResponse';
import {IArrangementRepository} from '../../domain/repositories/IArrangementRepository';

export class ArrangementRepository implements IArrangementRepository {
  private remoteDataSource: IArrangementDataSource;

  constructor(remoteDataSource: IArrangementDataSource) {
    this.remoteDataSource = remoteDataSource;
  }

  async sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListModel>> {
    return handleData<SourceAccountListModel>(
      this.remoteDataSource.sourceAccountList(request),
      mapSourceAccountListResponseToModel,
    );
  }
}
