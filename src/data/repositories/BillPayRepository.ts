import {mapGetBillDetailResponseToModel} from '../mappers/get-bill-detail/GetBillDetailMapper';
import {GetBillDetailModel} from '../../domain/entities/get-bill-detail/GetBillDetailModel';
import {GetBillDetailRequest} from '../models/get-bill-detail/GetBillDetailRequest';
import {mapBillValidateResponseToModel} from '../mappers/bill-validate/BillValidateMapper';
import {BillValidateModel} from '../../domain/entities/bill-validate/BillValidateModel';
import {mapMyBillListResponseToModel} from '../mappers/my-bill-list/MyBillListMapper';
import {MyBillListRequest} from '../models/my-bill-list/MyBillListRequest';
import {mapProviderListResponseToModel} from '../mappers/provider-list/ProviderListMapper';
import {ProviderListModel} from '../../domain/entities/provider-list/ProviderListModel';
import {ProviderListRequest} from '../models/provider-list/ProviderListRequest';
import {mapCategoryListResponseToModel} from '../mappers/category-list/CategoryListMapper';
import {handleData} from '../../utils/HandleData';
import {CategoryListModel} from '../../domain/entities/category-list/CategoryListModel';
import {IBillPayDataSource} from '../datasources/IBillPayDataSource';
import {BaseResponse} from '../../core/BaseResponse';
import {IBillPayRepository} from '../../domain/repositories/IBillPayRepository';
import {BillValidateRequest} from '../models/bill-validate/BillValidateRequest';
import {MyBillContactListModel} from '../../domain/entities/my-bill-contact-list/MyBillContactListModel';

export class BillPayRepository implements IBillPayRepository {
  private remoteDataSource: IBillPayDataSource;

  constructor(remoteDataSource: IBillPayDataSource) {
    this.remoteDataSource = remoteDataSource;
  }

  async categoryList(): Promise<BaseResponse<CategoryListModel>> {
    return handleData<CategoryListModel>(this.remoteDataSource.categoryList(), mapCategoryListResponseToModel);
  }

  async providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListModel>> {
    return handleData<ProviderListModel>(this.remoteDataSource.providerList(request), mapProviderListResponseToModel);
  }

  async myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillContactListModel>> {
    return handleData<MyBillContactListModel>(this.remoteDataSource.myBillList(request), mapMyBillListResponseToModel);
  }

  async getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailModel>> {
    return handleData<GetBillDetailModel>(
      this.remoteDataSource.getBillDetail(request),
      mapGetBillDetailResponseToModel,
    );
  }

  async billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateModel>> {
    return handleData<BillValidateModel>(this.remoteDataSource.billValidate(request), mapBillValidateResponseToModel);
  }
}
