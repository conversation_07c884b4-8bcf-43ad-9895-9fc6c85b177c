import {mapPaymentOrderStatusResponseToModel} from '../mappers/payment-order-status/PaymentOrderStatusMapper';
import {PaymentOrderStatusModel} from '../../domain/entities/payment-order-status/PaymentOrderStatusModel';
import {mapPaymentOrderResponseToModel} from '../mappers/payment-order/PaymentOrderMapper';
import {handleData} from '../../utils/HandleData';
import {PaymentOrderModel} from '../../domain/entities/payment-order/PaymentOrderModel';
import {IPaymentOrderDataSource} from '../datasources/IPaymentOrderDataSource';
import {BaseResponse} from '../../core/BaseResponse';
import {IPaymentOrderRepository} from '../../domain/repositories/IPaymentOrderRepository';
import {PaymentOrderRequest} from '../models/payment-order/PaymentOrderRequest';
import {PaymentOrderStatusRequest} from '../models/payment-order-status/PaymentOrderStatusRequest';

export class PaymentOrderRepository implements IPaymentOrderRepository {
  private remoteDataSource: IPaymentOrderDataSource;

  constructor(remoteDataSource: IPaymentOrderDataSource) {
    this.remoteDataSource = remoteDataSource;
  }

  async paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderModel>> {
    return handleData<PaymentOrderModel>(this.remoteDataSource.paymentOrder(request), mapPaymentOrderResponseToModel);
  }

  async paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusModel>> {
    return handleData<PaymentOrderStatusModel>(
      this.remoteDataSource.paymentOrderStatus(request),
      mapPaymentOrderStatusResponseToModel,
    );
  }
}
