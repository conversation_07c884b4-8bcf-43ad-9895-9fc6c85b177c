import {PaymentOrderStatusResponse} from '../../models/payment-order-status/PaymentOrderStatusResponse';
import {
  PaymentOrderStatusAdditionalModel,
  PaymentOrderStatusModel,
} from '../../../domain/entities/payment-order-status/PaymentOrderStatusModel';

export function mapPaymentOrderStatusResponseToModel(response: PaymentOrderStatusResponse): PaymentOrderStatusModel {
  return new PaymentOrderStatusModel(
    response.status ?? '',
    new PaymentOrderStatusAdditionalModel(response.additions?.t24TraceCode ?? ''),
  );
}
