import {
  BillData,
  BillHistoryDTO,
  BillHistoryModel,
} from '../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts';
import {GetMyBillHistoryListResponse} from '../../models/get-my-bill-history-list/GetMyBillHistoryListResponse.ts';
import moment from 'moment/moment';

// Utility functions copied from account module
const formatPrice = (amount: number | string | null | undefined) => {
  if (amount == null || amount == undefined) {
    return '';
  }
  const num = typeof amount === 'string' ? Number(amount) : amount;
  if (isNaN(num)) {
    return '';
  }
  return num.toLocaleString('vi-VN').replace(/\./g, ',');
};

const formatDate = (date: string): string => {
  return moment(date, 'YYYY-MM-DD').format('DD/MM/YYYY');
};

export function mapGetMyBillHistoryListResponseToModel(response: GetMyBillHistoryListResponse): BillHistoryModel {
  // Group bills by paymentDate
  const grouped =
    response?.reduce((acc, bill) => {
      const dateKey = bill.paymentDate?.split('T')[0] || bill.creationTime?.split('T')[0]; // Extract date part
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push({
        id: bill.id,
        transName: bill.counterPartyName || bill.customerName || bill.billCode,
        content: bill.description || bill.content || `Bill Payment - ${bill.serviceCode}`,
        amount:
          bill.creditDebitIndicator === 'CRDT'
            ? `+${formatPrice(+(bill.transactionAmountCurrency?.amount || bill.totalAmount || 0))}`
            : `-${formatPrice(+(bill.transactionAmountCurrency?.amount || bill.totalAmount || 0))}`,
        transDate: bill.creationTime
          ? moment(bill.creationTime).format('HH:mm').toString()
          : bill.paymentDate
          ? moment(bill.paymentDate).format('HH:mm').toString()
          : '',
        creationDate: bill.creationTime || bill.paymentDate,
      });
      return acc;
    }, {} as Record<string, BillData[]>) ?? [];

  // Convert grouped object to DTO structure
  const billHistory: BillHistoryDTO[] = Object.entries(grouped).map(([date, bills]) => ({
    title: formatDate(date),
    data: bills,
  }));

  return {
    billHistoryDTO: billHistory,
    billHistory: response,
  };
}
