import {
  SourceAccountProductKindResponse,
  SourceAccountProductResponse,
  SourceAccountListResponse,
  SourceAccountResponse,
  SourceAccountUserPreferencesResponse,
} from '../../models/source-account-list/SourceAccountListResponse';
import {
  SourceAccountProductKindModel,
  SourceAccountProductModel,
  SourceAccountListModel,
  SourceAccountModel,
  SourceAccountUserPreferencesModel,
} from '../../../domain/entities/source-account-list/SourceAccountListModel';

export function mapSourceAccountListResponseToModel(response: SourceAccountListResponse): SourceAccountListModel {
  if (!response.totalCount || !Array.isArray(response?.data)) {
    return {totalCount: 0, data: []};
  }
  return {
    totalCount: response.totalCount ?? 0,
    data: response.data
      .map(account => mapSourceAccountResponseToModel(account))
      .filter(account => account?.userPreferences?.visible !== false),
  };
}

export const mapUserPreferencesResponseToModel = (
  response?: SourceAccountUserPreferencesResponse,
): SourceAccountUserPreferencesModel => {
  return {
    arrangementId: response?.arrangementId,
    alias: response?.alias,
    visible: response?.visible,
    favorite: response?.favorite,
    additions: response?.additions,
  };
};

export const mapProductKindResponseToModel = (
  response: SourceAccountProductKindResponse,
): SourceAccountProductKindModel => {
  return {
    externalKindId: response.externalKindId,
    kindName: response.kindName,
    kindUri: response.kindUri,
    expectsChildren: response.expectsChildren,
    additions: response.additions,
  };
};

export const mapProductResponseToModel = (response: SourceAccountProductResponse): SourceAccountProductModel => {
  return {
    id: response.id,
    translations: response.translations,
    additions: response.additions,
    externalId: response.externalId,
    externalTypeId: response.externalTypeId,
    typeName: response.typeName,
    productKind: mapProductKindResponseToModel(response.productKind),
  };
};

export const mapSourceAccountResponseToModel = (response: SourceAccountResponse): SourceAccountModel => {
  return {
    id: response.id,
    productKindName: response.productKindName,
    legalEntityIds: response.legalEntityIds,
    productId: response.productId,
    productTypeName: response.productTypeName,
    externalProductId: response.externalProductId,
    externalArrangementId: response.externalArrangementId,
    userPreferences: mapUserPreferencesResponseToModel(response.userPreferences),
    product: mapProductResponseToModel(response.product),
    state: response.state,
    parentId: response.parentId,
    subscriptions: response.subscriptions,
    isDefault: response.isDefault,
    cifNo: response.cifNo,
    virtualAccountInfos: response.virtualAccountInfos,
    additions: response.additions,
    name: response.name,
    bookedBalance: response.bookedBalance,
    availableBalance: response.availableBalance,
    creditLimit: response.creditLimit,
    currency: response.currency,
    externalTransferAllowed: response.externalTransferAllowed,
    urgentTransferAllowed: response.urgentTransferAllowed,
    accountOpeningDate: response.accountOpeningDate,
    accountHolderNames: response.accountHolderNames,
    bankAlias: response.bankAlias,
    BBAN: response.BBAN,
    IBAN: response.IBAN,
    BIC: response.BIC,
  };
};
