import {
  BillDetailBillResponse,
  BillDetailCustomerInfoResponse,
  BillDetailServiceResponse,
  GetBillDetailResponse,
} from '../../models/get-bill-detail/GetBillDetailResponse';
import {
  BillDetailBillModel,
  BillDetailCustomerInfoModel,
  BillDetailServiceModel,
  GetBillDetailModel,
} from '../../../domain/entities/get-bill-detail/GetBillDetailModel';

export function mapGetBillDetailResponseToModel(response: GetBillDetailResponse): GetBillDetailModel {
  return new GetBillDetailModel(
    response.billCode,
    response.service ? mapBillDetailServiceResponseToModel(response.service) : undefined,
    response.queryRef,
    response.customerInfo ? mapBillDetailCustomerInfoResponseToModel(response.customerInfo) : undefined,
    response.billList ? response.billList.map(mapBillDetailBillResponseToModel) : undefined,
    response.partnerRespCode,
    response.partnerRespDesc,
    response.partnerTraceSeq,
    response.result,
    response.extendData,
    response.paymentRule,
  );
}

function mapBillDetailBillResponseToModel(response: BillDetailBillResponse): BillDetailBillModel {
  return new BillDetailBillModel(
    response.id,
    response.no,
    response.amount,
    response.code,
    response.custCode,
    response.custName,
    response.period,
    response.fee,
    response.custAddress,
  );
}

function mapBillDetailCustomerInfoResponseToModel(
  response: BillDetailCustomerInfoResponse,
): BillDetailCustomerInfoModel {
  return new BillDetailCustomerInfoModel(response.cif, response.phone, response.acct, response.name, response.address);
}

function mapBillDetailServiceResponseToModel(response: BillDetailServiceResponse): BillDetailServiceModel {
  return new BillDetailServiceModel(response.code);
}
