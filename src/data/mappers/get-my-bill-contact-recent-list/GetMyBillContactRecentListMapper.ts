import {
  GetMyBillContactRecentListResponse,
  GetMyBillContactRecentResponse,
} from '../../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';
import {
  GetMyBillContactRecentListModel,
  GetMyBillContactRecentModel,
} from '../../../domain/entities/get-my-bill-contact-recent-list/GetMyBillContactRecentListModel';

export function mapGetMyBillContactRecentListResponseToModel(
  response: GetMyBillContactRecentListResponse,
): GetMyBillContactRecentListModel {
  return response.map(item => mapRecentBillContactResponseToModel(item));
}

export function mapRecentBillContactResponseToModel(
  response: GetMyBillContactRecentResponse,
): GetMyBillContactRecentModel {
  const data = new GetMyBillContactRecentModel(
    response.id,
    response.billCode,
    response.category,
    response.subGroupId,
    response.customerName,
    response.totalAmount,
    response.period,
    response.paymentDate,
    response.accountNumber,
    response.coreRef,
    response.serviceCode,
    response.arrangementId,
    response.paymentOrderId,
    response.cifNo,
  );

  console.log('LOG CONTACT', data);
  return data;
}
