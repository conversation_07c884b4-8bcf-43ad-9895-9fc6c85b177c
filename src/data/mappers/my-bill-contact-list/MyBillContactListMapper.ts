import {
  MyBillContactListModel,
  MyBillContactModel,
  AccountModel,
} from '../../../domain/entities/my-bill-contact-list/MyBillContactListModel';
import {
  MyBillContactListResponse,
  MyBillContactResponse,
  AccountResponse,
} from '../../models/my-bill-contact-list/MyBillContactListResponse';

export function mapMyBillContactListResponseToModel(response: MyBillContactListResponse): MyBillContactListModel {
  return response.map(bill => mapBillContactResponseToModel(bill));
}

function mapBillContactResponseToModel(response: MyBillContactResponse): MyBillContactModel {
  const data = new MyBillContactModel(
    response.id,
    response.name,
    response.alias,
    response.category,
    response.activeStatus,
    response.accessContextScope,
    response.accounts?.map(account => mapAccountResponseToModel(account)),
    response.additions?.payableAmount,
    response.additions?.favoriteStatus as 'ACTIVE' | 'INACTIVE',
    response.additions?.reminderStatus as 'ACTIVE' | 'INACTIVE',
  );

  console.log('LOG CONTACT', data);
  return data;
}

function mapAccountResponseToModel(account: AccountResponse): AccountModel {
  return new AccountModel(
    account.bankName,
    account.accountNumber,
    account.bankCode,
    account.accountType,
    account.externalId,
    account.bankPostCode,
  );
}
