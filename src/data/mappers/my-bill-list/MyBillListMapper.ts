import {
  AccountModel,
  MyBillContactListModel,
  MyBillContactModel,
} from '../../../domain/entities/my-bill-contact-list/MyBillContactListModel';
import {AccountResponse, MyBillContactResponse, MyBillListResponse} from '../../models/my-bill-list/MyBillListResponse';

export function mapMyBillListResponseToModel(response: MyBillListResponse): MyBillContactListModel {
  console.log('-------------LOG RESPONSE', response);
  return response.map(bill => mapBillContactResponseToModel(bill));
}

function mapBillContactResponseToModel(response: MyBillContactResponse): MyBillContactModel {
  const data = new MyBillContactModel(
    response.id,
    response.name,
    response.alias,
    response.category,
    response.activeStatus,
    response.accessContextScope,
    response.accounts?.map(account => mapAccountResponseToModel(account)),
  );

  console.log('LOG CONTACT', data, response);
  console.log('LOG RESPONSE', response);
  return data;
}

function mapAccountResponseToModel(account: AccountResponse): AccountModel {
  return new AccountModel(
    account.bankName,
    account.accountNumber,
    account.bankCode,
    account.accountType,
    account.externalId,
    account.bankPostCode,
  );
}
