import {ProviderListResponse} from '../../models/provider-list/ProviderListResponse';
import {ProviderListModel, ProviderModel} from '../../../domain/entities/provider-list/ProviderListModel';

export function mapProviderListResponseToModel(response: ProviderListResponse): ProviderListModel {
  return response.map(
    provider =>
      new ProviderModel(
        provider.subgroupId,
        provider.serviceCode,
        provider.categoryCode,
        provider.subgroupId,
        provider.subgroupNameVn,
        provider.subgroupNameEn,
        provider.partnerCode,
        provider.partnerName,
        provider.autoBillSupport,
        provider.voucherSupport,
        provider.phoneRequired,
        provider.isRecommend,
        provider.partnerType,
        provider.payFee,
        provider.type,
        provider.paymentSupport,
        provider.description,
      ),
  );
}
