export interface GetProfileResponse {
  id?: string;
  username?: string;
  customer?: Customer;
  internalUserId?: string;
  cif?: string;
  fullName?: string;
  firstName?: string;
  lastName?: string;
  icNo?: string;
  oldIcNo?: string;
  icExpiredDate?: string;
  icIssuedDate?: string;
  email?: string;
  dob?: string;
  serviceGroup?: string;
  segment?: string;
  branchCode?: string;
  currentAddress?: string;
  incomeCode?: string;
  incomeValue?: string;
  marriageStatus?: string;
  permanentAddress?: string;
  personalReferralCode?: string;
  referralCodeBy?: string;
  additions?: Additions;
  occupationCode?: string;
  occupationPosition?: string;
  programCode?: string;
  hubCode?: string;
  bioEnrollmentState?: string;
  icType?: string;
  gender?: string;
  icPlace?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: any;
  updatedAt?: any;
}

export interface Customer {
  id?: string;
  phoneNo?: string;
  type?: string;
  status?: string;
  additions?: Additions;
  createdBy?: any;
  updatedBy?: any;
  createdAt?: string;
  updatedAt?: string;
}

export interface Additions {}
