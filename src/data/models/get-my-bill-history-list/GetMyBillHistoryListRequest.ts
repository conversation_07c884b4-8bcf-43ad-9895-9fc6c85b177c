export interface GetMyBillHistoryListDataRequest {
  from: number;
  size: number;
  bookingDateLessThan?: string; // From date (e.g., "2025-01-01")
  bookingDateGreaterThan?: string; // From date (e.g., "2025-01-01")
  creditDebitIndicator?: 'CRDT' | 'DBIT';
  typeGroup: 'PAYMENT';
  orderBy?: 'creationTime';
  counterPartyName?: string;
  description?: string;
  counterPartyAccountNumber?: string;
  direction?: 'ASC' | 'DESC';
  arrangementId?: string;
  billCode?: string;
  serviceCode?: string;
}
export type GetMyBillHistoryListRequest = Partial<GetMyBillHistoryListDataRequest>;

export enum BillChannel {
  IB = 'IB',
  MB = 'MB',
  IBMB = 'IBMB',
}
