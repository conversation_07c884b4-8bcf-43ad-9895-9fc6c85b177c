import {BillHistoryData} from '../../../domain/entities/get-my-bill-history-list/GetMyBillHistoryListModel.ts';

export type GetMyBillHistoryListResponse = BillHistoryData[];

export interface GetMyBillHistoryList {
  id: string;
  billCode: string;
  category: string;
  subGroupId?: string;
  customerName?: string;
  totalAmount: number;
  period?: string;
  paymentDate: string;
  accountNumber: string;
  coreRef?: string;
  content?: string;
  serviceCode: string;
  arrangementId: string;
  paymentOrderId: string;
  cifNo: string;
  creditDebitIndicator?: 'CRDT' | 'DBIT';
  transactionAmountCurrency?: {
    amount: string;
    currencyCode: string;
  };
  creationTime: string;
  counterPartyName?: string;
  description?: string;
}
