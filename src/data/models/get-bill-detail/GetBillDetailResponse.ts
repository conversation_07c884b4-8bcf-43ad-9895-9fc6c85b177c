export interface GetBillDetailResponse {
  billCode?: string;
  service?: BillDetailServiceResponse;
  queryRef?: string;
  customerInfo?: BillDetailCustomerInfoResponse;
  billList?: BillDetailBillResponse[];
  partnerRespCode?: string;
  tranSeqCount?: number;
  partnerRespDesc?: string;
  partnerTraceSeq?: string;
  result?: string;
  extendData?: any;
  paymentRule?: number;
}

export interface BillDetailServiceResponse {
  code?: string;
}

export interface BillDetailCustomerInfoResponse {
  cif?: string;
  phone?: any;
  acct?: any;
  name?: string;
  address?: any;
}

export interface BillDetailBillResponse {
  id?: string;
  no?: any;
  amount?: number;
  code?: string;
  custCode?: any;
  custName?: string;
  period?: any;
  fee?: any;
  custAddress?: any;
}
