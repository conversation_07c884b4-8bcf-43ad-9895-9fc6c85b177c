export type MyBillListResponse = MyBillContactResponse[];

export interface MyBillContactResponse {
  id: string | undefined;
  accessContextScope: string | undefined;
  name: string | undefined;
  alias: string | undefined;
  category: string | undefined;
  activeStatus: string | undefined;
  accounts: AccountResponse[] | undefined;
}

export interface AccountResponse {
  accountNumber: string | undefined;
  bankCode: string | undefined;
  bankName?: string | undefined;
  externalId?: string | undefined;
  accountType?: string | undefined;
  bankPostCode?: string | undefined;
}
