export type MyBillContactListResponse = MyBillContactResponse[];

export interface MyBillContactResponse {
  id?: string | null;
  name?: string | null;
  alias?: string | null;
  category?: string | null;
  activeStatus?: string | null;
  accessContextScope?: string | null;
  accounts: AccountResponse[];
  additions: Additions;
}

export interface Additions {
  favoriteStatus: string;
  reminderStatus: string;
  payableAmount: string;
}
export interface AccountResponse {
  accountNumber: string;
  bankCode: string;
  accountType: string;
  bankName?: string;
  bankPostCode?: string;
  externalId?: string;
}
