export interface PaymentOrderStatusResponse {
  id?: string;
  status?: string;
  bankStatus?: string;
  reasonCode?: string;
  reasonText?: string;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  permissibleActions?: PermissibleActions;
  version?: number;
  intraLegalEntity?: boolean;
  originatorAccountCurrency?: string;
  confirmationId?: string;
  originator?: Originator;
  originatorAccount?: OriginatorAccount;
  instructionPriority?: string;
  requestedExecutionDate?: string;
  paymentMode?: string;
  paymentType?: string;
  transferTransactionInformation?: TransferTransactionInformation;
  totalAmount?: TotalAmount;
  edited?: boolean;
  additions?: Additions;
}

export interface PermissibleActions {
  approve?: boolean;
  finalApprove?: boolean;
  reject?: boolean;
  cancel?: boolean;
  delete?: boolean;
  edit?: boolean;
  create?: boolean;
  export?: boolean;
}

export interface Originator {
  name?: string;
  role?: string;
}

export interface OriginatorAccount {
  arrangementId?: string;
  identification?: Identification;
}

export interface Identification {
  identification?: string;
  schemeName?: string;
}

export interface TransferTransactionInformation {
  counterparty?: Counterparty;
  counterpartyAccount?: CounterpartyAccount;
  counterpartyBank?: CounterpartyBank;
  instructedAmount?: InstructedAmount;
  remittanceInformation?: RemittanceInformation;
}

export interface Counterparty {
  name?: string;
  role?: string;
}

export interface CounterpartyAccount {
  identification?: Identification2;
}

export interface Identification2 {
  identification?: string;
  schemeName?: string;
}

export interface CounterpartyBank {
  bankBranchCode?: string;
  name?: string;
}

export interface InstructedAmount {
  amount?: string;
  currencyCode?: string;
}

export interface RemittanceInformation {
  type?: string;
  content?: string;
}

export interface TotalAmount {
  amount?: string;
  currencyCode?: string;
}

export interface Additions {
  t24TraceCode?: string;
  napasTraceCode?: string;
}
