export interface SourceAccountListResponse {
  totalCount: number;
  data: SourceAccountResponse[];
}

export interface SourceAccountUserPreferencesResponse {
  arrangementId?: string;
  alias?: string;
  visible?: boolean | null;
  favorite?: boolean;
  additions?: any | null;
}

export interface SourceAccountProductKindResponse {
  externalKindId: string;
  kindName: string;
  kindUri: string;
  expectsChildren: boolean;
  additions: any | null;
}

export interface SourceAccountProductResponse {
  id: string;
  translations: any[];
  additions: any | null;
  externalId: string;
  externalTypeId: string | null;
  typeName: string;
  productKind: SourceAccountProductKindResponse;
}

export interface SourceAccountResponse {
  id: string;
  productKindName: string;
  legalEntityIds: string[];
  productId: string;
  productTypeName: string;
  externalProductId: string;
  externalArrangementId: string;
  userPreferences?: SourceAccountUserPreferencesResponse;
  product: SourceAccountProductResponse;
  state?: any | null;
  parentId?: string | null;
  subscriptions?: any | null;
  isDefault: string;
  cifNo: string;
  virtualAccountInfos?: any[];
  additions?: any | null;
  name: string;
  bookedBalance?: number | null;
  availableBalance?: number | null;
  creditLimit?: number | null;
  currency: string;
  externalTransferAllowed?: boolean | null;
  urgentTransferAllowed?: boolean | null;
  accountOpeningDate?: string | null;
  accountHolderNames?: string | null;
  bankAlias?: string;
  BBAN?: string;
  IBAN?: string | null;
  BIC?: string | null;
  amountQR?: string | null;
  contentQR?: string | null;
}
