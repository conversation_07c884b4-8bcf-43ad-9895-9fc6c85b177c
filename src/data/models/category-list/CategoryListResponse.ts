export type CategoryListResponse = {
  items: CategoryResponse[];
  totalPage: number;
  totalElements: number;
  page: number;
  size: number;
};

export interface ProductParam {
  id: number;
  code: string;
  name: string;
  dataType: string;
  dataValue: string;
  status: string;
  version: number;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  productParamHistories: any[];
}
export interface CategoryResponse {
  id: number;
  subDomainCode: string;
  subDomainName: string;
  domainCode: string;
  domainName: string;
  code: string;
  status: string;
  name: string;
  description: string | null;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
  productParams: ProductParam[];
}
