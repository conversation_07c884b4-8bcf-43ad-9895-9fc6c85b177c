export interface EditBillContactRequest {
  name: string;
  alias: string;
  category: string;
  accounts: AccountRequest[];
  additions?: AdditionsRequest;
  id: string;
}

export interface AccountRequest {
  bankName: string;
  accountType: string;
  accountNumber: string;
  bankCode: string | undefined;
  externalId?: string;
}

export interface AdditionsRequest {
  favoriteStatus: 'ACTIVE' | 'INACTIVE';
  reminderStatus: 'ACTIVE' | 'INACTIVE';
  payableAmount: string;
}
