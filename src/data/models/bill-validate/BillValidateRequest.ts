export interface BillValidateRequest {
  originatorAccount: {
    identification: {
      identification: string;
      schemeName: string;
    };
  };
  requestedExecutionDate: string; // ISO 8601 format date string
  paymentType: string;
  transferTransactionInformation: {
    instructedAmount: {
      amount: string; // if dynamic binding like "{{totalAmount}}", use string
      currencyCode: string;
    };
    counterparty: {
      name: string;
    };
    counterpartyAccount: {
      identification: {
        identification: string;
        schemeName: string;
      };
    };
    additions: {
      bpQueryRef: string;
      bpBillList: string; // or stringified JSON if passed as a string
      bpSummary: string;
      bpServiceCode: string;
      cifNo: string;
      bpCategory: string;
    };
  };
}
