export interface BillValidateResponse {
  id: string;
  originatorAccount: OriginatorAccountResponse;
  instructionPriority: string;
  requestedExecutionDate: string;
  paymentMode: string;
  paymentType: string;
  transferTransactionInformation: TransferTransactionInformationResponse;
  originator: OriginatorResponse;
  totalAmount: CurrencyAmountResponse;
  isIntraLegalEntityPaymentOrder: boolean;
  canApprove: boolean;
  finalApprover: boolean;
}

export interface OriginatorAccountResponse {
  arrangementId: string;
  externalArrangementId: string;
  identification: AccountIdentificationResponse;
}

export interface AccountIdentificationResponse {
  identification: string;
  schemeName: string;
}

export interface TransferTransactionInformationResponse {
  counterparty: CounterpartyResponse;
  counterpartyAccount: AccountIdentificationResponse;
  instructedAmount: CurrencyAmountResponse;
  additions: TransferAdditionsResponse;
}

export interface CounterpartyResponse {
  name: string;
  role: string;
}

export interface CurrencyAmountResponse {
  amount: string;
  currencyCode: string;
}

export interface TransferAdditionsResponse {
  bpQueryRef: string;
  bpBillList: string; // Hoặc: BillItemResponse[] nếu parse JSON
  bpSummary: string; // Hoặc: BpSummaryResponse nếu parse JSON
  bpServiceCode: string;
  cifNo: string;
  bpCategory: string;
  bpAccountingNumber: string;
}

export interface OriginatorResponse {
  name: string;
  role: string;
  postalAddress: Record<string, any>;
}
