export interface ValidateRequest {
  originatorAccount: OriginatorAccount;
  requestedExecutionDate: string;
  paymentType: string;
  transferTransactionInformation: TransferTransactionInformation;
}

export interface OriginatorAccount {
  identification: Identification;
}

export interface Identification {
  identification: string;
  schemeName: string;
}

export interface TransferTransactionInformation {
  instructedAmount: InstructedAmount;
  counterparty: Counterparty;
  counterpartyAccount: CounterpartyAccount;
  additions: Additions;
}

export interface InstructedAmount {
  amount: string;
  currencyCode: string;
}

export interface Counterparty {
  name: string;
}

export interface CounterpartyAccount {
  identification: Identification2;
}

export interface Identification2 {
  identification: string;
  schemeName: string;
}

export interface Additions {
  bpQueryRef: string;
  bpBillList: string;
  bpSummary: string;
  bpServiceCode: string;
  cifNo: string;
  bpCategory: string;
}
