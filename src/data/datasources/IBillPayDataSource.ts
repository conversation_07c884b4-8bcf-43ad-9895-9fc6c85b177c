import {GetBillDetailResponse} from '../models/get-bill-detail/GetBillDetailResponse';
import {GetBillDetailRequest} from '../models/get-bill-detail/GetBillDetailRequest';
import {BillValidateResponse} from '../models/bill-validate/BillValidateResponse';
import {MyBillListResponse} from '../models/my-bill-list/MyBillListResponse';
import {MyBillListRequest} from '../models/my-bill-list/MyBillListRequest';
import {ProviderListResponse} from '../models/provider-list/ProviderListResponse';
import {ProviderListRequest} from '../models/provider-list/ProviderListRequest';
import {CategoryListResponse} from '../models/category-list/CategoryListResponse';
import {BaseResponse} from '../../core/BaseResponse';
import {BillValidateRequest} from '../models/bill-validate/BillValidateRequest';

export interface IBillPayDataSource {
  categoryList(): Promise<BaseResponse<CategoryListResponse>>;
  providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListResponse>>;
  myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillListResponse>>;
  getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailResponse>>;
  billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateResponse>>;
}
