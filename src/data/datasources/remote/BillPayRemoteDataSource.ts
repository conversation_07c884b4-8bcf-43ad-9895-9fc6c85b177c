import {GetBillDetailResponse} from '../../models/get-bill-detail/GetBillDetailResponse';
import {GetBillDetailRequest} from '../../models/get-bill-detail/GetBillDetailRequest';
import {BillValidateResponse} from '../../models/bill-validate/BillValidateResponse';
import {MyBillListResponse} from '../../models/my-bill-list/MyBillListResponse';
import {MyBillListRequest} from '../../models/my-bill-list/MyBillListRequest';
import {ProviderListResponse} from '../../models/provider-list/ProviderListResponse';
import {ProviderListRequest} from '../../models/provider-list/ProviderListRequest';
import {CategoryListResponse} from '../../models/category-list/CategoryListResponse';
import {BaseResponse} from '../../../core/BaseResponse';
import {PathResolver} from '../../../utils/PathResolver';
import {IHttpClient} from 'msb-host-shared-module';
import {handleResponse} from '../../../utils/ResponseHandler';
import {IBillPayDataSource} from '../IBillPayDataSource';
import {BillValidateRequest} from '../../models/bill-validate/BillValidateRequest';
import {createError, CustomError} from '../../../core/MSBCustomError';

export class BillPayRemoteDataSource implements IBillPayDataSource {
  constructor(private httpClient: IHttpClient) {}

  async categoryList(): Promise<BaseResponse<CategoryListResponse>> {
    try {
      const url = PathResolver.billPay.categoryList();
      const response = await this.httpClient.get(url);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async providerList(request: ProviderListRequest): Promise<BaseResponse<ProviderListResponse>> {
    try {
      const url = PathResolver.billPay.providerList(request.code);
      const response = await this.httpClient.get(url);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async myBillList(request: MyBillListRequest): Promise<BaseResponse<MyBillListResponse>> {
    try {
      const url = PathResolver.billPay.myBillList();
      const response = await this.httpClient.post(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async getBillDetail(request: GetBillDetailRequest): Promise<BaseResponse<GetBillDetailResponse>> {
    try {
      const url = PathResolver.billPay.getBillDetail();
      const response = await this.httpClient.post(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async billValidate(request: BillValidateRequest): Promise<BaseResponse<BillValidateResponse>> {
    try {
      const url = PathResolver.billPay.billValidate();
      const response = await this.httpClient.post(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }
}
