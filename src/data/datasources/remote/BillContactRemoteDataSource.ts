import {GetMyBillHistoryListResponse} from '../../models/get-my-bill-history-list/GetMyBillHistoryListResponse';
import {GetMyBillHistoryListRequest} from '../../models/get-my-bill-history-list/GetMyBillHistoryListRequest';
import {GetMyBillContactRecentListResponse} from '../../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';
import {GetMyBillContactRecentListRequest} from '../../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';
import {MyBillContactListResponse} from '../../models/my-bill-contact-list/MyBillContactListResponse';
import {EditBillContactResponse} from '../../models/edit-bill-contact/EditBillContactResponse';
import {EditBillContactRequest} from '../../models/edit-bill-contact/EditBillContactRequest';
import {DeleteBillContactResponse} from '../../models/delete-bill-contact/DeleteBillContactResponse';
import {DeleteBillContactRequest} from '../../models/delete-bill-contact/DeleteBillContactRequest';
import {BaseResponse} from '../../../core/BaseResponse';
import {PathResolver} from '../../../utils/PathResolver';
import {IHttpClient} from 'msb-host-shared-module';
import {handleResponse} from '../../../utils/ResponseHandler';
import {SaveBillContactResponse} from '../../models/save-bill-contact/SaveBillContactResponse';
import {SaveBillContactRequest} from '../../models/save-bill-contact/SaveBillContactRequest';
import {IBillContactDataSource} from '../IBillContactDataSource';
import {ContactType} from '../../../commons/Constants';
import {createError, CustomError} from '../../../core/MSBCustomError';

export class BillContactRemoteDataSource implements IBillContactDataSource {
  constructor(private httpClient: IHttpClient) {}

  async saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactResponse>> {
    try {
      const url = PathResolver.billContact.saveBillContact();
      const response = await this.httpClient.post(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactResponse>> {
    try {
      const url = PathResolver.billContact.deleteBillContact(request.id);
      const response = await this.httpClient.delete(url);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactResponse>> {
    try {
      const url = PathResolver.billContact.editBillContact(request.id);
      const response = await this.httpClient.put(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async myBillContactList(): Promise<BaseResponse<MyBillContactListResponse>> {
    try {
      const url = PathResolver.billContact.myBillContactList(ContactType.BILLPAY);
      const response = await this.httpClient.get(url);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async getMyBillContactRecentList(
    request: GetMyBillContactRecentListRequest,
  ): Promise<BaseResponse<GetMyBillContactRecentListResponse>> {
    try {
      const url = PathResolver.billContact.getMyBillContactRecentList();
      const response = await this.httpClient.get(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async getMyBillHistoryList(
    request: GetMyBillHistoryListRequest,
  ): Promise<BaseResponse<GetMyBillHistoryListResponse>> {
    try {
      const url = PathResolver.billContact.getMyBillHistoryList();
      const queryParams = new URLSearchParams(request as any).toString();
      const fullUrl = `${url}?${queryParams}`;
      const response = await this.httpClient.get(fullUrl);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }
}
