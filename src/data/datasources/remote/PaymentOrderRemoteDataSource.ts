import {PaymentOrderStatusResponse} from '../../models/payment-order-status/PaymentOrderStatusResponse';
import {handleResponse} from '../../../utils/ResponseHandler';
import {PaymentOrderResponse} from '../../models/payment-order/PaymentOrderResponse';
import {BaseResponse} from '../../../core/BaseResponse';
import {PathResolver} from '../../../utils/PathResolver';
import {hostSharedModule, IHttpClient} from 'msb-host-shared-module';
import {IPaymentOrderDataSource} from '../IPaymentOrderDataSource';
import {PaymentOrderRequest} from '../../models/payment-order/PaymentOrderRequest';
import {PaymentOrderStatusRequest} from '../../models/payment-order-status/PaymentOrderStatusRequest';
import {createError, CustomError} from '../../../core/MSBCustomError';

export class PaymentOrderRemoteDataSource implements IPaymentOrderDataSource {
  constructor(private httpClient: IHttpClient) {}

  async paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderResponse>> {
    try {
      const url = PathResolver.paymentOrder.paymentOrder();
      console.log('url', url);
      console.log('request', request);
      const response = await hostSharedModule.d.domainService?.onTransfer({
        url: url,
        method: 'POST',
        body: JSON.stringify(request),
      });
      return response as BaseResponse<PaymentOrderResponse>;
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }

  async paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusResponse>> {
    try {
      if (Object.keys(request).includes('id')) {
        const url = PathResolver.paymentOrder.paymentOrderStatus(request.id ?? '');
        const response = await this.httpClient.get(url);
        return handleResponse(response);
      }
      const url = PathResolver.paymentOrder.paymentOrder();
      const queryParams = new URLSearchParams(request as any).toString();
      const fullUrl = `${url}?${queryParams}`;
      const response = await this.httpClient.get(fullUrl);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }
}
