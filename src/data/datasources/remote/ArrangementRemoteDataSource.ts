import {BaseResponse} from '../../../core/BaseResponse';
import {PathResolver} from '../../../utils/PathResolver';
import {IHttpClient} from 'msb-host-shared-module';
import {handleResponse} from '../../../utils/ResponseHandler';
import {SourceAccountListResponse} from '../../models/source-account-list/SourceAccountListResponse';
import {SourceAccountListRequest} from '../../models/source-account-list/SourceAccountListRequest';
import {IArrangementDataSource} from '../IArrangementDataSource';
import {createError, CustomError} from '../../../core/MSBCustomError';

export class ArrangementRemoteDataSource implements IArrangementDataSource {
  constructor(private httpClient: IHttpClient) {}

  async sourceAccountList(request: SourceAccountListRequest): Promise<BaseResponse<SourceAccountListResponse>> {
    try {
      const url = PathResolver.arrangement.sourceAccountList();
      const response = await this.httpClient.post(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }
}
