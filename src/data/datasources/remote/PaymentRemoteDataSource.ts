import {BaseResponse} from '../../../core/BaseResponse';
import {PathResolver} from '../../../utils/PathResolver';
import {IHttpClient} from 'msb-host-shared-module';
import {handleResponse} from '../../../utils/ResponseHandler';
import {ValidateResponse} from '../../models/validate/ValidateResponse';
import {ValidateRequest} from '../../models/validate/ValidateRequest';
import {IPaymentDataSource} from '../IPaymentDataSource';
import {createError, CustomError} from '../../../core/MSBCustomError';

export class PaymentRemoteDataSource implements IPaymentDataSource {
  constructor(private httpClient: IHttpClient) {}

  async validate(request: ValidateRequest): Promise<BaseResponse<ValidateResponse>> {
    try {
      const url = PathResolver.payment.validate();
      const response = await this.httpClient.post(url, request);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }
}
