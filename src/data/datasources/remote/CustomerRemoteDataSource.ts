import {GetProfileResponse} from '../../models/get-profile/GetProfileResponse';
import {BaseResponse} from '../../../core/BaseResponse';
import {PathResolver} from '../../../utils/PathResolver';
import {IHttpClient} from 'msb-host-shared-module';
import {handleResponse} from '../../../utils/ResponseHandler';
import {ICustomerDataSource} from '../ICustomerDataSource';
import {createError, CustomError} from '../../../core/MSBCustomError';

export class CustomerRemoteDataSource implements ICustomerDataSource {
  constructor(private httpClient: IHttpClient) {}

  async getProfile(): Promise<BaseResponse<GetProfileResponse>> {
    try {
      const url = PathResolver.customer.getProfile();
      const response = await this.httpClient.get(url);
      return handleResponse(response);
    } catch (error: any) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw createError();
    }
  }
}
