import {GetMyBillHistoryListResponse} from '../models/get-my-bill-history-list/GetMyBillHistoryListResponse';
import {GetMyBillHistoryListRequest} from '../models/get-my-bill-history-list/GetMyBillHistoryListRequest';
import {GetMyBillContactRecentListResponse} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListResponse';
import {GetMyBillContactRecentListRequest} from '../models/get-my-bill-contact-recent-list/GetMyBillContactRecentListRequest';
import {MyBillContactListResponse} from '../models/my-bill-contact-list/MyBillContactListResponse';
import {EditBillContactResponse} from '../models/edit-bill-contact/EditBillContactResponse';
import {EditBillContactRequest} from '../models/edit-bill-contact/EditBillContactRequest';
import {DeleteBillContactResponse} from '../models/delete-bill-contact/DeleteBillContactResponse';
import {DeleteBillContactRequest} from '../models/delete-bill-contact/DeleteBillContactRequest';
import {BaseResponse} from '../../core/BaseResponse';
import {SaveBillContactResponse} from '../models/save-bill-contact/SaveBillContactResponse';
import {SaveBillContactRequest} from '../models/save-bill-contact/SaveBillContactRequest';

export interface IBillContactDataSource {
  saveBillContact(request: SaveBillContactRequest): Promise<BaseResponse<SaveBillContactResponse>>;
  deleteBillContact(request: DeleteBillContactRequest): Promise<BaseResponse<DeleteBillContactResponse>>;
  editBillContact(request: EditBillContactRequest): Promise<BaseResponse<EditBillContactResponse>>;
  myBillContactList(): Promise<BaseResponse<MyBillContactListResponse>>;
  getMyBillContactRecentList(
    request: GetMyBillContactRecentListRequest,
  ): Promise<BaseResponse<GetMyBillContactRecentListResponse>>;
  getMyBillHistoryList(request: GetMyBillHistoryListRequest): Promise<BaseResponse<GetMyBillHistoryListResponse>>;
}
