import {PaymentOrderStatusResponse} from '../models/payment-order-status/PaymentOrderStatusResponse';
import {PaymentOrderResponse} from '../models/payment-order/PaymentOrderResponse';
import {BaseResponse} from '../../core/BaseResponse';
import {PaymentOrderRequest} from '../models/payment-order/PaymentOrderRequest';
import {PaymentOrderStatusRequest} from '../models/payment-order-status/PaymentOrderStatusRequest';

export interface IPaymentOrderDataSource {
  paymentOrder(request: PaymentOrderRequest): Promise<BaseResponse<PaymentOrderResponse>>;
  paymentOrderStatus(request: PaymentOrderStatusRequest): Promise<BaseResponse<PaymentOrderStatusResponse>>;
}
