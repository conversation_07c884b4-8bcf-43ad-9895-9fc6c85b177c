import {HighlightTextProps} from './types';
import {ColorGlobal, MSBTextBase} from 'msb-shared-component';
import React from 'react';

const HighlightText = ({text, search, style, ...restProps}: HighlightTextProps) => {
  if (!search) {
    return <MSBTextBase style={style} content={text} {...restProps} />;
  }

  const regex = new RegExp(`(${search.replace(/[.*+?^=!:${}()|[\]/\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <MSBTextBase style={style}>
      {parts.map((part, index) =>
        part.toLowerCase() === search.toLowerCase() ? (
          <MSBTextBase key={index} style={[style, {color: ColorGlobal.Brand500}]} content={part} />
        ) : (
          <MSBTextBase style={style} key={index} content={part} />
        ),
      )}
    </MSBTextBase>
  );
};

export default HighlightText;
