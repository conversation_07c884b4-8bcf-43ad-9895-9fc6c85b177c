import {Text} from 'react-native';
import {HighlightTextProps} from './types';
import {ColorGlobal} from 'msb-shared-component';
import React from 'react';

const HighlightText = ({text, search, style, ...restProps}: HighlightTextProps) => {
  if (!search) {
    return (
      <Text style={style} {...restProps}>
        {text}
      </Text>
    );
  }

  const regex = new RegExp(`(${search.replace(/[.*+?^=!:${}()|[\]/\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <Text style={style}>
      {parts.map((part, index) =>
        part.toLowerCase() === search.toLowerCase() ? (
          <Text key={index} style={[style, {color: ColorGlobal.Brand500}]}>
            {part}
          </Text>
        ) : (
          <Text style={style} key={index}>
            {part}
          </Text>
        ),
      )}
    </Text>
  );
};

export default HighlightText;
