import {
  createMSBStyleSheet,
  MAX_WIDTH,
  MSBFastImage,
  MSBIcon,
  MSBIcons,
  MSBTextBase,
  MSBTouchable,
  useMSBStyles,
} from 'msb-shared-component';
import React, {useState} from 'react';
import {Text, View, useWindowDimensions} from 'react-native';
import {TabBar, TabView} from 'react-native-tab-view';
import {FlatList} from 'react-native-gesture-handler';

import DimensionUtils from '../../utils/DimensionUtils';
import FormatUtils from '../../utils/FormatUtils';
import {SourceAccountListProps} from './types';
import {SourceAccountModel} from '../../domain/entities/source-account-list/SourceAccountListModel';
import Images from '../../assets/images/Images';
import {hostSharedModule} from 'msb-host-shared-module';

const AccountTab = ({accountList, onSelectAcount, accSelected}: SourceAccountListProps) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  const onSelectAccount = (item: SourceAccountModel) => {
    if (onSelectAcount) {
      onSelectAcount(item);
    }
  };

  return (
    <View style={styles.listContainer}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={accountList}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({item, index}) => (
          <MSBTouchable
            style={styles.wrapItem}
            onPress={() => onSelectAccount(item)}
            testID={
              item.isDefault === 'Y'
                ? 'transfer.sourceAccountList.selectAccount.default'
                : `transfer.sourceAccountList.selectAccount.${index}`
            }>
            <View style={styles.itemContainer}>
              <View style={styles.accNumber}>
                <Text style={styles.txtBank}>{item?.BBAN}</Text>
                <View style={styles.dot} />
                <Text style={styles.txtName} numberOfLines={1}>
                  {item?.userPreferences?.alias || item?.bankAlias}
                </Text>
              </View>
              <Text style={styles.txtAmount}>
                {`${FormatUtils.formatPrice(item?.availableBalance)}`}

                <Text style={styles.txtCurrency}> VND</Text>
              </Text>
              {item.isDefault === 'Y' && (
                <View style={styles.accDefault}>
                  <Text style={styles.txtDefault}>{'Mặc định'}</Text>
                </View>
              )}
            </View>
            {item?.id === accSelected?.id && (
              <MSBIcon
                iconColor={theme.ColorItem.IconBrand}
                icon={MSBIcons.IconCheck}
                iconSize={theme.SizeGlobal.Size800}
              />
            )}
          </MSBTouchable>
        )}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const CreditCardTab = ({cardList, onSelectCard, cardSelected}: any) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  const onSelectCreditCard = (item: any) => {
    console.log('Selected Credit Card:', item);
    if (onSelectCard) {
      hostSharedModule.d.domainService.undevelopedFeature();
    }
  };

  if (!cardList || cardList.length === 0) {
    return (
      <View style={styles.emptyTabContent}>
        <Text style={styles.emptyTabText}>{'Không có dữ liệu'}</Text>
      </View>
    );
  }

  return (
    <View style={styles.listContainer}>
      <FlatList
        showsVerticalScrollIndicator={false}
        data={cardList}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({item, index}) => (
          <MSBTouchable
            style={styles.wrapItem}
            onPress={() => onSelectCreditCard(item)}
            testID={`payment.cardList.selectCard.${index}`}>
            <View style={styles.itemContainer}>
              <View style={styles.cardRow}>
                <View style={styles.cardImageContainer}>
                  <View style={styles.cardImage}>
                    <MSBFastImage source={Images.cardTest} style={{width: 60, height: 40}} resizeMode="cover" />
                  </View>
                </View>
                <View style={styles.cardInfo}>
                  <Text style={styles.txtBank}>{item?.cardName || 'MSB Visa Signature'}</Text>
                  <Text style={styles.txtCardNumber}>{item?.maskedCardNumber || '******* 9898'}</Text>
                  <Text style={styles.txtAmount}>
                    {`${FormatUtils.formatPrice(item?.availableBalance || *********)}`}
                    <Text style={styles.txtCurrency}> VND</Text>
                  </Text>
                  {item.isDefault === 'Y' && (
                    <View style={styles.accDefault}>
                      <Text style={styles.txtDefault}>{'Mặc định'}</Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
            {item?.id === cardSelected?.id && (
              <MSBIcon
                iconColor={theme.ColorItem.IconBrand}
                icon={MSBIcons.IconCheck}
                iconSize={theme.SizeGlobal.Size800}
              />
            )}
          </MSBTouchable>
        )}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

const SourceAccountList = (props: SourceAccountListProps) => {
  const layout = useWindowDimensions();
  const {styles} = useMSBStyles(makeStyle);
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    {key: 'account', title: 'Tài khoản'},
    {key: 'creditCard', title: 'Thẻ tín dụng'},
  ]);

  // Mock data for credit cards
  const mockCreditCards = [
    {
      id: '1',
      cardName: 'MSB Visa Signature',
      maskedCardNumber: '******* 9898',
      availableBalance: *********,
      isDefault: 'Y',
    },
    {
      id: '2',
      cardName: 'MSB Visa Signature',
      maskedCardNumber: '******* 9898',
      availableBalance: *********,
      isDefault: 'N',
    },
    {
      id: '3',
      cardName: 'MSB Visa Signature',
      maskedCardNumber: '******* 9898',
      availableBalance: *********,
      isDefault: 'N',
    },
  ];

  return (
    <TabView
      testID="transfer.sourceAccountList.changeTab"
      onIndexChange={setIndex}
      navigationState={{index, routes}}
      renderScene={({route}) => {
        switch (route.key) {
          case 'account':
            return <AccountTab {...props} />;
          case 'creditCard':
            return (
              <CreditCardTab
                cardList={mockCreditCards}
                onSelectCard={props.onSelectAcount}
                cardSelected={props.accSelected}
              />
            );
          default:
            return null;
        }
      }}
      initialLayout={{width: layout.width}}
      swipeEnabled={true}
      renderTabBar={tabProps => (
        <TabBar
          {...tabProps}
          style={styles.tabBar}
          indicatorStyle={styles.indicator}
          contentContainerStyle={styles.tabContainer}
          renderTabBarItem={({route, onPress}) => {
            const focused = index === tabProps.navigationState.routes.findIndex(r => r.key === route.key);
            return (
              <MSBTouchable onPress={onPress} style={styles.tabItem}>
                <MSBTextBase style={[styles.tabText, focused && styles.tabTextActive]} content={route.title} />
                {focused && <View style={styles.tabIndicator} />}
              </MSBTouchable>
            );
          }}
        />
      )}
    />
  );
};

export default SourceAccountList;

const makeStyle = createMSBStyleSheet(
  ({ColorGlobal, ColorToggle, ColorField, ColorDataView, ColorTag, SizeGlobal, Typography}) => {
    return {
      cardRow: {
        flexDirection: 'row',
        alignItems: 'center',
      },
      cardImageContainer: {
        marginRight: SizeGlobal.Size300,
      },
      cardImage: {
        width: 60,
        height: 40,
        borderRadius: SizeGlobal.Size200,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
      },
      cardInfo: {
        flex: 1,
      },
      txtCardNumber: {
        ...Typography?.small_regular,
        color: ColorDataView.TextSub,
        marginBottom: SizeGlobal.Size100,
      },
      accDefault: {
        alignItems: 'center',
        backgroundColor: ColorTag.SurfaceBlue,
        borderRadius: SizeGlobal.Size300,
        marginTop: SizeGlobal.Size100,
        paddingVertical: SizeGlobal.Size100,
        width: DimensionUtils.getWindowWidth() / 4,
      },
      accNumber: {
        alignItems: 'center',
        flexDirection: 'row',
      },
      container: {
        backgroundColor: ColorGlobal.NeutralWhite,

        flex: 1,
      },
      listContainer: {
        flex: 1,
        marginHorizontal: SizeGlobal.Size400,
      },
      dot: {
        backgroundColor: ColorToggle.SurfaceDisable,
        borderRadius: 2,
        height: SizeGlobal.Size100,
        marginHorizontal: SizeGlobal.Size200,
        width: SizeGlobal.Size100,
      },
      itemContainer: {
        paddingVertical: 16,
        flex: 1,
      },
      separator: {
        backgroundColor: ColorField.BorderDefault,
        height: 1,
      },
      txtAmount: {
        ...Typography?.base_semiBold,
        color: ColorDataView.TextMain,
        marginTop: SizeGlobal.Size100,
      },
      txtCurrency: {
        ...Typography?.base_semiBold,
        color: ColorGlobal.Neutral500,
      },
      txtBank: {
        ...Typography?.small_regular,
        color: ColorDataView.TextSub,
      },
      txtName: {
        ...Typography?.small_regular,
        color: ColorDataView.TextSub,
        flex: 1,
        marginRight: SizeGlobal.Size200,
      },
      txtDefault: {
        ...Typography?.small_medium,
        color: ColorTag.TextBlue,
      },
      wrapItem: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
      },

      titleText: {
        ...Typography?.h4_semiBold,
      },
      tabBar: {
        backgroundColor: ColorGlobal.NeutralWhite,
        elevation: 2,
        shadowColor: ColorGlobal.Neutral300,
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.2,
        shadowRadius: 2,
        borderBottomWidth: 1,
        borderBottomColor: ColorGlobal.Neutral100,
      },
      tabContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        flex: 1,
      },
      tabIndicator: {
        backgroundColor: ColorGlobal.Brand500,
        bottom: 0,
        height: SizeGlobal.Size50,
        position: 'absolute',
        width: '100%',
      },
      tabItem: {
        alignItems: 'center',
        width: MAX_WIDTH / 2,
        paddingVertical: SizeGlobal.Size300,
      },
      tabText: {
        ...Typography?.base_regular,
        // color: ColorGlobal.Black,
      },
      tabTextActive: {
        ...Typography?.base_semiBold,
        color: ColorGlobal.Brand500,
      },
      indicator: {
        height: 0,
      },
      emptyTabContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      },
      emptyTabText: {
        ...Typography?.base_regular,
        color: ColorGlobal.Neutral500,
      },
    };
  },
);
