// for test

import React from 'react';
import {View} from 'react-native';
import {
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  MSBTextBase,
  SizeGlobal as Si<PERSON>,
  <PERSON>ze<PERSON><PERSON><PERSON>,
  useMSBStyles,
} from 'msb-shared-component';
import {PaymentBillInfoProps} from './types';
import {translate} from '../../locales/i18n';

const PaymentBillInfo = ({style, merchantName, storeId}: PaymentBillInfoProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <View style={[styles.container, style]}>
      <MSBTextBase style={styles.txtBank}>{translate('qrPaymentInfo.merchantName')}</MSBTextBase>
      <View style={styles.customerInfo}>
        <MSBFastImage
          folder={MSBFolderImage.ICON_SVG}
          nameImage="tone-bill"
          style={{width: Size.Size800, height: Size.Size800}}
        />
        <View style={styles.accInfo}>
          <MSBTextBase style={[styles.txtName]} content={merchantName} />

          <View style={styles.accNumber}>
            <MSBTextBase style={[styles.txtBank]} numberOfLines={1} content={storeId} />
          </View>
        </View>
      </View>
    </View>
  );
};

export default PaymentBillInfo;

export const makeStyle = createMSBStyleSheet(({ColorDataView, Typography, SizeGlobal}) => {
  return {
    accInfo: {
      flex: 1,
      marginLeft: SizeGlobal.Size300,
      marginTop: SizeGlobal.Size100,
    },

    customerInfo: {
      alignItems: 'center',
      flexDirection: 'row',
      paddingBottom: SizeAlias.Spacing4xSmall,
    },
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: SizeAlias.Spacing4xSmall,
      // flex: 1,
    },
    container: {
      // backgroundColor: ColorGlobal.NeutralWhite,
    },

    txtBank: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    txtName: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },
  };
});
