import {TextStyle, ViewStyle} from 'react-native';
import {MSBIcons} from 'msb-shared-component';

import {SafeAny} from '../../commons/Constants';

export type ContactItemProps = {
  style?: ViewStyle;
  styleName?: TextStyle;
  styleBankName?: TextStyle;
  styleAccountNo?: TextStyle;
  onPress?: (tag?: string) => SafeAny;
  title?: string;
  icon?: MSBIcons;
  name?: string;
  bankName?: string;
  bankAlias?: string;
  isNotShowBankName?: boolean;
  searchText?: string;
};
