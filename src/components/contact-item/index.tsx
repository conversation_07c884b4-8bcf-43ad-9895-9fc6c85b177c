// for test

import {View} from 'react-native';
import {
  ColorAlias,
  ColorItem,
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  useMSBStyles,
} from 'msb-shared-component';

import {ContactItemProps} from './types';
import HighlightText from '../highlight-text';
import React from 'react';

const ContactItem = ({style, bankName, name, searchText, bankAlias, isTopup}: ContactItemProps) => {
  const {styles} = useMSBStyles(makeStyle);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.beneficiaryInfo}>
        {/* <Image source={Utils.getProviderIcon(bankName || '')} style={styles.icLogo} /> */}
        <MSBFastImage
          nameImage={bankName}
          style={styles.icLogo}
          folder={isTopup ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
        />
        <View style={styles.accInfo}>
          <HighlightText style={styles.txtName} text={name ?? ''} search={searchText ?? ''} />
          <View style={styles.accNumber}>
            <HighlightText style={styles.txtBank} text={bankName ?? ''} search={searchText ?? ''} />
            <View style={styles.dot} />
            <HighlightText style={[styles.txtBank, {flex: 1}]} text={bankAlias ?? ''} search={searchText ?? ''} />
          </View>
        </View>
      </View>
    </View>
  );
};

export default ContactItem;
const makeStyle = createMSBStyleSheet(({Typography, SizeAlias, ColorDataView, SizeGlobal}) => {
  return {
    accInfo: {
      flex: 1,
      marginLeft: SizeAlias.SpacingXSmall,
    },
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: SizeAlias.Spacing4xSmall,
      // flex: 1,
    },
    beneficiaryInfo: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: SizeAlias.Spacing4xSmall,
    },
    container: {
      backgroundColor: ColorAlias.BackgroundWhite,
      paddingVertical: SizeAlias.SpacingMedium,
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    dot: {
      backgroundColor: ColorItem.TextSub,
      borderRadius: SizeGlobal.Size50,
      height: SizeGlobal.Size100,
      marginHorizontal: SizeAlias.Spacing2xSmall,
      width: SizeGlobal.Size100,
    },
    icLogo: {
      height: SizeGlobal.Size800,
      width: SizeGlobal.Size800,
    },

    txtBank: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    txtName: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },
  };
});
