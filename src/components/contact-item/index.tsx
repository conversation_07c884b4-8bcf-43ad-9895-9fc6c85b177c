// for test

import {View} from 'react-native';
import {
  ColorAlias,
  ColorItem,
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  useMSBStyles,
} from 'msb-shared-component';

import {ContactItemProps} from './types';
import HighlightText from '../highlight-text';
import React from 'react';

const ContactItem = ({style, bankName, name, searchText, bankAlias}: ContactItemProps) => {
  const {styles} = useMSBStyles(makeStyle);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.beneficiaryInfo}>
        {/* <Image source={Utils.getProviderIcon(bankName || '')} style={styles.icLogo} /> */}
        <MSBFastImage nameImage={bankName} style={styles.icLogo} folder={MSBFolderImage.LOGO_BILLING} />
        <View style={styles.accInfo}>
          <HighlightText style={styles.txtName} text={name ?? ''} search={searchText ?? ''} />
          <View style={styles.accNumber}>
            <HighlightText style={styles.txtBank} text={bankName ?? ''} search={searchText ?? ''} />
            <View style={styles.dot} />
            <HighlightText style={[styles.txtBank, {flex: 1}]} text={bankAlias ?? ''} search={searchText ?? ''} />
          </View>
        </View>
      </View>
    </View>
  );
};

export default ContactItem;
const makeStyle = createMSBStyleSheet(({Typography}) => {
  return {
    accInfo: {
      flex: 1,
      marginLeft: 12,
    },
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 4,
      // flex: 1,
    },
    beneficiaryInfo: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 4,
    },
    container: {
      backgroundColor: ColorAlias.BackgroundWhite,
      paddingVertical: 20,
      paddingHorizontal: 16,
    },
    dot: {
      backgroundColor: ColorItem.TextSub,
      borderRadius: 2,
      height: 4,
      marginHorizontal: 8,
      width: 4,
    },
    icLogo: {
      height: 32,
      width: 32,
    },

    txtBank: {
      ...Typography?.small_regular,
      color: ColorItem.TextSub,
    },
    txtName: {
      ...Typography?.base_medium,
      color: ColorItem.TextMain,
    },
  };
});
