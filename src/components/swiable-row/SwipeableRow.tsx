import React from 'react';
import {StyleSheet, View} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {
  interpolate,
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import {SizeGlobal} from 'msb-shared-component';

type Props = {
  children: React.ReactNode;
  renderRightActions: (translateX: SharedValue<number>) => React.ReactNode;
  disabledSwipe?: boolean;
};
const nativeGesture = Gesture.Native(); // Detect native scroll
export const SwipeableRow: React.FC<Props> = props => {
  const translateX = useSharedValue(0);
  const prevTranslationX = useSharedValue(0);

  const panGesture = Gesture.Pan()
    .minPointers(1)
    .maxPointers(1)
    .activeOffsetX([-10, 10])
    .hitSlop({horizontal: -20})
    .onStart(() => {
      prevTranslationX.value = translateX.value;
    })
    .onUpdate(event => {
      if (translateX.value > 0) {
        // Change this condition to prevent swiping left
        translateX.value = 1;
        return;
      }
      if (translateX.value <= -120) {
        translateX.value = -120;
        return;
      }
      translateX.value = event.translationX + prevTranslationX.value;
    })
    .onEnd(() => {
      if (translateX.value < -SizeGlobal.Size1200 * 2) {
        translateX.value = withSpring(-SizeGlobal.Size1200 * 2);
      } else {
        translateX.value = withSpring(0);
      }
    })
    .simultaneousWithExternalGesture(nativeGesture)
    .requireExternalGestureToFail(nativeGesture);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
  }));

  const animatedStyleAndroid = useAnimatedStyle(() => {
    const opacity = interpolate(translateX.value, [0, -SizeGlobal.Size1200 * 2], [0, 1]);
    return {
      opacity: opacity,
    };
  });

  return props.disabledSwipe ? (
    <View pointerEvents="box-none" style={styles.container}>
      <Animated.View style={animatedStyle}>{props.children}</Animated.View>
      <Animated.View style={[styles.row, animatedStyleAndroid]}>{props.renderRightActions?.(translateX)}</Animated.View>
    </View>
  ) : (
    <GestureDetector gesture={panGesture}>
      {/*<View style={{justifyContent: 'center'}}>*/}
      <View pointerEvents="box-none" style={styles.container}>
        <Animated.View style={animatedStyle}>{props.children}</Animated.View>
        <Animated.View style={[styles.row, animatedStyleAndroid]}>
          {props.renderRightActions?.(translateX)}
        </Animated.View>
      </View>
    </GestureDetector>
  );
  // return <Swipeable renderRightActions={renderRightActions}>{props.children}</Swipeable>;
};

const styles = StyleSheet.create({
  container: {justifyContent: 'center'},
  row: {position: 'absolute', right: 0, height: '100%'},
});
