import {
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  MSBLoadingItemSkeleton,
  MSBSelection,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';
import React, {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {Keyboard, View} from 'react-native';

import {hostSharedModule} from 'msb-host-shared-module';
import {FlatList} from 'react-native-gesture-handler';
import {DIContainer} from '../../di/DIContainer';
import {translate} from '../../locales/i18n';
import {showCommonPopup} from '../../utils/PopupUtils';
import {ProviderListModel, ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import {ProviderSelectionRef, ProviderSelectionProps, ComponentState} from './types';
import {ProviderListRequest} from '../../data/models/provider-list/ProviderListRequest';
import ProviderList from '../provider-list/ProviderList';

const MSBProviderSelection = memo(
  forwardRef<ProviderSelectionRef, ProviderSelectionProps>(({disabled, code, onSelected, defaultValue}, ref) => {
    // STATE
    const {styles, theme} = useMSBStyles(makeStyle);

    const [providerList, setProviderList] = useState<ComponentState<ProviderListModel>>({
      isLoading: false,
      data: [],
      error: null,
    });

    const [selectedItem, setSelectedItem] = useState<ProviderModel>();
    const [isBottomSheetOpen, setIsBottomSheetOpen] = useState<boolean>(false);

    const getProviderList = useCallback(async () => {
      setProviderList(preState => {
        return {
          ...preState,
          isLoading: true,
          data: [],
        };
      });
      if (!code) {
        showCommonPopup();
        return;
      }
      const request: ProviderListRequest = {code};
      const result = await DIContainer.getInstance().getProviderListUseCase().execute(request);
      if (result.status === 'ERROR') {
        showCommonPopup(result.error);
        // handle error
        setProviderList(preState => {
          return {
            ...preState,
            isLoading: false,
            data: [],
            error: result.error,
          };
        });
        return;
      }

      // handle success
      if (result.status === 'SUCCESS') {
        if (result.data?.length === 1) {
          // handle case only 1 item
          setSelectedItem(result.data[0]);
          onSelected(result.data[0]);
        }
        setProviderList(preState => {
          return {
            ...preState,
            isLoading: false,
            data: result.data,
          };
        });
      }
    }, [code, onSelected]);

    // HOOKS
    useImperativeHandle(ref, () => ({
      resetSelected() {
        setSelectedItem(undefined);
      },
      isBottomSheetOpen,
    }));

    useEffect(() => {
      getProviderList();
    }, [getProviderList]);

    // handle for case pass defaultSelectedBank
    useEffect(() => {
      if (defaultValue) {
        setSelectedItem(defaultValue.providerSelected);
      }
    }, [defaultValue, providerList]);

    // useEffect(() => {
    //   if (selectedItem && selectedItem.isRetrieveBranch) {
    //     getBankBranchList(selectedItem.bankNo);
    //   }
    // }, [selectedItem]);

    // Emit event to parent when bank or bank branch is selected
    useEffect(() => {
      // xử lý cho case normal
      if (selectedItem) {
        onSelected(selectedItem);
      }
    }, [selectedItem, onSelected]);

    // FUNCTIONS

    const handleSelectBank = useCallback((item: ProviderModel) => {
      hostSharedModule.d.domainService?.hideBottomSheet();
      setSelectedItem(item);
      setIsBottomSheetOpen(false);
    }, []);

    const openBottomSheetBank = () => {
      if (disabled) {
        return;
      }
      Keyboard.dismiss();
      setIsBottomSheetOpen(true);
      hostSharedModule.d.domainService?.showBottomSheet({
        header: translate('paymentBill.labelBank'),
        children: _renderUIProviderList,
        snapToIndex: 80,
      });
    };

    // Tối ưu FlatList trong component ProviderSelection
    const _renderUIProviderList = useMemo(() => {
      if (providerList.isLoading) {
        return (
          <View style={styles.bankLoading}>
            <FlatList
              data={[1, 2, 3, 4, 5, 6, 7, 8]}
              keyExtractor={(_, index) => index.toString()}
              getItemLayout={(_, index) => ({
                length: 60, // chiều cao của mỗi item
                offset: 60 * index,
                index,
              })}
              renderItem={() => (
                <View style={styles.bankLoadingItem}>
                  <MSBLoadingItemSkeleton loading={providerList.isLoading} />
                </View>
              )}
            />
          </View>
        );
      }

      return <ProviderList list={providerList.data} onClick={handleSelectBank} defaultValue={selectedItem} />;
    }, [providerList, selectedItem, handleSelectBank]);

    return (
      <View style={[styles.bankContainer]}>
        {/* List of bank */}
        <MSBSelection
          testID={'payment.beneficiaryScreen.selectProviderBeneficiary'}
          disabled={disabled}
          key={selectedItem?.serviceCode}
          label={translate('paymentBill.labelSelecboxProvider')}
          onChange={openBottomSheetBank}
          childrenContent={
            selectedItem?.partnerName && (
              <View style={styles.bankItem}>
                {selectedItem.getIconName() ? (
                  // <Image source={Utils.getProviderIcon(selectedItem.getIconName())} style={styles.bankLogo} />
                  <MSBFastImage
                    nameImage={selectedItem.getIconName()}
                    style={styles.bankLogo}
                    folder={selectedItem.isTopup() ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
                  />
                ) : (
                  <MSBFastImage nameImage={'tone-bill'} style={styles.bankLogo} folder={MSBFolderImage.ICON_SVG} />
                )}
                <MSBTextBase type={theme.Typography?.title_semiBold} content={selectedItem?.getName()} />
              </View>
            )
          }
          placeholder={translate('paymentBill.hintSelecboxProvider')}
        />
        {/* End */}
      </View>
    );
  }),
);

export default MSBProviderSelection;

const makeStyle = createMSBStyleSheet(({Typography, ColorItem, SizeGlobal, SizeAlias}) => {
  return {
    bankContainer: {
      gap: SizeGlobal.Size500,
    },
    bankItem: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    bankLogo: {
      height: SizeGlobal.Size800,
      marginRight: SizeAlias.SpacingSmall,
      resizeMode: 'stretch',
      width: SizeGlobal.Size800,
    },
    bankLoading: {
      paddingHorizontal: SizeAlias.SpacingSmall,
    },
    bankLoadingItem: {
      paddingVertical: SizeAlias.Spacing2xSmall,
      borderBottomWidth: 1,
      borderBottomColor: ColorItem.SurfaceLoading,
    },
  };
});
