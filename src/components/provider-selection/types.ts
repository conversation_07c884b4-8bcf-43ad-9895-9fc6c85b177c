import {StyleProp, ViewStyle} from 'react-native';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';

export type ProviderSelectionState = {
  providerSelected?: ProviderModel;
};

export type ProviderSelectionProps = {
  rootStyle?: StyleProp<ViewStyle>;
  code: string;
  disabled?: boolean;
  defaultValue?: ProviderSelectionState;
  onSelected: (value: ProviderModel) => void;
};

export type ProviderSelectionRef = {
  resetSelected: () => void;
  isBottomSheetOpen: boolean;
};

export type ComponentState<T> = {
  isLoading: boolean;
  data: T | any;
  error: any;
};
