import {MSBInputBase} from 'msb-shared-component';
import React from 'react';

import {TransferContentInputProps} from './types';
import {translate} from '../../locales/i18n';
import Utils from '../../utils/Utils';

const TransferContentInput = ({
  value,
  label = translate('common.content'),
  placeholder = translate('common.contentPlaceholder'),
  onChangeText,
  disabled,
  ...restProps
}: TransferContentInputProps) => {
  const handleBlur = (): void => {
    onChangeText?.(Utils.normalizeSpaces(Utils.regexTransferContent(value)));
  };

  return (
    <MSBInputBase
      label={label}
      value={value}
      onBlur={handleBlur}
      onChangeText={onChangeText}
      maxLength={140}
      disabled={disabled}
      isDisableRemoveIcon={disabled}
      placeholder={placeholder}
      textInputProps={{
        keyboardType: 'ascii-capable',
        returnKeyLabel: 'Done',
        returnKeyType: 'done',
      }}
      {...restProps}
    />
  );
};

export default TransferContentInput;
