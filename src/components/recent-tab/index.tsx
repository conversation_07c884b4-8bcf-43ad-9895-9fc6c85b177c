import {
  ColorDataView,
  ColorItem,
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  MSBSearchInput,
  MSBTextBase,
  MSBTouchable,
  useMSBStyles,
} from 'msb-shared-component';
import React from 'react';
import {Image, View} from 'react-native';
import {translate} from '../../locales/i18n';
import {RecentTabProps} from './types';
import {FlatList} from 'react-native-gesture-handler';
import ContactItem from '../contact-item';
import Utils from '../../utils/Utils';
// import Images from '../../assets/images/Images';
import {useMemo, useState} from 'react';

export const RecentTab = ({recentContact, onSelect}: RecentTabProps) => {
  const {styles} = useMSBStyles(makeStyle);

  const [searchText, setSearchText] = useState('');

  const filteredAccountContact = useMemo(() => {
    return recentContact.filter(contact => {
      const temp =
        Utils.regexTransformToEnglishCharacter(contact.alias?.toLowerCase() ?? '') +
        Utils.regexTransformToEnglishCharacter(contact.bankName?.toLowerCase() ?? '') +
        Utils.regexTransformToEnglishCharacter(contact.accNumber?.toLowerCase() ?? '') +
        Utils.regexTransformToEnglishCharacter(contact.accName?.toLowerCase() ?? '');
      return temp.includes(Utils.regexTransformToEnglishCharacter(searchText).trim().toLowerCase());
    });
  }, [recentContact, searchText]);

  const onSearch = (text: string) => {
    setSearchText(text);
  };

  const _onSelect = (item: any) => {
    if (onSelect) {
      onSelect(item);
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.containerSearchInput}>
        <MSBSearchInput
          testID={'payment.SavedTab.enterSearch'}
          value={searchText}
          placeholder={'Tên, số tài khoản'}
          maxLength={255}
          onChangeText={onSearch}
        />
      </View>
      <FlatList
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.containerEmpty}>
            <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
            <MSBTextBase style={styles.textTitleEmpty} content={translate('billingTab.titleEmptyContactFiltered')} />
            <MSBTextBase
              style={styles.textTitleEmptyDes}
              content={translate('billingTab.contentEmptyContactFiltered')}
            />
          </View>
        }
        data={filteredAccountContact ?? []}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item}) => (
          <View>
            {item.title?.isShow && (
              <View style={styles.titleContainer}>
                <MSBTextBase style={styles.title} content={item.title?.title} />
              </View>
            )}
            <MSBTouchable onPress={() => _onSelect(item)}>
              <ContactItem
                searchText={searchText}
                // style={styles.itemContainer}
                name={item.alias ?? item.accName ?? ''}
                bankName={item.bankName}
                bankAlias={item.accNumber ?? ''}
              />
            </MSBTouchable>
          </View>
        )}
      />
    </View>
  );
};

const makeStyle = createMSBStyleSheet(({Typography}) => {
  return {
    container: {
      flex: 1,
    },
    containerSearchInput: {
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: ColorItem.BorderDivider,
    },
    searchInput: {
      marginEnd: 15,
    },
    separator: {
      backgroundColor: ColorItem.BorderDivider,
      height: 1,
      marginHorizontal: 16,
    },
    title: {
      ...Typography?.small_medium,
      color: ColorDataView.TextMain,
    },
    titleContainer: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      backgroundColor: '#F7F8F9',
    },
    itemContainer: {
      borderBottomWidth: 1,
      borderBottomColor: ColorItem.BorderDivider,
    },

    //empty
    containerEmpty: {
      alignItems: 'center',
      flexDirection: 'column',
      flex: 1,
      justifyContent: 'center',
      width: '100%',
    },
    image: {
      height: 144,
      width: 144,
      marginVertical: 40,
    },
    textAlign: {
      textAlign: 'center',
    },
    textTitleEmpty: {
      ...Typography?.base_semiBold,
    },
    textTitleEmptyDes: {
      ...Typography?.small_regular,
      marginTop: 4,
    },
  };
});
