import {MSBInputBase} from 'msb-shared-component';
import React from 'react';

import {translate} from '../../locales/i18n';

const TransferAccountNumberInput = ({
  label = translate('common.accountNumber'),
  placeholder = translate('common.accountNumberPlaceholder'),
  onChangeText,
  disabled,
  ...restProps
}: MSBInputBaseProps) => {
  return (
    <MSBInputBase
      label={label}
      onChangeText={onChangeText}
      maxLength={34}
      disabled={disabled}
      isDisableRemoveIcon={disabled}
      placeholder={placeholder}
      {...restProps}
    />
  );
};

export default TransferAccountNumberInput;
