// for test

import React from 'react';
import {View} from 'react-native';
import {createMSBStyleSheet, MSBFastImage, MSBFolderImage, MSBTextBase, useMSBStyles} from 'msb-shared-component';

import {BillInfoProps} from './types';

const BillInfo = ({
  style,
  styleName,
  styleBankName,
  styleAccountNo,
  title,
  bankAlias,
  bankName,
  bankLogo,
  name,
  isNotShowBankName,
  isUppercase,
}: BillInfoProps) => {
  const {styles} = useMSBStyles(makeStyle);

  return (
    <View style={[styles.container, style]}>
      <MSBTextBase style={styles.title} content={title} />
      <View style={styles.beneficiaryInfo}>
        {/* <Image source={bankLogo ? Utils.getProviderIcon(bankLogo) : Utils.getDefaultIcon('WC')} style={styles.icLogo} /> */}
        <MSBFastImage nameImage={bankLogo} style={styles.icLogo} folder={MSBFolderImage.LOGO_BILLING} />
        <View style={styles.accInfo}>
          <MSBTextBase style={[styles.txtName, styleName]} content={isUppercase ? name?.toUpperCase() : name} />
          {!isNotShowBankName && (
            <View style={styles.accNumber}>
              <MSBTextBase style={[styles.txtBank, styleBankName]} numberOfLines={1} content={bankName} />
              <View style={styles.dot} />
              <MSBTextBase style={[styles.txtBank, {flex: 1}, styleAccountNo]} numberOfLines={1} content={bankAlias} />
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default BillInfo;
const makeStyle = createMSBStyleSheet(({ColorAlias, ColorGlobal, ColorDataView, Typography}) => {
  return {
    accInfo: {
      flex: 1,
      marginLeft: 12,
    },
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 4,
      // flex: 1,
    },
    beneficiaryInfo: {
      alignItems: 'center',
      flexDirection: 'row',
      marginTop: 4,
    },
    container: {
      backgroundColor: ColorGlobal.NeutralWhite,
    },
    dot: {
      backgroundColor: ColorDataView.IconDefault,
      borderRadius: 2,
      height: 4,
      marginHorizontal: 8,
      width: 4,
    },
    icLogo: {
      height: 32,
      width: 32,
    },
    title: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    txtBank: {
      ...Typography?.small_regular,
      color: ColorDataView.IconDefault,
    },
    txtName: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
    },
  };
});
