import {useState} from 'react';
import {View} from 'react-native';
import {TabBar, TabView, SceneRendererProps, NavigationState} from 'react-native-tab-view';
import {BottomSheetView} from '@gorhom/bottom-sheet';

import {createMSBStyleSheet, getSize, MSBTextBase, MSBTouchable, SizeAlias, useMSBStyles} from 'msb-shared-component';

import {translate} from '../../locales/i18n';
import {SavedTab} from '../saved-tab';
import {RecentTab} from '../recent-tab';
import DimensionUtils from '../../utils/DimensionUtils';
import {ContabTabProps} from './types';
import React from 'react';

type RouteType = {
  key: string;
  title: string;
};

const ContactTab = ({contactList, recentContact, onSelect}: ContabTabProps) => {
  const [index, setIndex] = useState<number>(0);
  const {styles} = useMSBStyles(makeStyle);

  const [routes] = useState<RouteType[]>([
    {key: 'saved', title: translate('billingTab.titleSaved')},
    {key: 'recent', title: translate('billingTab.titleRecent')},
  ]);

  const renderScene = ({route}: {route: RouteType}) => {
    switch (route.key) {
      case 'saved':
        return <SavedTab contactList={contactList} onSelect={onSelect} />;
      case 'recent':
        return <RecentTab recentContact={recentContact} />;
      default:
        return null;
    }
  };

  return (
    <BottomSheetView
      style={{
        paddingBottom: DimensionUtils.getPaddingBottomByDevice(),
        height: (DimensionUtils.getWindowHeight() * 80) / 100,
      }}>
      <TabView
        testID="payment.ContactTab.changeTab"
        navigationState={{index, routes}}
        renderScene={renderScene}
        onIndexChange={setIndex}
        swipeEnabled={false}
        renderTabBar={(tabBarProps: SceneRendererProps & {navigationState: NavigationState<RouteType>}) => (
          <TabBar
            {...tabBarProps}
            style={styles.tabBar}
            indicatorStyle={styles.indicator}
            contentContainerStyle={styles.tabContainer}
            renderTabBarItem={({route, onPress}) => {
              const focused = index === routes.findIndex(r => r.key === route.key);
              return (
                <MSBTouchable onPress={onPress} style={styles.tabItem} testID={`payment.moneyHubScreen.${route.title}`}>
                  <MSBTextBase style={[styles.tabText, focused && styles.tabTextActive]} content={route.title} />
                  {focused && <View style={styles.tabIndicator} />}
                </MSBTouchable>
              );
            }}
          />
        )}
      />
    </BottomSheetView>
  );
};
export default ContactTab;

const makeStyle = createMSBStyleSheet(({Typography, ColorGlobal, SizeGlobal}) => {
  return {
    indicator: {
      backgroundColor: ColorGlobal.Brand500,
    },
    tabBar: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderTopEndRadius: SizeAlias.Radius3,
    },
    tabContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    tabIndicator: {
      backgroundColor: ColorGlobal.Brand500,
      bottom: 0,
      height: SizeGlobal.Size50,
      position: 'absolute',
      width: getSize(86),
    },
    tabItem: {
      alignItems: 'center',
      flex: 1,
      paddingVertical: SizeGlobal.Size300,
    },
    tabText: {
      ...Typography?.base_regular,
      // color: ColorGlobal.Black,
    },
    tabTextActive: {
      ...Typography?.base_semiBold,
      color: ColorGlobal.Brand500,
    },
  };
});
