import React from 'react';
import {View} from 'react-native';
import {
  ColorField,
  ColorItem,
  ColorLabelCaption,
  Fonts,
  MSBTouchable,
  MSBTextBase,
  MSBFolderImage,
  MSBIcon,
  createMSBStyleSheet,
  useMSBStyles,
  SizeAlias,
} from 'msb-shared-component';

import {SoureAccountProps} from './types';
import FormatUtils from '../../utils/FormatUtils';

const SourceAccount = ({title, account, onSelectAccount, errorTitle}: SoureAccountProps) => {
  const {styles} = useMSBStyles(makeStyle);

  const _onSelectAccount = () => {
    if (onSelectAccount) {
      onSelectAccount();
    }
  };

  return (
    <MSBTouchable
      testID={'payment.sourceAccount.selectSourceAccount'}
      style={[styles.container]}
      activeOpacity={onSelectAccount ? 0.8 : 1}
      onPress={_onSelectAccount}>
      <MSBTextBase style={styles.title}>{title}</MSBTextBase>
      <View style={[styles.sourceAccInfo, errorTitle && styles.errorContainer]}>
        <View style={styles.flex}>
          <View style={styles.accNumber}>
            <MSBTextBase style={styles.txtAcc}>{account?.BBAN}</MSBTextBase>
            <View style={styles.dot} />
            <MSBTextBase style={styles.txtBank}>{account?.bankAlias}</MSBTextBase>
          </View>
          <MSBTextBase style={styles.txtAmount}>
            {FormatUtils.formatPrice(account?.availableBalance)}
            <MSBTextBase style={styles.txtCurrency}> VND</MSBTextBase>
          </MSBTextBase>
        </View>
        {onSelectAccount && (
          <MSBIcon
            folderIcon={MSBFolderImage.ICON_SVG}
            icon={'down'}
            onIconClick={_onSelectAccount}
            styleContainer={styles.marginLeft16}
          />
        )}
      </View>
      {errorTitle && <MSBTextBase style={styles.errorTitle}>{errorTitle}</MSBTextBase>}
    </MSBTouchable>
  );
};

export default SourceAccount;

const makeStyle = createMSBStyleSheet(({ColorGlobal, SizeGlobal, Typography}) => {
  return {
    accNumber: {
      alignItems: 'center',
      flexDirection: 'row',
      flex: 1,
    },
    container: {
      // backgroundColor: ColorAlias.BackgroundWhite,
    },
    dot: {
      backgroundColor: ColorItem.IconDot,
      borderRadius: SizeGlobal.Size50,
      height: SizeGlobal.Size100,
      marginHorizontal: SizeGlobal.Size200,
      width: SizeGlobal.Size100,
    },
    flex: {
      flex: 1,
    },
    icDown: {
      color: ColorField.IconDefault,
      fontFamily: Fonts.msb_font_icon,
      fontSize: SizeGlobal.Size600,
    },

    marginLeft16: {
      marginLeft: SizeGlobal.Size400,
    },
    sourceAccInfo: {
      alignItems: 'center',
      borderColor: ColorField.BorderDefault,
      borderRadius: SizeGlobal.Size200,
      borderWidth: 1,
      flexDirection: 'row',
      marginTop: SizeGlobal.Size100,
      padding: SizeGlobal.Size400,
    },
    title: {
      ...Typography?.small_medium,
      color: ColorLabelCaption.TextMain,
    },
    txtAcc: {
      ...Typography?.small_regular,
      color: ColorItem.TextSub,
    },
    txtAmount: {
      ...Typography?.base_semiBold,
      color: ColorItem.TextMain,
      marginTop: SizeAlias.Spacing4xSmall,
    },
    txtCurrency: {
      ...Typography?.base_regular,
      color: ColorGlobal.Neutral500,
    },
    txtBank: {
      ...Typography?.small_regular,
      color: ColorItem.TextSub,
      flex: 1,
    },
    errorContainer: {
      borderColor: ColorGlobal.Red500,
      borderWidth: 1,
    },
    errorTitle: {
      ...Typography?.small_regular,
      color: ColorGlobal.Red500,
      marginTop: SizeGlobal.Size100,
    },
  };
});
