import React from 'react';
import {View, Image} from 'react-native';
import Animated, {interpolate, SharedValue, useAnimatedStyle} from 'react-native-reanimated';
import {
  ColorGlobal,
  MSBButton,
  MSBIcon,
  MSBIcons,
  MSBTextBase,
  MSBTouchable,
  SizeGlobal,
  useMSBStyles,
} from 'msb-shared-component';

import {SwipeableRow} from '../swiable-row/SwipeableRow';
import Utils from '../../utils/Utils';
import HighlightText from '../highlight-text';
import Images from '../../assets/images/Images';
import {makeStyle} from './style';
import {translate} from '../../locales/i18n';
import {IBillContact} from '../../domain/entities/IBillContact';

interface BillProps {
  item: IBillContact;
  highlight: string;
  onClick: (item: any) => void;
  editOnClick?: (item: any) => void;
  deleteOnClick?: (item: any) => void;
  onPressPayment?: (item: any) => void;
}

const SwipeableBillItem = ({item, highlight, onClick, editOnClick, deleteOnClick, onPressPayment}: BillProps) => {
  const {styles, theme} = useMSBStyles(makeStyle);

  return (
    <SwipeableRow
      disabledSwipe={item.isEditable()}
      renderRightActions={(translateX: SharedValue<number>) => {
        const animatedWidthStyle = useAnimatedStyle(() => {
          const width = interpolate(
            translateX.value,
            [0, -SizeGlobal.Size1600 * 2],
            [0, SizeGlobal.Size2400 + SizeGlobal.Size100],
          );

          return {
            width,
          };
        });

        return (
          <MSBTouchable
            onPress={() => {
              editOnClick?.(item);
              translateX.value = 0;
            }}
            style={styles.editAnimated}>
            <Animated.View style={[styles.childViewAnimated, animatedWidthStyle]}>
              <MSBIcon
                iconColor={ColorGlobal.White}
                icon={MSBIcons.IconEdit}
                onIconClick={() => {
                  editOnClick?.(item);
                  translateX.value = 0;
                }}
              />
              <MSBTextBase
                style={{
                  ...theme.Typography?.caption_regular,
                  color: ColorGlobal.White,
                }}
                content={translate('edit')}
              />
            </Animated.View>

            <MSBTouchable
              onPress={() => {
                translateX.value = 0;
                deleteOnClick?.(item);
              }}>
              <Animated.View style={[styles.deleteAnimated, animatedWidthStyle]}>
                <MSBIcon
                  iconColor={ColorGlobal.White}
                  icon={MSBIcons.IconTrashDeleteBin}
                  onIconClick={() => {
                    deleteOnClick?.(item);
                    translateX.value = 0;
                  }}
                />
                <MSBTextBase
                  style={{
                    ...theme.Typography?.caption_regular,
                    color: ColorGlobal.White,
                  }}
                  content={translate('delete')}
                />
              </Animated.View>
            </MSBTouchable>
          </MSBTouchable>
        );
      }}>
      <BillItem item={item} highlight={highlight} onClick={onClick} onPressPayment={onPressPayment} />
    </SwipeableRow>
  );
};

const BillItem = ({item, highlight, onClick, onPressPayment}: BillProps) => {
  const {styles} = useMSBStyles(makeStyle);
  return (
    <MSBTouchable onPress={() => onClick(item)}>
      <View style={styles.container}>
        {Utils.getProviderIcon(item.getIcon()) ? (
          <Image source={Utils.getProviderIcon(item.getIcon())} style={styles.icon} />
        ) : (
          <Image source={Images.icBill} style={styles.icon} />
        )}

        <View style={styles.subContainer}>
          <HighlightText style={styles.title} text={item.getCustomerName() ?? ''} search={highlight} />

          <View style={styles.subTitleContainer}>
            <HighlightText style={styles.subTitle} text={item.getSubtitle() ?? ''} search={highlight} />
            <Image source={Images.icDot} style={styles.dot} />
            <HighlightText style={styles.subTitleFlex} text={item.getBillCode?.() ?? ''} search={highlight} />
          </View>
        </View>

        {onPressPayment && item.getReminderStatus?.() === 'ACTIVE' && (
          <View>
            <MSBButton
              buttonType="Secondary"
              label={translate('common.payment')}
              onPress={() => {
                onPressPayment?.(item);
              }}
            />
          </View>
        )}
      </View>
    </MSBTouchable>
  );
};

export {SwipeableBillItem, BillItem};
