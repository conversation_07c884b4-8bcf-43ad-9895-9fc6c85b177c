import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(
  ({Typography, ColorGlobal, ColorDataView, ColorAlias, SizeGlobal, SizeAlias}) => {
    return {
      container: {flexDirection: 'row', alignItems: 'center', padding: SizeAlias.SpacingSmall},
      logo: {
        height: SizeGlobal.Size800,
        marginRight: SizeAlias.SpacingSmall,
        resizeMode: 'contain',
        width: SizeGlobal.Size800,
      },
      subContainer: {flexDirection: 'column', flex: 1, justifyContent: 'center'},
      subTitleContainer: {flexDirection: 'row', alignItems: 'center'},
      icon: {
        height: SizeGlobal.Size800,
        marginRight: SizeAlias.SpacingSmall,
        resizeMode: 'contain',
        width: SizeGlobal.Size800,
      },
      title: {
        ...Typography?.base_semiBold,
        color: ColorDataView.TextMain,
        marginBottom: SizeAlias.Spacing2xSmall,
      },
      subTitle: {...Typography?.base_regular, color: ColorDataView.TextSub},
      subTitleFlex: {...Typography?.base_regular, color: ColorDataView.TextSub, flex: 1},
      dot: {
        width: SizeGlobal.Size200,
        height: SizeGlobal.Size200,
        resizeMode: 'contain',
        paddingHorizontal: SizeAlias.SpacingXMSmall,
      },
      deleteAnimated: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        backgroundColor: ColorGlobal.Red500,
      },
      editAnimated: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: ColorGlobal.Blue500,
        flex: 1,
      },
      childViewAnimated: {alignItems: 'center', justifyContent: 'center'},
    };
  },
);
