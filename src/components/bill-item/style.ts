import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyle = createMSBStyleSheet(({Typography, ColorGlobal}) => {
  return {
    container: {flexDirection: 'row', alignItems: 'center', padding: 16},
    logo: {
      height: 32,
      marginRight: 16,
      resizeMode: 'contain',
      width: 32,
    },
    subContainer: {flexDirection: 'column', flex: 1, justifyContent: 'center'},
    subTitleContainer: {flexDirection: 'row', alignItems: 'center'},
    icon: {
      height: 32,
      marginRight: 16,
      resizeMode: 'contain',
      width: 32,
    },
    title: {
      fontSize: 16,
      color: '#091E42',
      fontWeight: '600',
      marginBottom: 8,
    },
    subTitle: {fontSize: 14, color: '#6B788E', fontWeight: '400'},
    subTitleFlex: {fontSize: 14, color: '#6B788E', fontWeight: '400', flex: 1},
    dot: {width: 4, height: 4, resizeMode: 'contain', paddingHorizontal: 10},
    deleteAnimated: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      backgroundColor: ColorGlobal.Red500,
    },
    editAnimated: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: ColorGlobal.Blue500,
      flex: 1,
    },
    childViewAnimated: {alignItems: 'center', justifyContent: 'center'},
  };
});
