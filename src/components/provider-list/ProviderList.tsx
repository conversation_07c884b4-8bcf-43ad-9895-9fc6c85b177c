import React, {useState} from 'react';
import {View, StyleProp, ScrollView} from 'react-native';
import {
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  MSBSearchInput,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';

// import {ProviderModel} from '../../../data/models/model.ts';
import {FlatList} from 'react-native-gesture-handler';

import {BottomSheetView} from '@gorhom/bottom-sheet';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import DimensionUtils from '../../utils/DimensionUtils';
// import Images from '../../assets/images/Images';
import Utils from '../../utils/Utils';
import {ProviderItem} from './ProviderItem';
// import Images from '../../assets/images/Images';

interface ProviderListProps {
  list: ProviderModel[];
  onClick?: (item: ProviderModel) => void;
  defaultValue?: ProviderModel | null;
}

const ProviderList = ({list: list, onClick, defaultValue}: ProviderListProps) => {
  const [searchText, setSearchText] = useState('');
  let content;
  let filteredProviders: ProviderModel[] | null = null;
  if (Utils.isEmpty(list) || list === undefined) {
    content = <EmptyBankSystemErrorScreen />;
  } else {
    filteredProviders = list?.filter(
      provider =>
        provider.description?.toLowerCase().includes((searchText.trim() ?? '').toLowerCase()) ||
        provider.subgroupNameVn?.toLowerCase().includes((searchText.trim() ?? '').toLowerCase()),
    );
  }

  if (filteredProviders == null) {
    content = <EmptyBankSystemErrorScreen />;
  } else if (filteredProviders.length === 0) {
    content = <EmptyBankFilteredScreen />;
  } else {
    content = (
      <FlatList
        data={filteredProviders}
        // renderItem={({ item }) => <BankItem item={item} highlight={searchText} />}
        renderItem={({item, index}) => (
          <ProviderItem
            defaultValue={defaultValue}
            item={item}
            highlight={searchText.trim()}
            onClick={bankItem => {
              onClick && onClick(bankItem);
            }}
            index={index}
          />
        )}
        keyExtractor={item => item.serviceCode ?? ''}
        // contentContainerStyle={styles.listContainer}
      />
    );
  }
  return (
    <BottomSheetView style={{width: '100%', flexDirection: 'column', height: DimensionUtils.getWindowHeight() * 0.8}}>
      <SearchComponent
        onSearch={output => {
          console.log('pdz', output);
          setSearchText(output);
        }}
      />
      {content}
    </BottomSheetView>
  );
};

const SearchComponent = ({onSearch}: {onSearch: (text: string) => void}) => {
  const [searchText, setSearchText] = useState('');
  const {styles, theme} = useMSBStyles(makeStyle);

  const handleTextChange = (text: string) => {
    setSearchText(text);
    onSearch(text);
  };

  return (
    <View>
      <MSBSearchInput
        testID={'transfer.beneficiaryScreen.searchBank'}
        placeholder={'Nhập nội dung'}
        value={searchText}
        maxLength={255}
        onChangeText={handleTextChange}
        isInputBottomSheet
        containerSearchStyle={styles.containerSearchInput}
      />
      <View style={{height: 1, backgroundColor: theme.ColorAlias.BorderDefault}} />
    </View>
  );
};

const EmptyBankFilteredScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <ScrollView>
      <View style={styles.containerEmpty}>
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase style={theme.Typography?.base_semiBold} content="Không tìm thấy kết quả" />
        <MSBTextBase style={theme.Typography?.small_regular} content="Vui lòng tìm với từ khoá khác" />
      </View>
    </ScrollView>
  );
};

const EmptyBankSystemErrorScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <ScrollView>
      <View style={styles.containerEmpty}>
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase style={theme.Typography?.base_semiBold} content="Hệ thống gián đoạn" />
        <MSBTextBase
          style={[theme.Typography?.small_regular, styles.textAlign]}
          content="Quý khách vui lòng thử lại. Xin lỗi vì sự bất tiện này."
        />
      </View>
    </ScrollView>
  );
};

export interface HighlightTextProps {
  text: string;
  search: string;
  style: StyleProp<any>;
}

export const HighlightText: React.FC<HighlightTextProps> = ({text, search, style}) => {
  if (!search) {
    return <MSBTextBase style={style} content={text} />;
  }

  const regex = new RegExp(`(${search.replace(/[.*+?^=!:${}()|[\]/\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <MSBTextBase style={style}>
      {parts.map((part, index) =>
        part.toLowerCase() === search.toLowerCase() ? (
          <MSBTextBase key={index} style={[style, {color: 'orange'}]} content={part} />
        ) : (
          <MSBTextBase style={style} key={index} content={part} />
        ),
      )}
    </MSBTextBase>
  );
};

const makeStyle = createMSBStyleSheet(({Typography}) => {
  return {
    containerSearchInput: {
      paddingHorizontal: 16,
      paddingVertical: 10,
    },
    container: {
      alignItems: 'center',
      backgroundColor: '#F7F8F9',
      flexDirection: 'row',
      height: 48,
      paddingHorizontal: 12,
    },
    containerEmpty: {
      alignItems: 'center',
      backgroundColor: '#fff',
      flexDirection: 'column',
      flex: 1,
      justifyContent: 'center',
      width: '100%',
    },
    icon: {
      width: 24,
      height: 24,
      tintColor: '#B0B0B0', // Màu của icon
      marginRight: 10,
    },
    image: {
      height: 144,
      marginBottom: 20,
      width: 144,
    },
    input: {
      color: '#000',
      flex: 1,
      fontSize: 16, // Màu chữ
    },
    textAlign: {
      textAlign: 'center',
    },
  };
});

export default ProviderList;
