import React, {useState} from 'react';
import {View, StyleProp, ScrollView} from 'react-native';
import {
  createMSBStyleSheet,
  MSBFastImage,
  MSBFolderImage,
  MSBSearchInput,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';

// import {ProviderModel} from '../../../data/models/model.ts';
import {FlatList} from 'react-native-gesture-handler';

import {BottomSheetView} from '@gorhom/bottom-sheet';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import DimensionUtils from '../../utils/DimensionUtils';
// import Images from '../../assets/images/Images';
import Utils from '../../utils/Utils';
import {ProviderItem} from './ProviderItem';
import {translate} from '../../locales/i18n';
// import Images from '../../assets/images/Images';

interface ProviderListProps {
  list: ProviderModel[];
  onClick?: (item: ProviderModel) => void;
  defaultValue?: ProviderModel | null;
}

const ProviderList = ({list: list, onClick, defaultValue}: ProviderListProps) => {
  const [searchText, setSearchText] = useState('');
  let content;
  let filteredProviders: ProviderModel[] | null = null;
  if (Utils.isEmpty(list) || list === undefined) {
    content = <EmptyBankSystemErrorScreen />;
  } else {
    filteredProviders = list?.filter(
      provider =>
        provider.description?.toLowerCase().includes((searchText.trim() ?? '').toLowerCase()) ||
        provider.subgroupNameVn?.toLowerCase().includes((searchText.trim() ?? '').toLowerCase()),
    );
  }

  if (filteredProviders == null) {
    content = <EmptyBankSystemErrorScreen />;
  } else if (filteredProviders.length === 0) {
    content = <EmptyBankFilteredScreen />;
  } else {
    content = (
      <FlatList
        data={filteredProviders}
        // renderItem={({ item }) => <BankItem item={item} highlight={searchText} />}
        renderItem={({item, index}) => (
          <ProviderItem
            defaultValue={defaultValue}
            item={item}
            highlight={searchText.trim()}
            onClick={bankItem => {
              onClick && onClick(bankItem);
            }}
            index={index}
          />
        )}
        keyExtractor={item => item.serviceCode ?? ''}
        // contentContainerStyle={styles.listContainer}
      />
    );
  }
  return (
    <BottomSheetView style={{width: '100%', flexDirection: 'column', height: DimensionUtils.getWindowHeight() * 0.8}}>
      <SearchComponent
        onSearch={output => {
          console.log('pdz', output);
          setSearchText(output);
        }}
      />
      {content}
    </BottomSheetView>
  );
};

const SearchComponent = ({onSearch}: {onSearch: (text: string) => void}) => {
  const [searchText, setSearchText] = useState('');
  const {styles, theme} = useMSBStyles(makeStyle);

  const handleTextChange = (text: string) => {
    setSearchText(text);
    onSearch(text);
  };

  return (
    <View>
      <MSBSearchInput
        testID={'transfer.beneficiaryScreen.searchBank'}
        placeholder={translate('components.providerList.searchPlaceholder')}
        value={searchText}
        maxLength={255}
        onChangeText={handleTextChange}
        isInputBottomSheet
        containerSearchStyle={styles.containerSearchInput}
      />
      <View style={{height: 1, backgroundColor: theme.ColorAlias.BorderDefault}} />
    </View>
  );
};

const EmptyBankFilteredScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <ScrollView>
      <View style={styles.containerEmpty}>
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase
          style={theme.Typography?.base_semiBold}
          content={translate('components.providerList.noResultsTitle')}
        />
        <MSBTextBase
          style={theme.Typography?.small_regular}
          content={translate('components.providerList.noResultsMessage')}
        />
      </View>
    </ScrollView>
  );
};

const EmptyBankSystemErrorScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <ScrollView>
      <View style={styles.containerEmpty}>
        <MSBFastImage nameImage={'empty-data'} style={styles.image} folder={MSBFolderImage.IMAGES} />
        <MSBTextBase
          style={theme.Typography?.base_semiBold}
          content={translate('components.providerList.systemErrorTitle')}
        />
        <MSBTextBase
          style={[theme.Typography?.small_regular, styles.textAlign]}
          content={translate('components.providerList.systemErrorMessage')}
        />
      </View>
    </ScrollView>
  );
};

export interface HighlightTextProps {
  text: string;
  search: string;
  style: StyleProp<any>;
}

export const HighlightText: React.FC<HighlightTextProps> = ({text, search, style}) => {
  if (!search) {
    return <MSBTextBase style={style} content={text} />;
  }

  const regex = new RegExp(`(${search.replace(/[.*+?^=!:${}()|[\]/\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <MSBTextBase style={style}>
      {parts.map((part, index) =>
        part.toLowerCase() === search.toLowerCase() ? (
          <MSBTextBase key={index} style={[style, {color: 'orange'}]} content={part} />
        ) : (
          <MSBTextBase style={style} key={index} content={part} />
        ),
      )}
    </MSBTextBase>
  );
};

const makeStyle = createMSBStyleSheet(
  ({Typography, ColorItem, ColorAlias, ColorField, ColorDataView, ColorGlobal, SizeAlias, SizeGlobal}) => {
    return {
      containerSearchInput: {
        paddingHorizontal: SizeAlias.SpacingSmall,
        paddingVertical: SizeAlias.SpacingXMSmall,
      },
      container: {
        alignItems: 'center',
        backgroundColor: ColorGlobal.Neutral100,
        flexDirection: 'row',
        height: SizeGlobal.Size1200,
        paddingHorizontal: SizeAlias.SpacingXSmall,
      },
      containerEmpty: {
        alignItems: 'center',
        backgroundColor: ColorGlobal.NeutralWhite,
        flexDirection: 'column',
        flex: 1,
        justifyContent: 'center',
        width: '100%',
      },
      icon: {
        width: SizeGlobal.Size600,
        height: SizeGlobal.Size600,
        tintColor: ColorField.IconDefault,
        marginRight: SizeAlias.SpacingXMSmall,
      },
      image: {
        height: SizeGlobal.Size2880,
        marginBottom: SizeAlias.SpacingMedium,
        width: SizeGlobal.Size2880,
      },
      input: {
        ...Typography?.base_medium,
        color: ColorDataView.TextMain,
        flex: 1,
      },
      textAlign: {
        textAlign: 'center',
      },
    };
  },
);

export default ProviderList;
