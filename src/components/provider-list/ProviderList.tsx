import React, {useState} from 'react';
import {View, Text, Image, StyleProp, ScrollView} from 'react-native';
import {createMSBStyleSheet, MSBSearchInput, useMSBStyles} from 'msb-shared-component';

// import {ProviderModel} from '../../../data/models/model.ts';
import {FlatList} from 'react-native-gesture-handler';

import {BottomSheetView} from '@gorhom/bottom-sheet';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
import DimensionUtils from '../../utils/DimensionUtils';
import Images from '../../assets/images/Images';
import Utils from '../../utils/Utils';
import {ProviderItem} from './ProviderItem';

interface ProviderListProps {
  list: ProviderModel[];
  onClick?: (item: ProviderModel) => void;
  defaultValue?: ProviderModel | null;
}

const ProviderList = ({list: list, onClick, defaultValue}: ProviderListProps) => {
  const [searchText, setSearchText] = useState('');
  let content;
  let filteredProviders: ProviderModel[] | null = null;
  if (Utils.isEmpty(list) || list === undefined) {
    content = <EmptyBankSystemErrorScreen />;
  } else {
    filteredProviders = list?.filter(
      provider =>
        provider.description?.toLowerCase().includes((searchText.trim() ?? '').toLowerCase()) ||
        provider.subgroupNameVn?.toLowerCase().includes((searchText.trim() ?? '').toLowerCase()),
    );
  }

  if (filteredProviders == null) {
    content = <EmptyBankSystemErrorScreen />;
  } else if (filteredProviders.length === 0) {
    content = <EmptyBankFilteredScreen />;
  } else {
    content = (
      <FlatList
        data={filteredProviders}
        // renderItem={({ item }) => <BankItem item={item} highlight={searchText} />}
        renderItem={({item, index}) => (
          <ProviderItem
            defaultValue={defaultValue}
            item={item}
            highlight={searchText.trim()}
            onClick={bankItem => {
              onClick && onClick(bankItem);
            }}
            index={index}
          />
        )}
        keyExtractor={item => item.serviceCode ?? ''}
        // contentContainerStyle={styles.listContainer}
      />
    );
  }
  return (
    <BottomSheetView style={{width: '100%', flexDirection: 'column', height: DimensionUtils.getWindowHeight() * 0.8}}>
      <SearchComponent
        onSearch={output => {
          console.log('pdz', output);
          setSearchText(output);
        }}
      />
      {content}
    </BottomSheetView>
  );
};

const SearchComponent = ({onSearch}: {onSearch: (text: string) => void}) => {
  const [searchText, setSearchText] = useState('');
  const {styles, theme} = useMSBStyles(makeStyle);

  const handleTextChange = (text: string) => {
    setSearchText(text);
    onSearch(text);
  };

  return (
    <View>
      <MSBSearchInput
        testID={'transfer.beneficiaryScreen.searchBank'}
        placeholder={'Nhập nội dung'}
        value={searchText}
        maxLength={255}
        onChangeText={handleTextChange}
        isInputBottomSheet
        containerSearchStyle={styles.containerSearchInput}
      />
      <View style={{height: 1, backgroundColor: theme.ColorAlias.BorderDefault}} />
    </View>
  );
};

const EmptyBankFilteredScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <ScrollView>
      <View style={styles.containerEmpty}>
        <Image style={styles.image} source={Images.icEmptyBankFiltered} />
        <Text style={theme.Typography?.base_semiBold}>{'Không tìm thấy kết quả'}</Text>
        <Text style={theme.Typography?.small_regular}>{'Vui lòng tìm với từ khoá khác'}</Text>
      </View>
    </ScrollView>
  );
};

const EmptyBankSystemErrorScreen = () => {
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <ScrollView>
      <View style={styles.containerEmpty}>
        <Image style={styles.image} source={Images.icEmptyBillSystemError} />
        <Text style={theme.Typography?.base_semiBold}>{'Hệ thống gián đoạn'}</Text>
        <Text style={[theme.Typography?.small_regular, styles.textAlign]}>
          {'Quý khách vui lòng thử lại. Xin lỗi vì sự bất tiện này.'}
        </Text>
      </View>
    </ScrollView>
  );
};

export interface HighlightTextProps {
  text: string;
  search: string;
  style: StyleProp<any>;
}

export const HighlightText: React.FC<HighlightTextProps> = ({text, search, style}) => {
  if (!search) {
    return <Text style={style}>{text}</Text>;
  }

  const regex = new RegExp(`(${search.replace(/[.*+?^=!:${}()|[\]/\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <Text style={style}>
      {parts.map((part, index) =>
        part.toLowerCase() === search.toLowerCase() ? (
          <Text key={index} style={[style, {color: 'orange'}]}>
            {part}
          </Text>
        ) : (
          <Text style={style} key={index}>
            {part}
          </Text>
        ),
      )}
    </Text>
  );
};

const makeStyle = createMSBStyleSheet(({Typography}) => {
  return {
    containerSearchInput: {
      paddingHorizontal: 16,
      paddingVertical: 10,
    },
    container: {
      alignItems: 'center',
      backgroundColor: '#F7F8F9',
      flexDirection: 'row',
      height: 48,
      paddingHorizontal: 12,
    },
    containerEmpty: {
      alignItems: 'center',
      backgroundColor: '#fff',
      flexDirection: 'column',
      flex: 1,
      justifyContent: 'center',
      width: '100%',
    },
    icon: {
      width: 24,
      height: 24,
      tintColor: '#B0B0B0', // Màu của icon
      marginRight: 10,
    },
    image: {
      height: 144,
      marginBottom: 20,
      width: 144,
    },
    input: {
      color: '#000',
      flex: 1,
      fontSize: 16, // Màu chữ
    },
    textAlign: {
      textAlign: 'center',
    },
  };
});

export default ProviderList;
