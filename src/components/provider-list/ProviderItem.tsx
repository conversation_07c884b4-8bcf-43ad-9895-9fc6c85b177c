import {
  MSBTouchable,
  MSBIcon,
  ColorItem,
  MSBIcons,
  SizeGlobal,
  MSBFolderImage,
  MSBIconSize,
  MSBFastImage,
  createMSBStyleSheet,
  useMSBStyles,
  getSize,
} from 'msb-shared-component';
import {View} from 'react-native';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
// import Utils from '../../utils/Utils';
import HighlightText from '../highlight-text';
import React from 'react';

export interface ProviderItemProps {
  item: ProviderModel;
  highlight: string;
  onClick?: (bankItem: ProviderModel) => void;
  defaultValue?: ProviderModel | null | undefined;
  index?: number;
}

// Hàm renderItem độc lập
export const ProviderItem: React.FC<ProviderItemProps> = ({item, highlight, onClick, defaultValue, index}) => {
  const isTopup = item.isTopup();
  const {styles, theme} = useMSBStyles(makeStyle);
  return (
    <MSBTouchable
      testID={`transfer.beneficiaryScreen.pressBank.${index}}`}
      style={styles.itemContainerV1}
      onPress={() => onClick && onClick(item)}>
      <View style={styles.itemContainerV2}>
        {true ? (
          // <Image source={Utils.getProviderIcon(item.getIconName())} style={styles.logo} />
          // <MSBIcon icon="74" folderIcon={MSBFolderImage.LOGO_TOPUP} iconSize={MSBIconSize.SIZE_24} />
          // <MSBFastImage folder={MSBFolderImage.LOGO_TOPUP} source={74} style={styles.logo} />
          <MSBIcon
            folderIcon={isTopup ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
            icon={item.subGroupId?.toString() ?? ''}
            iconSize={MSBIconSize.SIZE_40}
            styleContainer={styles.logo}
          />
        ) : (
          // <Image source={Utils.getDefaultIcon(item.categoryCode ?? '')} style={styles.logo} />
          <MSBFastImage nameImage={'tone-bill'} style={styles.logo} folder={MSBFolderImage.ICON_SVG} />
        )}
        <View style={styles.textContainer}>
          <View style={styles.shortNameCitad}>
            <HighlightText style={styles.shortName} text={item.getName()} search={highlight} />
            <View style={{width: 10}} />
          </View>
          {/* <HighlightText style={styles.fullName} text={item.description || ''} search={highlight} /> */}
          {/*<Text style={styles.shortName}>{item.shortName}</Text>*/}
          {/*<Text style={styles.fullName}>{item.fullName}</Text>*/}
        </View>
        {item?.serviceCode === defaultValue?.serviceCode && (
          <MSBIcon iconColor={ColorItem.IconBrand} icon={MSBIcons.IconCheck} iconSize={SizeGlobal.Size800} />
        )}
      </View>
      <View style={styles.line} />
    </MSBTouchable>
  );
};

const makeStyle = createMSBStyleSheet(({Typography, SizeAlias, SizeGlobal, ColorDataView, ColorAlias, ColorGlobal}) => {
  return {
    fullName: {
      ...Typography?.base_regular,
      color: ColorDataView.TextSub,
    },
    itemContainerV1: {
      alignItems: 'center',
      flexDirection: 'column',
      paddingHorizontal: SizeAlias.SpacingXSmall,
    },
    itemContainerV2: {
      alignItems: 'center',
      flexDirection: 'row',
      height: getSize(72),
      padding: SizeAlias.Spacing4xSmall,
    },
    line: {
      backgroundColor: ColorGlobal.Neutral100,
      height: 1,
      paddingHorizontal: SizeAlias.SpacingXSmall,
      width: '100%',
    },
    listContainer: {
      backgroundColor: ColorGlobal.NeutralWhite,
      padding: 0,
    },
    logo: {
      height: SizeGlobal.Size800,
      marginRight: SizeAlias.SpacingSmall,
      resizeMode: 'contain',
      width: SizeGlobal.Size800,
    },
    shortName: {
      color: ColorAlias.TextInformation,
      ...Typography?.base_semiBold,
    },
    shortNameCitad: {
      alignItems: 'center',
      flexDirection: 'row',
    },
    status: {
      // color: ColorDataView '#007b00',
      color: ColorAlias.TextSuccess,
      ...Typography?.caption_regular,
      marginTop: SizeAlias.Spacing4xSmall,
    },
    textContainer: {
      flex: 1,
    },
  };
});
