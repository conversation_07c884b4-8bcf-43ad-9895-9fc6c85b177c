import {
  MSBTouchable,
  MSBIcon,
  ColorItem,
  MSBIcons,
  SizeGlobal,
  MSBFolderImage,
  MSBIconSize,
  MSBFastImage,
} from 'msb-shared-component';
import {View, StyleSheet} from 'react-native';
import {ProviderModel} from '../../domain/entities/provider-list/ProviderListModel';
// import Utils from '../../utils/Utils';
import HighlightText from '../highlight-text';
import React from 'react';

export interface ProviderItemProps {
  item: ProviderModel;
  highlight: string;
  onClick?: (bankItem: ProviderModel) => void;
  defaultValue?: ProviderModel | null | undefined;
  index?: number;
}

// Hàm renderItem độc lập
export const ProviderItem: React.FC<ProviderItemProps> = ({item, highlight, onClick, defaultValue, index}) => {
  const isTopup = item.isTopup();
  return (
    <MSBTouchable
      testID={`transfer.beneficiaryScreen.pressBank.${index}}`}
      style={styles.itemContainerV1}
      onPress={() => onClick && onClick(item)}>
      <View style={styles.itemContainerV2}>
        {true ? (
          // <Image source={Utils.getProviderIcon(item.getIconName())} style={styles.logo} />
          // <MSBIcon icon="74" folderIcon={MSBFolderImage.LOGO_TOPUP} iconSize={MSBIconSize.SIZE_24} />
          // <MSBFastImage folder={MSBFolderImage.LOGO_TOPUP} source={74} style={styles.logo} />
          <MSBIcon
            folderIcon={isTopup ? MSBFolderImage.LOGO_TOPUP : MSBFolderImage.LOGO_BILLING}
            icon={item.subGroupId?.toString() ?? ''}
            iconSize={MSBIconSize.SIZE_40}
            styleContainer={styles.logo}
          />
        ) : (
          // <Image source={Utils.getDefaultIcon(item.categoryCode ?? '')} style={styles.logo} />
          <MSBFastImage nameImage={'tone-bill'} style={styles.logo} folder={MSBFolderImage.ICON_SVG} />
        )}
        <View style={styles.textContainer}>
          <View style={styles.shortNameCitad}>
            <HighlightText style={styles.shortName} text={item.getName()} search={highlight} />
            <View style={{width: 10}} />
          </View>
          {/* <HighlightText style={styles.fullName} text={item.description || ''} search={highlight} /> */}
          {/*<Text style={styles.shortName}>{item.shortName}</Text>*/}
          {/*<Text style={styles.fullName}>{item.fullName}</Text>*/}
        </View>
        {item?.serviceCode === defaultValue?.serviceCode && (
          <MSBIcon iconColor={ColorItem.IconBrand} icon={MSBIcons.IconCheck} iconSize={SizeGlobal.Size800} />
        )}
      </View>
      <View style={styles.line} />
    </MSBTouchable>
  );
};

const styles = StyleSheet.create({
  fullName: {
    color: '#6B788E',
    fontSize: 14,
    fontWeight: '400',
  },
  itemContainerV1: {
    alignItems: 'center',
    flexDirection: 'column',
    paddingHorizontal: 12,
  },
  itemContainerV2: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 72,
    padding: 5,
  },
  line: {
    backgroundColor: '#EBEDF0',
    height: 1,
    paddingHorizontal: 12,
    width: '100%',
  },
  listContainer: {
    backgroundColor: '#ffffff',
    padding: 0,
  },
  logo: {
    height: 32,
    marginRight: 16,
    resizeMode: 'contain',
    width: 32,
  },
  shortName: {
    color: '#091E42',
    fontSize: 16,
    fontWeight: '500',
  },
  shortNameCitad: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  status: {
    color: '#007b00',
    fontSize: 12,
    marginTop: 4,
  },
  textContainer: {
    flex: 1,
  },
});
