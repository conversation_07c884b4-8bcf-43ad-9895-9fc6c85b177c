// for test

import React from 'react';
import {View, Text} from 'react-native';
import {ColorDataView, createMSBStyleSheet, useMSBStyles} from 'msb-shared-component';

import {BillItemProps} from './types';

const BillItemInfo = ({style, title, value}: BillItemProps) => {
  const {styles} = useMSBStyles(makeStyle);

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.txtTitle}>{title}</Text>
      <Text style={styles.txtValue}>{value}</Text>
    </View>
  );
};

export default BillItemInfo;
const makeStyle = createMSBStyleSheet(({Typography}) => {
  return {
    container: {},
    txtTitle: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    txtValue: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
      marginTop: 4,
    },
  };
});
