// for test

import React from 'react';
import {View} from 'react-native';
import {ColorDataView, createMSBStyleSheet, MSBTextBase, useMSBStyles} from 'msb-shared-component';

import {BillItemProps} from './types';

const BillItemInfo = ({style, title, value}: BillItemProps) => {
  const {styles} = useMSBStyles(makeStyle);

  return (
    <View style={[styles.container, style]}>
      <MSBTextBase style={styles.txtTitle} content={title} />
      <MSBTextBase style={styles.txtValue} content={value} />
    </View>
  );
};

export default BillItemInfo;
const makeStyle = createMSBStyleSheet(({Typography}) => {
  return {
    container: {},
    txtTitle: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    txtValue: {
      ...Typography?.base_medium,
      color: ColorDataView.TextMain,
      marginTop: 4,
    },
  };
});
