# Payment Module(Remote app)

## Quản lý phiên bản các dependency của remote app

- Khi có yêu cầu nâng cấp hay `thêm/sửa/xoá` phiên bản của dependency để sử dụng cần yêu cầu để cập nhật các metadata ở [`msb-digibank-sdk`](https://gitlab-dso.msb.com.vn/digital-banking-platform/ibretail/mobile/super-app/library/msb-digibank-sdk)
- Sau khi `msb-digibank-sdk` cập nhật các metadata mới cho các dependency, hãy cập nhật phiên bản của nó

  ```sh
  yarn msb-digibank-sdk@new-version -D
  ```

- <PERSON><PERSON> kiểm tra các phiên bản hiện tại đang sử dụng có đúng với yêu cầu hay không.

  ```sh
  yarn check-deps
  ```

  Nếu có bất kì nào không phù hợp, sẽ nhận được thông báo cần chỉnh sửa version cho phù hợp. VD

  ```
  error package.json: Changes are needed to satisfy all capabilities.
          In dependencies:
          - i18n-js "3.9.3" should be "3.9.2"
  error Re-run with '--write' to fix them.
  ```

- Nếu muốn áp dụng các thay đổi được đề xuất từ `check-deps` thay vì phải sửa thủ công, hãy sử dụng

  ```sh
  yarn align-deps
  ```

## Lỗi và cách sửa lỗi

1. `Cannot read property ‘createTranslationOptions’ of undefined`

   Nguyên nhân: Do import trực tiếp hàm `translate()` thẳng từ module `i18n`
   Giải pháp: Sử dụng hàm đã được định nghĩa ở trong folder `locales` của project

   ```jsx
   import {translate} from '@locales';
   ```
