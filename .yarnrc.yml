compressionLevel: mixed

enableGlobalCache: false

nodeLinker: node-modules

npmRegistryServer: 'https://nexus-dso.msb.com.vn/repository/npm-group'
npmAlwaysAuth: true
npmAuthIdent: 'YnBtLXJvOjAwM2FjODY0ZDY4YWU3ZWY0'

npmScopes:
  npm-group-dbp:
    npmRegistryServer: 'https://nexus-dbp-nonprod.msb.com.vn/repository/npm-group/'
  npm-group-msb:
    npmRegistryServer: 'https://nexus-dso.msb.com.vn/repository/npm-group/'
  npm-snapshot-msb:
    npmRegistryServer: 'https://nexus-dso.msb.com.vn/repository/npm-snapshot-msb/'
